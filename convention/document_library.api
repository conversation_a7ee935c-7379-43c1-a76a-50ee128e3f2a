syntax = "v1"


type ImportDocumentLibraryReq {
  MainFileID string `json:"mainFileId"`
  ListFileIDs []string `json:"listFileIds,optional"`
  TypeDictionaryID string `json:"typeDictionaryId"` // 类型字典id
  DomainDictionaryID string `json:"domainDictionaryId,optional"` // 领域字典id
  AuthDictionaryID string `json:"authDictionaryId,optional"` // 认证字典id
  ModuleType int `json:"moduleType"` // 模块类型 1-书籍库 2-内部库 3-外部库
}

type ImportDocumentLibraryResp {

}



type ExportDocumentLibraryReq {
  ModuleType int `json:"moduleType"` // 模块类型 1-书籍库 2-内部库 3-外部库
  Params map[string]interface{} `json:"params"` // 参数
}

type ExportDocumentLibraryResp {

}

type GetDocPermissionUsersReq {
  FileID string `form:"fileId"`
  FileForm int32 `form:"fileForm"`
  FilePermission int32 `form:"filePermission"`
}

type GetDocPermissionUsersResp {
  OrganizationUserTree *OrganizationUserTree `json:"organizationUserInfo"`
  Users []OrganizationUsers `json:"users"`
}

type OrganizationUserTree {
  ParentId string `json:"parentId"`
  OrgID string `json:"orgId"`
  OrgName string `json:"orgName"`
  OrganizationUsers []OrganizationUsers `json:"userInfo"`
  Children []*OrganizationUserTree `json:"children"`
}

type OrganizationUsers {
  UserID string `json:"userId"`
  Nickname string `json:"nickname"`
  Status int `json:"status"`
}

type GetDistributeListReq {
  PageInfo
  FileNumber string `json:"fileNumber,optional"` // 文件编号
  FileName string `json:"fileName,optional"` // 文件名称
  FileType int `json:"fileType,optional"` // 文件类型
  FileCategory []string `json:"fileCategory,optional"` // 文件类别
  DistributeType int `json:"distributeType,optional"` // 发放类型
  Status int `json:"status,optional"` // 状态，1待提交 | 2待审批 | 3已审批 | 4已驳回
  Applicant string `json:"applicant,optional"` // 申请人
}

type GetDistributeListResp {
  Total int64 `json:"total"`
  Data []GetDistributeListInfo `json:"data"`
}

type GetDistributeListInfo {
  ID string `json:"id"` // 主键id
  Applicant string `json:"applicant"` // 申请人
  ApplyDate int64 `json:"applyDate"` // 申请时间
  DistributeType int `json:"distributeType"` // 发放类型，1内部发放 | 2外部发放
  FileType int `json:"fileType"` // 文件类型，1内部文件 | 2外部文件
  FileCategory string `json:"fileCategory"` // 文件类别
  Reason string `json:"reason"` // 发放原因
  OtherReason string `json:"otherReason"` // 其他原因
  WishDistributeDate int64 `json:"wishDistributeDate"` // 期望发放时间
  Status int `json:"status"` // 状态，1待提交 | 2待审批 | 3已审批 | 4已驳回
  WorkflowID string `json:"workflowId"` // 工作流id
  ApprovalInfo ApprovalInfo `json:"approver"` // 审批人
  DistributeCount int `json:"distributeCount"` // 发放份数
  ReceivedCount int `json:"receivedCount"` // 签收份数
  DisposalCount int `json:"disposalCount"` // 处置份数
  RecycleStatus string `json:"recycleStatus"` // 回收状态
  DistributeUsers []DistributeUser `json:"distributeUsers"` // 发放用户
}

type DistributeUser {
  UserID string `json:"userId"`       // 用户id
  FileForm int32 `json:"fileForm"`    // 文件形式（1电子文件 | 2纸质文件）
  Nickname string `json:"nickname"`    // 用户昵称
  Status int `json:"status"`      // 状态（1已签收 | 2未签收 | 3已回收 | 4已处置）
}

type GetDistributeInventoryReq {
  ID string `form:"id"`
}

type GetDistributeInventoryResp {
  DisposalStatus string `json:"disposalStatus"` // 处置状态
  Data []DistributeInventory `json:"data"` // 发放清单
}

type DistributeInventory {
  ID string `json:"id"` // 发放清单id
  FileId string `json:"fileId"` // 文档id
  FileName string `json:"fileName"` // 文档名称
  Number string `json:"number"` // 文档编号
  Version string `json:"version"` // 文档版本
  Recipient string `json:"recipient"` // 接收方
  EFileLook PermissionResp `json:"eFileLook"` // 电子文件查阅权限
  EFileLookAndDownload PermissionResp `json:"eFileLookAndDownload"` // 电子文件查阅和下载权限
  PaperDocumentOnceDownload PermissionResp `json:"paperDocumentOnceDownload"` // 纸质文件一次下载权限
  EFileOnceDownload PermissionResp `json:"eFileOnceDownload"` // 电子文件一次下载权限
}

type PermissionResp {
  FileForm int32 `json:"fileForm"` // 文件形式（1电子文件 | 2纸质文件）
  FilePermission int32 `json:"filePermission"` // 文件权限（1查阅 | 2查阅/下载 | 3一次下载）
  DistributeCount int `json:"distributeCount"` // 发放份数
  RecycleCount int `json:"recycleCount"` // 回收份数
  DisposalCount int `json:"disposalCount"` // 处置份数
  ReceivedBy []ReceivedBy `json:"receivedBy"` // 接收人
}

type ReceivedBy {
  UserID string `json:"userId"` // 用户id
  Nickname string `json:"nickname"` // 用户昵称
  Status int `json:"status"` // 状态（1未回收 | 2回收审批中 | 3已回收 | 4处置审批中 | 5已处置）
  RecycleDate int64 `json:"recycleDate"` // 回收时间
}

type DeleteDistributeReq {
  ID string `form:"id"` // 发放列表id
}

type DeleteDistributeResp {
}

// 获取发放详情请求
type GetDistributeDetailReq {
  ID string `form:"id"` // 发放记录ID
}

// 获取发放详情响应
type GetDistributeDetailResp {
   // 基本信息
  ID string `json:"id"` // 发放记录ID
  WorkflowID string `json:"workflowId"` // 流程ID
  Applicant string `json:"applicant"` // 申请人ID
  ApplicantName string `json:"applicantName"` // 申请人姓名
  ApplyDate int64 `json:"applyDate"` // 申请日期

  // 类型信息
  DistributeType int32 `json:"distributeType"` // 发放类型（1内部发放 | 2外部发放）
  FileType int32 `json:"fileType"` // 文件类型（1内部文件 | 2外部文件）
  FileCategory string `json:"fileCategory"` // 文件类别
  TypeDictNodeID string `json:"typeDictNodeId"` // 类型字典节点ID

  // 原因信息
  Reason string `json:"reason"` // 发放原因
  OtherReason string `json:"otherReason"` // 其他原因

  // 日期和状态
  WishDistributeDate int64 `json:"wishDistributeDate"` // 期望发放日期
  Status int32 `json:"status"` // 状态（1待提交 | 2待审批 | 3已审批 | 4已驳回）

  // 发放清单
  DistributeList []DistributeInventoryDetail `json:"distributeList"` // 发放清单

  // 时间戳
  CreatedAt int64 `json:"createdAt"` // 创建时间
  UpdatedAt int64 `json:"updatedAt"` // 更新时间
}


// 发放清单详情
type DistributeInventoryDetail {
  ID string `json:"id"` // 发放清单ID
  FileID string `json:"fileId"` // 文件ID
  FileName string `json:"fileName"` // 文件名称
  Number string `json:"number"` // 文件编号
  Version string `json:"version"` // 文件版本
  Permissions []PermissionDetail `json:"permissions"` // 权限详情列表
}

// 权限详情信息
type PermissionDetail {
  FileForm int32 `json:"fileForm"` // 文件形式（1电子文件 | 2纸质文件）
  FilePermission int32 `json:"filePermission"` // 文件权限（1查询 | 2查询/下载 | 3一次下载）
  Recipient string `json:"recipient"` // 接收方
  ReceivedBy []DistributeUserDetail `json:"receivedBy"` // 接收人详情列表
}

// 用户详情信息
type DistributeUserDetail {
  UserID string `json:"userId"` // 用户ID
  UserNickname string `json:"userNickname"` // 用户姓名
  RecycleStatus int32 `json:"recycleStatus"` // 回收状态（0未回收 | 1已回收 | 2待审批）
  RecycleTime int64 `json:"recycleTime"` // 回收时间（Unix时间戳）
}

// 根据发放清单ID查询回收信息请求
type GetRecycleInfoByDistributeIdReq {
  DistributeListID string `form:"distributeListId"` // 发放清单ID
}

// 根据发放清单ID查询回收信息响应
type GetRecycleInfoByDistributeIdResp {
   FileName string `json:"fileName"` // 文件名称
  FileNumber string `json:"fileNumber"` // 文件编号
  RecycleRecords []RecycleRecord `json:"recycleRecords"` // 回收记录列表
}


// 回收记录
type RecycleRecord {
  RecycleInitiator string `json:"recycleInitiator"` // 回收发起人
  RecycleReason string `json:"recycleReason"` // 回收原因
  HandoverPersons []HandoverPerson `json:"handoverPersons"` // 交还人信息列表
  Auditors []string `json:"auditors"` // 审批人列表
  Approvers []string `json:"approvers"` // 批准人列表
  RecycleDate int64 `json:"recycleDate"` // 回收日期（毫秒级时间戳）
}

// 交还人信息
type HandoverPerson {
  HandoverID string `json:"handoverId"` // 交还人ID
  HandoverName string `json:"handoverName"` // 交还人名字
  FileForm int32 `json:"fileForm"` // 文件形式（1电子文件 | 2纸质文件）
  FilePermission int32 `json:"filePermission"` // 文件权限（1查询 | 2查询/下载 | 3一次下载）
}

// ==================== 借阅记录相关类型定义 ====================
// 新增借阅记录请求
type AddLoanReq {
  BorrowTime int64 `json:"borrowTime"` // 借阅时间
  DueTime int64 `json:"dueTime"` // 归还时间
  BorrowReason string `json:"borrowReason"` // 借阅原因
  Documents []LoanDocument `json:"documents"` // 文档列表
}

// 借阅文档信息
type LoanDocument {
  DocumentId string `json:"documentId"` // 文档id
  DocumentName string `json:"documentName,optional"` // 文档名称
  DocumentValidity int32 `json:"documentValidity,optional"` // 文档有效性
  DocumentModuleType int32 `json:"documentModuleType"` // 文档模块：1书籍，2内部文档，3外部文档
  DocumentModuleName string `json:"documentModuleName,optional"` // 文档模块名称
  DocumentCategoryName string `json:"documentCategoryName,optional"` // 文档类别名称
  DocumentNo string `json:"documentNo,optional"` // 文档编号
  DocumentVersionNo string `json:"documentVersionNo"` // 文档版次号
  BorrowStatus int32 `json:"borrowStatus,optional"` // 借阅状态
}


// 新增借阅记录响应
type AddLoanResp {
}

// 更新借阅记录请求
type UpdateLoanReq {
  Id string `json:"id"` // 借阅申请id
  BorrowTime int64 `json:"borrowTime"` // 借阅时间
  DueTime int64 `json:"dueTime"` // 归还时间
  BorrowReason string `json:"borrowReason"` // 借阅原因
  Documents []LoanDocument `json:"documents"` // 文档列表
}

// 更新借阅记录响应
type UpdateLoanResp {
}

// 查询借阅记录请求
type GetLoanRecordsReq {
  PageInfo
  DocumentNo string `form:"documentNo,optional"` // 文档编号
  DocumentName string `form:"documentName,optional"` // 文档名称
  DocumentModule string `form:"documentModule,optional"` // 文档模块
  DocumentCategoryId string `form:"documentCategoryId,optional"` // 文档类别ID
  UserNickname string `form:"userNickname,optional"` // 借阅人名称
  ApprovalStatus string `form:"approvalStatus,optional"` // 审批状态
}

// 借阅记录信息
type LoanRecord {
  Id string `json:"id"` // 借阅记录ID
  BorrowTime int64 `json:"borrowTime"` // 借阅时间
  DueTime int64 `json:"dueTime"` // 归还时间
  BorrowReason string `json:"borrowReason"` // 借阅原因
  UserNickname string `json:"userNickname"` // 借阅人姓名
  ReviewerName string `json:"reviewerName"` // 审核人姓名
  ApproverName string `json:"approverName"` // 批准人姓名
  RecoverNames string `json:"recoverNames"` // 回收人姓名
  RecoverDocumentsCount int `json:"recoverDocumentsCount"` // 已回收文档数量
  BorrowDocumentsCount int `json:"borrowDocumentsCount"` // 借阅文档数量
  ApprovalStatus int `json:"approvalStatus"` // 审批状态：1-待提交，2-待审批，3-已驳回，4-已审批
  ApprovalApplyTime int64 `json:"approvalApplyTime"` // 申请时间
}

// 查询借阅记录响应
type GetLoanRecordsResp {
  Total int64 `json:"total"` // 总数
  Data []LoanRecord `json:"data"` // 借阅记录列表
}

// 查询借阅记录文档清单请求
type GetLoanRecordDocumentsReq {
  Id string `form:"id"` // 借阅记录id
}

// 查询借阅记录文档清单响应
type GetLoanRecordDocumentsResp {
  Data []LoanDocument `json:"data"` // 文档清单
}

// 删除借阅记录请求
type DeleteLoanReq {
  BorrowRecordId string `json:"borrowRecordId"` // 借阅记录ID
}

// 删除借阅记录响应
type DeleteLoanResp {
}


// 查询处置详情请求
type GetDisposalDetailReq {
  DistributeListID string `form:"distributeListId"` // 发放清单ID
}

// 查询处置详情响应
type GetDisposalDetailResp {
  FileName string `json:"fileName"` // 文件名称
  FileNumber string `json:"fileNumber"` // 文件编号
  DisposalRecords []DisposalRecord `json:"disposalRecords"` // 处置记录列表
}

// 处置记录
type DisposalRecord {
  HandoverPerson string `json:"handoverPerson"` // 交还人
  HandoverDate int64 `json:"handoverDate"` // 交还日期
  RecyclePerson string `json:"recyclePerson"` // 回收人
  RecycleDate int64 `json:"recycleDate"` // 回收日期
  DisposalPerson string `json:"disposalPerson"` // 处置人
  DisposalDate int64 `json:"disposalDate"` // 处置日期
  DisposalMethod string `json:"disposalMethod"` // 处置方式
}
// ==================== 根据文档ID查询发放回收用户记录相关类型定义 ====================

// 根据文档ID查询发放回收用户记录请求
type GetDistributeUserPermissionsReq {
  DocumentId  string `form:"documentId"` // 发放记录ID
  UserID string `form:"userId,optional"` // 用户ID
  Status int `form:"status,optional"` // 状态 1发放审批中 | 2已发放 | 3回收审批中
}

// 根据文档ID查询发放回收用户记录响应
type GetDistributeUserPermissionsResp {
  List []DistributeUserPermission `json:"list"` // 用户权限记录列表
}

// 用户权限记录
type DistributeUserPermission {
  InventoryId string `json:"inventoryId"` // 发放清单ID
  FileForm int32 `json:"fileForm"` // 文件形式,1电子文件 | 2纸质文件
  FilePermission int32 `json:"filePermission"` // 文件权限,1查阅 | 2查阅/下载 | 3一次下载
  UserId string `json:"userId"` // 用户ID
  UserNickName string `json:"userNickName"` // 用户昵称
  Status int `json:"status"` // 状态 1发放审批中 | 2已发放 | 3回收审批中
}
// ==================== 权限操作相关类型定义 ====================

// 权限操作请求
type PermissionOperationReq {
  DocumentID string `json:"documentId"` // 文档id
  FileForm int64 `json:"fileForm"` // 文件形式,1电子文件 | 2纸质文件
  FilePermission int64 `json:"filePermission"` // 文件权限,1查阅 | 2查阅/下载 | 3一次下载
  InventoryID string `json:"inventoryId"` // 清单id
  OperationType int64 `json:"operationType"` // 操作类型 1查阅 2下载
}

// 权限操作响应
type PermissionOperationResp {
  FileID string `json:"fileId"` // 文件id，预览地址，仅借阅查询时生效
  PreviewURL string `json:"previewUrl,omitempty"` // 预览地址，预览地址，仅借阅查询时生效
}

// ==================== 5.6.2 发放回收记录相关接口定义 ====================

// 获取文档发放回收记录请求
type GetDocumentDistributeRecordsReq {
  DocumentID string `form:"documentId"` // 文档ID
  PageInfo
}

// 获取文档发放回收记录响应
type GetDocumentDistributeRecordsResp {
  Data []DocumentDistributeRecord `json:"data"` // 发放回收记录列表
  PageInfo
}

// 文档发放回收记录
type DocumentDistributeRecord {
  RecordID string `json:"recordId"` // 记录ID
  WorkflowID string `json:"workflowId"` // 流程ID
  ApplyTime int64 `json:"applyTime"` // 申请时间（发起审批的时间，YYYY-MM-DD HH:MM:SS）
  InternalElectronicQuery string `json:"internalElectronicQuery"` // 内发：电子文件-查询权限变更记录
  InternalElectronicDownload string `json:"internalElectronicDownload"` // 内发：电子文件-查询/下载权限变更记录
  Applicant string `json:"applicant"` // 变更人ID
  ApplicantName string `json:"applicantName"` // 变更人姓名
}

// ==================== 5.6.3 内发纸质文件一次下载变更记录相关接口定义 ====================

// 获取内发纸质文件一次下载变更记录请求
type GetInternalPaperDownloadRecordsReq {
  DocumentID string `form:"documentId"` // 文档ID
  PageInfo
}

// 获取内发纸质文件一次下载变更记录响应
type GetInternalPaperDownloadRecordsResp {
  Data []InternalPaperDownloadRecord `json:"data"` // 内发纸质文件下载记录列表
  PageInfo
}

// 内发纸质文件一次下载变更记录
type InternalPaperDownloadRecord {
  RecordID string `json:"recordId"` // 记录ID
  WorkflowID string `json:"workflowId"` // 流程ID
  RecipientUserID string `json:"recipientUserId"` // 纸质文件接收人ID
  RecipientUserName string `json:"recipientUserName"` // 纸质文件接收人姓名
  PaperFileStatus int32 `json:"paperFileStatus"` // 纸质文件状态（1未回收 | 2回收中 | 3已回收）
  FileDisposeStatus int32 `json:"fileDisposeStatus"` // 文件处置状态（1未处置 | 2处置中 | 3已处置）
  DisposalMethod string `json:"disposalMethod"` // 处置方法（已处置时显示）
  DistributeApplicant string `json:"distributeApplicant"` // 发放人ID
  DistributeApplicantName string `json:"distributeApplicantName"` // 发放人姓名
  DistributeApplyTime int64 `json:"distributeApplyTime"` // 发放申请时间
  RecycleApplicant string `json:"recycleApplicant"` // 回收人ID
  RecycleApplicantName string `json:"recycleApplicantName"` // 回收人姓名
  RecycleApplyTime int64 `json:"recycleApplyTime"` // 回收申请时间
  InventoryID string `json:"inventoryId"` // 清单ID（发放记录文件ID，用于发起处置申请）
}


// ==================== 5.6.4 外发电子文件一次下载变更记录相关接口定义 ====================

// 获取外发电子文件一次下载变更记录请求
type GetExternalElectronicDownloadRecordsReq {
  DocumentID string `form:"documentId"` // 文档ID
  PageInfo
}

// 获取外发电子文件一次下载变更记录响应
type GetExternalElectronicDownloadRecordsResp {
  Data []ExternalElectronicDownloadRecord `json:"data"` // 外发电子文件下载记录列表
  PageInfo
}

// 外发电子文件一次下载变更记录
type ExternalElectronicDownloadRecord {
  RecordID string `json:"recordId"` // 记录ID
  WorkflowID string `json:"workflowId"` // 流程ID
  RecipientUserID string `json:"recipientUserId"` // 电子文件接收人ID
  RecipientUserName string `json:"recipientUserName"` // 电子文件接收人姓名
  DistributeApplicant string `json:"distributeApplicant"` // 发放人ID
  DistributeApplicantName string `json:"distributeApplicantName"` // 发放人姓名
  DistributeApplyTime int64 `json:"distributeApplyTime"` // 发放申请时间
  IsDownloaded bool `json:"isDownloaded"` // 是否下载（false未下载 | true已下载）
  InventoryID string `json:"inventoryId"` // 清单ID（发放记录文件ID，用于发起处置申请）
}


