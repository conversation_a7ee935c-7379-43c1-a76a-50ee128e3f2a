package main

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strings"
)

// MapperFile 表示一个mapper文件的信息
type MapperFile struct {
	Path    string
	Content string
	Updated bool
}

// UpdateResult 更新结果
type UpdateResult struct {
	TotalFiles   int
	UpdatedFiles int
	MethodsFixed int
	Errors       []string
}

func main() {
	mapperDir := "/Users/<USER>/Documents/WorkSpace/Go/nebula/internal/infrastructure/adapter/mapper"

	result := UpdateResult{}

	// 遍历mapper目录下的所有.go文件
	err := filepath.Walk(mapperDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理.go文件，排除测试文件
		if !strings.HasSuffix(path, ".go") || strings.HasSuffix(path, "_test.go") {
			return nil
		}

		result.TotalFiles++

		// 读取文件内容
		content, err := ioutil.ReadFile(path)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("读取文件失败 %s: %v", path, err))
			return nil
		}

		mapperFile := MapperFile{
			Path:    path,
			Content: string(content),
		}

		// 更新文件
		if updateMapperFile(&mapperFile) {
			result.UpdatedFiles++
			result.MethodsFixed += countMethodsFixed(mapperFile.Content)

			// 写回文件
			err = ioutil.WriteFile(path, []byte(mapperFile.Content), 0644)
			if err != nil {
				result.Errors = append(result.Errors, fmt.Sprintf("写入文件失败 %s: %v", path, err))
			}
		}

		return nil
	})

	if err != nil {
		fmt.Printf("遍历目录失败: %v\n", err)
		return
	}

	// 输出结果
	fmt.Printf("Mapper文件上下文更新完成:\n")
	fmt.Printf("- 总文件数: %d\n", result.TotalFiles)
	fmt.Printf("- 更新文件数: %d\n", result.UpdatedFiles)
	fmt.Printf("- 修复方法数: %d\n", result.MethodsFixed)

	if len(result.Errors) > 0 {
		fmt.Printf("- 错误数: %d\n", len(result.Errors))
		for _, err := range result.Errors {
			fmt.Printf("  %s\n", err)
		}
	}
}

// updateMapperFile 更新mapper文件
func updateMapperFile(file *MapperFile) bool {
	originalContent := file.Content

	// 1. 确保导入了context包
	file.Content = ensureContextImport(file.Content)

	// 2. 更新查询方法签名（添加ctx参数）
	file.Content = updateMethodSignatures(file.Content)

	// 3. 更新GORM查询调用（添加WithContext）
	file.Content = updateGormCalls(file.Content)

	// 4. 更新方法注释
	file.Content = updateMethodComments(file.Content)

	return file.Content != originalContent
}

// ensureContextImport 确保导入了context包
func ensureContextImport(content string) string {
	// 检查是否已经导入了context
	if strings.Contains(content, `"context"`) {
		return content
	}

	// 查找import语句
	importRegex := regexp.MustCompile(`import\s*\(\s*\n`)
	if importRegex.MatchString(content) {
		// 在import块中添加context
		return importRegex.ReplaceAllString(content, "import (\n\t\"context\"\n\n")
	}

	// 查找单行import
	singleImportRegex := regexp.MustCompile(`import\s+"[^"]+"\s*\n`)
	if singleImportRegex.MatchString(content) {
		// 转换为多行import并添加context
		return singleImportRegex.ReplaceAllStringFunc(content, func(match string) string {
			importPath := regexp.MustCompile(`"[^"]+"`).FindString(match)
			return fmt.Sprintf("import (\n\t\"context\"\n\n\t%s\n)\n", importPath)
		})
	}

	return content
}

// updateMethodSignatures 更新方法签名，添加ctx参数
func updateMethodSignatures(content string) string {
	// 匹配查询方法（Find, Get, Page等开头的方法）
	methodRegex := regexp.MustCompile(`func\s+\([^)]+\)\s+(Find\w*|Get\w*|Page\w*|Query\w*|Search\w*)\s*\(([^)]*)\)\s*\([^)]*\)\s*\{`)

	return methodRegex.ReplaceAllStringFunc(content, func(match string) string {
		// 检查是否已经有ctx参数
		if strings.Contains(match, "ctx context.Context") {
			return match
		}

		// 在参数列表开头添加ctx参数
		paramStart := strings.Index(match, "(")
		paramEnd := strings.Index(match[paramStart:], ")")
		if paramStart == -1 || paramEnd == -1 {
			return match
		}

		paramEnd += paramStart
		params := match[paramStart+1 : paramEnd]

		var newParams string
		if strings.TrimSpace(params) == "" {
			newParams = "ctx context.Context"
		} else {
			newParams = "ctx context.Context, " + params
		}

		return match[:paramStart+1] + newParams + match[paramEnd:]
	})
}

// updateGormCalls 更新GORM查询调用，添加WithContext
func updateGormCalls(content string) string {
	// 匹配GORM查询调用
	patterns := []string{
		`\.db\.Where`,
		`\.db\.Find`,
		`\.db\.First`,
		`\.db\.Count`,
		`\.db\.Table`,
		`\.db\.Model`,
		`\.db\.Select`,
		`\.db\.Joins`,
		`\.db\.Order`,
		`\.db\.Limit`,
		`\.db\.Offset`,
		`\.DB\.Where`,
		`\.DB\.Find`,
		`\.DB\.First`,
		`\.DB\.Count`,
		`\.DB\.Table`,
		`\.DB\.Model`,
		`\.DB\.Select`,
		`\.DB\.Joins`,
		`\.DB\.Order`,
		`\.DB\.Limit`,
		`\.DB\.Offset`,
	}

	for _, pattern := range patterns {
		// 检查是否已经有WithContext调用
		withContextPattern := strings.Replace(pattern, ".", `\.`, -1) + `Context\(ctx\)`
		withContextRegex := regexp.MustCompile(withContextPattern)
		if withContextRegex.MatchString(content) {
			continue
		}

		// 添加WithContext调用
		regex := regexp.MustCompile(pattern)
		replacement := strings.Replace(pattern, ".db.", ".db.WithContext(ctx).", 1)
		replacement = strings.Replace(replacement, ".DB.", ".DB.WithContext(ctx).", 1)
		content = regex.ReplaceAllString(content, replacement)
	}

	return content
}

// updateMethodComments 更新方法注释，添加ctx参数说明
func updateMethodComments(content string) string {
	// 匹配方法注释中的参数部分
	commentRegex := regexp.MustCompile(`(//\s*参数:\s*\n)((?://\s*-\s*[^\n]*\n)*)`)

	return commentRegex.ReplaceAllStringFunc(content, func(match string) string {
		// 检查是否已经有ctx参数说明
		if strings.Contains(match, "ctx: 上下文") || strings.Contains(match, "ctx context.Context") {
			return match
		}

		lines := strings.Split(match, "\n")
		if len(lines) < 2 {
			return match
		}

		// 在参数列表开头添加ctx参数说明
		result := lines[0] + "\n//   - ctx: 上下文\n"
		for i := 1; i < len(lines); i++ {
			if lines[i] != "" {
				result += lines[i] + "\n"
			}
		}

		return result
	})
}

// countMethodsFixed 统计修复的方法数量
func countMethodsFixed(content string) int {
	methodRegex := regexp.MustCompile(`func\s+\([^)]+\)\s+(Find\w*|Get\w*|Page\w*|Query\w*|Search\w*)\s*\(.*ctx context\.Context`)
	matches := methodRegex.FindAllString(content, -1)
	return len(matches)
}
