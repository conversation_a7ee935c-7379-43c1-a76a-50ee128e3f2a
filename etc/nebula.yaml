Name: nebula
Host: 0.0.0.0
Port: 9998
Timeout: 10000

MicroServices:
  phoenix:
    Url: http://***************:10011
    Desc: saas微服务
  macrohard:
    Url: http://0.0.0.0:8090
    Desc: 文档微服务

NebulaDB:
  type: mysql
  host: **************
  port: "33061"
  user: developer
  password: "Zj123456."
  schema: nebula
  maxIdleConns: 5
  connMaxLifetime: 60
  maxOpenConns: 20

DocvaultDB:
  type: mysql
  host: **************
  port: "33061"
  user: developer
  password: "Zj123456."
  schema: docvault
  maxIdleConns: 5
  connMaxLifetime: 60
  maxOpenConns: 20

PhoenixDB:
  type: mysql
  host: **************
  port: "33061"
  user: developer
  password: "Zj123456."
  schema: phoenix
  maxIdleConns: 5
  connMaxLifetime: 60
  maxOpenConns: 20

# redis 配置
Redis:
  Host: "***************:6379"
  Pass: ""
  Type: "node"

Log:
  ServiceName: nebula
  Mode: console
  Path: ./logs
  Encoding: json
  Level: debug
  Compress: false
  KeepDays: 7  


Snowflake:
  Node: 1
  Epoch: 1288834974657
  NodeBits: 1
  StepBits: 5

Kafka:
  Brokers:
    - **************:9092
  MinBytes: 1
  MaxBytes: 10e6
  # Consumers:
  #   - Topic: sit_workflow_passed_SIGNATURE_APPROVAL
  #     GroupID: nebula
  #     Key: signature_approval
  Consumers:
    - Topic: sit_flow_event_push_FILE_INCORPORATE
      GroupID: nebula1
      Key: import_group_external_docs_to_company_approval
    - Topic: sit_flow_event_push_FILE_GRANT
      GroupID: nebula4
      Key: file_grant
    - Topic: sit_flow_event_push_FILE_RECLAIM
      GroupID: nebula1
      Key: file_reclaim
    - Topic: sit_flow_event_push_FILE_DISPOSAL
      GroupID: nebula1
      Key: file_disposal
    - Topic: sit_flow_event_push_FILE_BORROW
      GroupID: nebula1
      Key: file_borrow
  Producers:
    docvault_business_dictionary_change:
       Topic: sit_docvault_business_dictionary_change
       Key: docvault_business_dictionary_change
    data_export:
      Topic: sit_data_export_topic
      Key: data_export

Business:
  SignatureAuthLetterFileID: "573799229890260630"

DocvaultRPC:
  Endpoints:
    - 127.0.0.1:11013
  # Etcd:
  #   Hosts:
  #     - **************:23791
  #   Key: docvault.rpc.sit
  NonBlock: true
