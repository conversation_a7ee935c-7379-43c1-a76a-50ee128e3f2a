{"openapi": "3.0.1", "info": {"title": "nebula", "description": "中一检测BFF服务", "version": "1.0.0"}, "tags": [], "paths": {"/nebula/api/v1/document-library/allow/borrows": {"get": {"summary": "查询可借阅文档列表", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "validityKind", "in": "query", "description": "1- 即将作废：文件库中搜索 2即将实施：文件库中搜索 3- 有效：文件库中搜索 4 拟修订：文件库中搜索 5 作废：作废文件库中搜索", "required": false, "example": 0, "schema": {"type": "integer"}}, {"name": "doumentKind", "in": "query", "description": "1 内部库 2 外部库", "required": false, "schema": {"type": "integer"}}, {"name": "documentCategoryId", "in": "query", "description": "文档分类 id", "required": false, "schema": {"type": "string"}}, {"name": "documentName", "in": "query", "description": "文档名称 模糊", "required": false, "schema": {"type": "string"}}, {"name": "documentNo", "in": "query", "description": "文档编号 模糊", "required": false, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VLaW5kIjoid2ViIiwiZXhwIjoxNzUyMDY3Mzk3LCJpYXQiOjE3NTIwNTI5OTcsImlzQWRtaW5Vbml0IjpmYWxzZSwiaXNTdXBlckFkbWluIjpmYWxzZSwiaXNWaXJ0dWFsVXNlciI6ZmFsc2UsIm1vYmlsZSI6IjE5OTA2NTQwNDE0Iiwib3JnYW5pemF0aW9uSWQiOiI1Njk3MTg5MDI4Mjk5MDM1MTAiLCJ0ZW5hbnRJZCI6IjUxMjU4MjIzMzA4MTY3MTE4NiIsInVzZXJJZCI6IjU3MDg0NTE5MzcyOTc2NTAxNCJ9.-4uVT4XIS8u2bnubvTbn4OrhEMjGQQS3KVW5d6Cucgs", "schema": {"type": "string", "default": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VLaW5kIjoid2ViIiwiZXhwIjoxNzUyMDY3Mzk3LCJpYXQiOjE3NTIwNTI5OTcsImlzQWRtaW5Vbml0IjpmYWxzZSwiaXNTdXBlckFkbWluIjpmYWxzZSwiaXNWaXJ0dWFsVXNlciI6ZmFsc2UsIm1vYmlsZSI6IjE5OTA2NTQwNDE0Iiwib3JnYW5pemF0aW9uSWQiOiI1Njk3MTg5MDI4Mjk5MDM1MTAiLCJ0ZW5hbnRJZCI6IjUxMjU4MjIzMzA4MTY3MTE4NiIsInVzZXJJZCI6IjU3MDg0NTE5MzcyOTc2NTAxNCJ9.-4uVT4XIS8u2bnubvTbn4OrhEMjGQQS3KVW5d6Cucgs"}}, {"name": "X-Session-User-Id", "in": "header", "description": "", "example": "570845193729765014", "schema": {"type": "string", "default": "570845193729765014"}}, {"name": "X-Session-Tenant-Id", "in": "header", "description": "", "example": "575527573568777878", "schema": {"type": "string", "default": "575527573568777878"}}, {"name": "X-Session-Organization-Id", "in": "header", "description": "", "example": "569718902829903510", "schema": {"type": "string", "default": "569718902829903510"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "title": "代码", "description": "0-成功"}, "msg": {"type": "string", "title": "消息"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"documentId": {"type": "string", "title": "文档 id"}, "documenVersionNo": {"type": "string", "title": "版本版次"}, "documentNo": {"type": "string", "title": "文档编号"}, "documentName": {"type": "string", "title": "文档名称"}}, "required": ["documentId", "documenVersionNo", "documentNo", "documentName"]}}}, "required": ["list"]}}, "required": ["code", "msg", "data"]}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": [], "security": []}