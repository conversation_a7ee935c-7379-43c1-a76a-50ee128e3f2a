{"openapi": "3.0.1", "info": {"title": "nebula", "description": "中一检测BFF服务", "version": "1.0.0"}, "tags": [], "paths": {"/nebula/api/v1/document-library/allow/borrows": {"get": {"summary": "查询可借阅文档列表", "deprecated": false, "description": "", "tags": [], "parameters": [{"name": "validityKind", "in": "query", "description": "1- 即将作废：文件库中搜索 2即将实施：文件库中搜索 3- 有效：文件库中搜索 4 拟修订：文件库中搜索 5 作废：作废文件库中搜索", "required": false, "example": 0, "schema": {"type": "integer"}}, {"name": "doumentKind", "in": "query", "description": "1 内部库 2 外部库", "required": false, "schema": {"type": "integer"}}, {"name": "documentCategoryId", "in": "query", "description": "文档分类 id", "required": false, "schema": {"type": "string"}}, {"name": "documentName", "in": "query", "description": "文档名称 模糊", "required": false, "schema": {"type": "string"}}, {"name": "documentNo", "in": "query", "description": "文档编号 模糊", "required": false, "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VLaW5kIjoid2ViIiwiZXhwIjoxNzUyMDY3Mzk3LCJpYXQiOjE3NTIwNTI5OTcsImlzQWRtaW5Vbml0IjpmYWxzZSwiaXNTdXBlckFkbWluIjpmYWxzZSwiaXNWaXJ0dWFsVXNlciI6ZmFsc2UsIm1vYmlsZSI6IjE5OTA2NTQwNDE0Iiwib3JnYW5pemF0aW9uSWQiOiI1Njk3MTg5MDI4Mjk5MDM1MTAiLCJ0ZW5hbnRJZCI6IjUxMjU4MjIzMzA4MTY3MTE4NiIsInVzZXJJZCI6IjU3MDg0NTE5MzcyOTc2NTAxNCJ9.-4uVT4XIS8u2bnubvTbn4OrhEMjGQQS3KVW5d6Cucgs", "schema": {"type": "string", "default": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VLaW5kIjoid2ViIiwiZXhwIjoxNzUyMDY3Mzk3LCJpYXQiOjE3NTIwNTI5OTcsImlzQWRtaW5Vbml0IjpmYWxzZSwiaXNTdXBlckFkbWluIjpmYWxzZSwiaXNWaXJ0dWFsVXNlciI6ZmFsc2UsIm1vYmlsZSI6IjE5OTA2NTQwNDE0Iiwib3JnYW5pemF0aW9uSWQiOiI1Njk3MTg5MDI4Mjk5MDM1MTAiLCJ0ZW5hbnRJZCI6IjUxMjU4MjIzMzA4MTY3MTE4NiIsInVzZXJJZCI6IjU3MDg0NTE5MzcyOTc2NTAxNCJ9.-4uVT4XIS8u2bnubvTbn4OrhEMjGQQS3KVW5d6Cucgs"}}, {"name": "X-Session-User-Id", "in": "header", "description": "", "example": "570845193729765014", "schema": {"type": "string", "default": "570845193729765014"}}, {"name": "X-Session-Tenant-Id", "in": "header", "description": "", "example": "575527573568777878", "schema": {"type": "string", "default": "575527573568777878"}}, {"name": "X-Session-Organization-Id", "in": "header", "description": "", "example": "569718902829903510", "schema": {"type": "string", "default": "569718902829903510"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "title": "代码", "description": "0-成功"}, "msg": {"type": "string", "title": "消息"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"documentId": {"type": "string", "title": "文档 id"}, "documenVersionNo": {"type": "string", "title": "版本版次"}, "documentNo": {"type": "string", "title": "文档编号"}, "documentName": {"type": "string", "title": "文档名称"}}, "required": ["documentId", "documenVersionNo", "documentNo", "documentName"]}}}, "required": ["list"]}}, "required": ["code", "msg", "data"]}}}, "headers": {}}}, "security": []}}, "/nebula/api/v1/document-library/document/permission-usage-records": {"get": {"summary": "获取文档权限使用记录", "deprecated": false, "description": "查询指定文档的权限使用记录，支持多种筛选条件", "tags": ["document_library"], "parameters": [{"name": "documentId", "in": "query", "description": "文档ID", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "query", "description": "用户ID（可选，用于筛选特定用户）", "required": false, "schema": {"type": "string"}}, {"name": "fileForm", "in": "query", "description": "文件形式（可选，1电子文件 | 2纸质文件）", "required": false, "schema": {"type": "integer", "enum": [1, 2]}}, {"name": "filePermission", "in": "query", "description": "文件权限（可选，1查阅 | 2查阅/下载 | 3一次下载）", "required": false, "schema": {"type": "integer", "enum": [1, 2, 3]}}, {"name": "usageStatus", "in": "query", "description": "使用状态（可选，1未使用 | 2已使用）", "required": false, "schema": {"type": "integer", "enum": [1, 2]}}, {"name": "startTime", "in": "query", "description": "开始时间（可选，Unix时间戳）", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "endTime", "in": "query", "description": "结束时间（可选，Unix时间戳）", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "noPage", "in": "query", "description": "是否不分页", "required": false, "schema": {"type": "boolean", "default": false}}, {"name": "Authorization", "in": "header", "description": "JWT认证令牌", "required": true, "schema": {"type": "string"}}, {"name": "X-Session-User-Id", "in": "header", "description": "用户ID", "required": true, "schema": {"type": "string"}}, {"name": "X-Session-Tenant-Id", "in": "header", "description": "租户ID", "required": true, "schema": {"type": "string"}}, {"name": "X-Session-Organization-Id", "in": "header", "description": "组织ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功返回权限使用记录列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "title": "状态码", "description": "0-成功"}, "msg": {"type": "string", "title": "消息"}, "data": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentPermissionUsageRecord"}}, "page": {"type": "integer", "title": "当前页码"}, "pageSize": {"type": "integer", "title": "每页数量"}, "total": {"type": "integer", "title": "总记录数"}}, "required": ["data", "page", "pageSize", "total"]}}, "required": ["code", "msg", "data"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "msg": {"type": "string", "example": "参数错误"}}}}}}, "401": {"description": "未授权", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 401}, "msg": {"type": "string", "example": "未授权"}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 500}, "msg": {"type": "string", "example": "服务器内部错误"}}}}}}}, "security": []}}}, "components": {"schemas": {"DocumentPermissionUsageRecord": {"type": "object", "title": "文档权限使用记录", "properties": {"recordId": {"type": "string", "title": "记录ID"}, "workflowId": {"type": "string", "title": "流程ID"}, "distributeRecordId": {"type": "string", "title": "发放记录ID"}, "userId": {"type": "string", "title": "用户ID"}, "userName": {"type": "string", "title": "用户姓名"}, "fileForm": {"type": "integer", "title": "文件形式", "description": "1电子文件 | 2纸质文件", "enum": [1, 2]}, "filePermission": {"type": "integer", "title": "文件权限", "description": "1查阅 | 2查阅/下载 | 3一次下载", "enum": [1, 2, 3]}, "isUsed": {"type": "boolean", "title": "是否已使用"}, "distributeApplicant": {"type": "string", "title": "发放申请人ID"}, "distributeApplicantName": {"type": "string", "title": "发放申请人姓名"}, "distributeApplyTime": {"type": "integer", "format": "int64", "title": "发放申请时间", "description": "Unix时间戳"}, "usageTime": {"type": "integer", "format": "int64", "title": "使用时间", "description": "Unix时间戳，已使用时显示"}, "inventoryId": {"type": "string", "title": "清单ID", "description": "发放记录文件ID"}, "signForStatus": {"type": "integer", "title": "签收状态", "description": "1未签收 | 2已签收", "enum": [1, 2]}, "disposeStatus": {"type": "integer", "title": "处置状态", "description": "1未回收 | 2回收审批中 | 3已回收 | 4处置审批中 | 5已处置", "enum": [1, 2, 3, 4, 5]}}, "required": ["recordId", "workflowId", "distributeRecordId", "userId", "userName", "fileForm", "filePermission", "isUsed", "distributeApplicant", "distributeApplicantName", "distributeApplyTime", "inventoryId", "signForStatus", "dispose<PERSON><PERSON>us"]}}, "securitySchemes": {}}, "servers": [], "security": []}