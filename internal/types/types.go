// Code generated by goctl. DO NOT EDIT.
package types

type AddLoanReq struct {
	BorrowTime  int64          `json:"borrowTime"`  // 借阅时间
	DueTime     int64          `json:"dueTime"`     // 归还时间
	Reason      int32          `json:"reason"`      // 借阅原因类型
	OtherReason string         `json:"otherReason"` // 其他原因
	Documents   []LoanDocument `json:"documents"`   // 文档列表
}

type AddLoanResp struct {
	BorrowRecordID string `json:"id"` // 借阅记录ID
}

type Approval struct {
	UserID       string `json:"userId"`
	UserNickname string `json:"userNickname"`
	PassedDate   int64  `json:"passedDate"`
}

type ApprovalInfo struct {
	Auditors  []Approval `json:"auditors"`
	Approvers []Approval `json:"approvers"`
}

type BookFileInfo struct {
	FileId   string `json:"fileId"`
	FileName string `json:"fileName"`
}

type BookInfo struct {
	ID               string `json:"id,optional"`
	Status           int32  `json:"status,optional"`
	IsDelete         int32  `json:"isDelete,optional"`
	Number           string `json:"number,optional"`
	Name             string `json:"name,optional"`
	Author           string `json:"author,optional"`
	BookType         string `json:"bookType,optional"`
	Publisher        string `json:"publisher,optional"`
	RegisterCount    int    `json:"registerCount,optional"`
	ReceiveCount     int    `json:"receiveCount,optional"`
	BorrowCount      int    `json:"borrowCount,optional"`
	OnBorrow         bool   `json:"onBorrow,optional"`
	SurplusCount     int    `json:"surplusCount,optional"`
	CreatedTime      int64  `json:"createdTime,optional"`
	UpdatedTime      int64  `json:"updatedTime,optional"`
	CreatedBy        string `json:"createdBy,optional"`
	UpdatedBy        string `json:"updatedBy,optional"`
	DictionaryNodeID string `json:"dictionaryNodeId,optional"`
	FileID           string `json:"fileId,optional"`
	OrganizationID   string `json:"organizationId,optional"`
}

type BookListInfo struct {
	ID               string       `json:"id"`
	Status           int32        `json:"status"`
	Number           string       `json:"number"`
	Name             string       `json:"name"`
	Author           string       `json:"author"`
	BookType         string       `json:"bookType"`
	Publisher        string       `json:"publisher"`
	RegisterCount    int          `json:"registerCount"`
	ReceiveCount     int          `json:"receiveCount"`
	BorrowCount      int          `json:"borrowCount"`
	OnBorrow         bool         `json:"onBorrow"`
	SurplusCount     int          `json:"surplusCount"`
	DictionaryNodeID string       `json:"dictionaryNodeId"`
	BookFileInfo     BookFileInfo `json:"bookFileInfo"`
}

type BusinessDictionaryInfo struct {
	ID         string `json:"id"`
	ModuleName string `json:"moduleName"`
	FieldName  string `json:"fieldName"`
	Status     bool   `json:"status"`
	UpdatedAt  int64  `json:"updatedAt"`
	UpdatedBy  string `json:"updatedBy"`
}

type BusinessDictionaryNodeInfo struct {
	ID        string `json:"id"`
	ParentID  string `json:"parentId,optional"`
	Sort      int    `json:"sort"`
	Code      string `json:"code"`
	Name      string `json:"name"`
	Remark    string `json:"remark"`
	Status    bool   `json:"status"`
	UpdatedAt int64  `json:"updatedAt"`
	UpdatedBy string `json:"updatedBy"`
}

type ChangeExternalDocumentReq struct {
	ID                              string   `json:"id"`
	Name                            string   `json:"name"`
	OriginalDocNumber               string   `json:"originalDocNumber"`
	PublishDocNumber                string   `json:"publishDocNumber"`
	PublishDepartment               string   `json:"publishDepartment"`
	TypeDictionaryNodeId            string   `json:"typeDictionaryNodeId"`
	DomainDictionaryNodeId          string   `json:"domainDictionaryNodeId"`
	AuthenticationDictionaryNodeIds []string `json:"authenticationDictionaryNodeIds,optional"`
	PublishDate                     int64    `json:"publishDate"`
	EffectiveDate                   int64    `json:"effectiveDate"`
	OriginalNumber                  string   `json:"originalNumber,optional"`
	OriginalVersion                 string   `json:"originalVersion,optional"`
	FileId                          string   `json:"fileId,optional"`
	OrgType                         int      `json:"orgType"`
}

type ChangeExternalDocumentResp struct {
}

type ChangeInternalDocumentReq struct {
	ID                string `json:"id"`
	Name              string `json:"name"`
	FileID            string `json:"fileId,optional"`
	DocCategoryID     string `json:"docCategoryId"`
	DepartmentID      string `json:"departmentId"`
	AuthorID          string `json:"authorId"`
	PublishDate       int64  `json:"publishDate"`
	EffectiveDate     int64  `json:"effectiveDate"`
	OriginalNo        string `json:"originalNo,optional"`
	OriginalVersionNo string `json:"originalVersionNo,optional"`
}

type ChangeInternalDocumentResp struct {
}

type CommonSearchInfo struct {
	Search string `json:"search,optional" form:"search,optional"`
}

type ConfirmSignatureReq struct {
	TaskId string `json:"taskId"`
}

type ConfirmSignatureResp struct {
}

type CreateBookReq struct {
	Name             string `json:"name"`
	Author           string `json:"author"`
	Publisher        string `json:"publisher"`
	RegisterCount    int32  `json:"registerCount"`
	DictionaryNodeID string `json:"dictionaryNodeId"`
	FileID           string `json:"fileId,optional"`
}

type CreateBookResp struct {
}

type CreateBusinessDictionaryNodeReq struct {
	DictionaryID string `json:"dictionaryId"`
	ParentID     string `json:"parentId,optional"`
	Name         string `json:"name"`
	Remark       string `json:"remark"`
	Code         string `json:"code,optional"`
}

type CreateBusinessDictionaryNodeResp struct {
}

type CreateExternalDocumentReq struct {
	Name                            string   `json:"name"`
	FileID                          string   `json:"fileId,optional"`
	OriginalNumber                  string   `json:"originalNumber,optional"`
	OriginalVersion                 string   `json:"originalVersion,optional"`
	TypeDictionaryNodeId            string   `json:"typeDictionaryNodeId"`
	DomainDictionaryNodeId          string   `json:"domainDictionaryNodeId"`
	AuthenticationDictionaryNodeIds []string `json:"authenticationDictionaryNodeIds,optional"`
	OriginalDocNumber               string   `json:"originalDocNumber"`
	PublishDocNumber                string   `json:"publishDocNumber"`
	PublishDepartment               string   `json:"publishDepartment"`
	PublishDate                     int64    `json:"publishDate"`
	EffectiveDate                   int64    `json:"effectiveDate"`
	OrgType                         int      `json:"orgType"`
}

type CreateExternalDocumentResp struct {
}

type CreateInternalDocumentReq struct {
	Name              string `json:"name"`
	FileID            string `json:"fileId,optional"`
	DocCategoryID     string `json:"docCategoryId"`
	DepartmentID      string `json:"departmentId"`
	AuthorID          string `json:"authorId"`
	PublishDate       int64  `json:"publishDate"`
	EffectiveDate     int64  `json:"effectiveDate"`
	OriginalNo        string `json:"originalNo,optional"`
	OriginalVersionNo string `json:"originalVersionNo,optional"`
}

type CreateInternalDocumentResp struct {
}

type CreateSignatureTaskReq struct {
}

type CreateSignatureTaskResp struct {
	TaskId string `json:"taskId"`
}

type DeleteBookReq struct {
	ID string `json:"id"`
}

type DeleteBookResp struct {
}

type DeleteBusinessDictionaryNodeReq struct {
	NodeID string `json:"nodeId"`
}

type DeleteBusinessDictionaryNodeResp struct {
}

type DeleteDistributeReq struct {
	ID string `form:"id"` // 发放列表id
}

type DeleteDistributeResp struct {
}

type DeleteLoanReq struct {
	BorrowRecordId string `json:"borrowRecordId"` // 借阅记录ID
}

type DeleteLoanResp struct {
}

type DisposalRecord struct {
	HandoverPerson string `json:"handoverPerson"` // 交还人
	HandoverDate   int64  `json:"handoverDate"`   // 交还日期
	RecyclePerson  string `json:"recyclePerson"`  // 回收人
	RecycleDate    int64  `json:"recycleDate"`    // 回收日期
	DisposalPerson string `json:"disposalPerson"` // 处置人
	DisposalDate   int64  `json:"disposalDate"`   // 处置日期
	DisposalMethod string `json:"disposalMethod"` // 处置方式
}

type DistributeApplicationInfoReq struct {
	ID                 string           `json:"id,optional"`                 // 发放记录id
	SaveMethod         int              `json:"saveMethod"`                  // 保存方式
	Applicant          string           `json:"applicant"`                   // 申请人
	ApplyDate          int64            `json:"applyDate"`                   // 申请日期
	DistributeType     int              `json:"distributeType"`              // 发放类型
	FileType           int              `json:"fileType"`                    // 文件类型
	TypeDictNodeId     string           `json:"typeDictNodeId"`              // 类型字典节点id
	Reason             string           `json:"reason"`                      // 原因
	OtherReason        string           `json:"otherReason,optional"`        // 其他原因
	WishDistributeDate int64            `json:"wishDistributeDate,optional"` // 期望发放日期
	DistributeList     []DistributeItem `json:"distributeList"`              // 发放清单
}

type DistributeInventory struct {
	ID                        string         `json:"id"`                        // 发放清单id
	FileId                    string         `json:"fileId"`                    // 文档id
	FileName                  string         `json:"fileName"`                  // 文档名称
	Number                    string         `json:"number"`                    // 文档编号
	Version                   string         `json:"version"`                   // 文档版本
	Recipient                 string         `json:"recipient"`                 // 接收方
	EFileLook                 PermissionResp `json:"eFileLook"`                 // 电子文件查阅权限
	EFileLookAndDownload      PermissionResp `json:"eFileLookAndDownload"`      // 电子文件查阅和下载权限
	PaperDocumentOnceDownload PermissionResp `json:"paperDocumentOnceDownload"` // 纸质文件一次下载权限
	EFileOnceDownload         PermissionResp `json:"eFileOnceDownload"`         // 电子文件一次下载权限
}

type DistributeInventoryDetail struct {
	ID          string             `json:"id"`          // 发放清单ID
	FileID      string             `json:"fileId"`      // 文件ID
	FileName    string             `json:"fileName"`    // 文件名称
	Number      string             `json:"number"`      // 文件编号
	Version     string             `json:"version"`     // 文件版本
	Permissions []PermissionDetail `json:"permissions"` // 权限详情列表
}

type DistributeItem struct {
	FileID      string       `json:"fileId"`      // 文档id
	FileName    string       `json:"fileName"`    // 文档名称
	Number      string       `json:"number"`      // 文档编号
	Version     string       `json:"version"`     // 文档版本
	Permissions []Permission `json:"permissions"` // 权限
}

type DistributeUser struct {
	UserID   string `json:"userId"`   // 用户id
	FileForm int32  `json:"fileForm"` // 文件形式（1电子文件 | 2纸质文件）
	Nickname string `json:"nickname"` // 用户昵称
	Status   int    `json:"status"`   // 状态（1已签收 | 2未签收 | 3已回收 | 4已处置）
}

type DistributeUserDetail struct {
	UserID        string `json:"userId"`        // 用户ID
	UserNickname  string `json:"userNickname"`  // 用户姓名
	RecycleStatus int32  `json:"recycleStatus"` // 回收状态（0未回收 | 1已回收 | 2待审批）
	RecycleTime   int64  `json:"recycleTime"`   // 回收时间（Unix时间戳）
}

type DistributeUserPermission struct {
	InventoryId    string `json:"inventoryId"`    // 发放清单ID
	FileForm       int32  `json:"fileForm"`       // 文件形式,1电子文件 | 2纸质文件
	FilePermission int32  `json:"filePermission"` // 文件权限,1查阅 | 2查阅/下载 | 3一次下载
	UserId         string `json:"userId"`         // 用户ID
	UserNickName   string `json:"userNickName"`   // 用户昵称
	Status         int    `json:"status"`         // 状态 1发放审批中 | 2已发放 | 3回收审批中|4 已回收｜5 处置审批中
}

type DocumentDistributeRecord struct {
	RecordID                   string                             `json:"recordId"`                   // 记录ID
	WorkflowID                 string                             `json:"workflowId"`                 // 流程ID
	ApplyTime                  int64                              `json:"applyTime"`                  // 申请时间（发起审批的时间，YYYY-MM-DD HH:MM:SS）
	InternalElectronicQuery    []InternalElectronicQueryRecord    `json:"internalElectronicQuery"`    // 内发：电子文件-查询权限变更记录列表
	InternalElectronicDownload []InternalElectronicDownloadRecord `json:"internalElectronicDownload"` // 内发：电子文件-查询/下载权限变更记录列表
	Applicant                  string                             `json:"applicant"`                  // 变更人ID
	ApplicantName              string                             `json:"applicantName"`              // 变更人姓名
}

type ExportDocumentLibraryReq struct {
	ModuleType int                    `json:"moduleType"` // 模块类型 1-书籍库 2-内部库 3-外部库
	Params     map[string]interface{} `json:"params"`     // 参数
}

type ExportDocumentLibraryResp struct {
}

type ExternalDocumentInfo struct {
	ID                              string       `json:"id"`
	Number                          string       `json:"number"`
	Version                         string       `json:"version"`
	OriginalNumber                  string       `json:"originalNumber"`
	OriginalVersion                 string       `json:"originalVersion"`
	Name                            string       `json:"name"`
	DocType                         string       `json:"docType"`
	Domain                          string       `json:"domain"`
	OriginalDocNumber               string       `json:"originalDocNumber"`
	PublishDocNumber                string       `json:"publishDocNumber"`
	PublishDepartment               string       `json:"publishDepartment"`
	ApprovalInfo                    ApprovalInfo `json:"approvalInfo"`
	PublishDate                     int64        `json:"publishDate"`
	EffectiveDate                   int64        `json:"effectiveDate"`
	Authentication                  string       `json:"authentication"`
	Status                          int          `json:"status"`
	TypeDictionaryNodeId            string       `json:"typeDictionaryNodeId"`
	DomainDictionaryNodeId          string       `json:"domainDictionaryNodeId"`
	AuthenticationDictionaryNodeIds []string     `json:"authenticationDictionaryNodeIds"`
	FileInfo                        FileInfo     `json:"fileInfo"`
}

type ExternalElectronicDownloadRecord struct {
	RecordID                string `json:"recordId"`                // 记录ID
	WorkflowID              string `json:"workflowId"`              // 流程ID
	DistributeRecordID      string `json:"distributeRecordId"`      // 发放记录ID
	RecipientUserID         string `json:"recipientUserId"`         // 电子文件接收人ID
	RecipientUserName       string `json:"recipientUserName"`       // 电子文件接收人姓名
	DistributeApplicant     string `json:"distributeApplicant"`     // 发放人ID
	DistributeApplicantName string `json:"distributeApplicantName"` // 发放人姓名
	DistributeApplyTime     int64  `json:"distributeApplyTime"`     // 发放申请时间
	IsDownloaded            bool   `json:"isDownloaded"`            // 是否下载（false未下载 | true已下载）
	InventoryID             string `json:"inventoryId"`             // 清单ID（发放记录文件ID，用于发起处置申请）
}

type FileInfo struct {
	FileID   string `json:"fileId,optional"`
	FileName string `json:"fileName,optional"`
}

type GetBookListReq struct {
	PageInfo
	Number            string   `json:"number,optional"`
	Name              string   `json:"name,optional"`
	Author            string   `json:"author,optional"`
	Publisher         string   `json:"publisher,optional"`
	BookType          string   `json:"bookType,optional"`
	OnBorrow          string   `json:"onBorrow,optional"`
	DictionaryNodeIds []string `json:"dictionaryNodeIds,optional"`
}

type GetBookListResp struct {
	Total int64          `json:"total"`
	Data  []BookListInfo `json:"data"`
}

type GetBusinessDictionaryNodeTreeReq struct {
	ID string `form:"id"`
}

type GetBusinessDictionaryNodeTreeResp struct {
	ID       string                               `json:"id"`
	Name     string                               `json:"name"`
	Code     string                               `json:"code"`
	Children []*GetBusinessDictionaryNodeTreeResp `json:"children"`
}

type GetBusinessDictionaryNodesReq struct {
	DictionaryID string `form:"dictionaryId"`
	ParentID     string `form:"parentId,optional"`
}

type GetBusinessDictionaryNodesResp struct {
	Data []BusinessDictionaryNodeInfo `json:"data"`
}

type GetBusinessDictionaryRelationCountReq struct {
	NodeID string `form:"nodeId"`
}

type GetBusinessDictionaryRelationCountResp struct {
	Count int64 `json:"count"`
}

type GetBusinessDictionarysReq struct {
	PageInfo
	CommonSearchInfo
}

type GetBusinessDictionarysResp struct {
	PageInfo
	Data []BusinessDictionaryInfo `json:"data"`
}

type GetCurrentSignatureResp struct {
	ID                 string `json:"id"`
	SignatureBase64    string `json:"signatureBase64"`
	AuthLetterFileID   string `json:"authLetterFileId"`
	AuthLetterFileName string `json:"authLetterFileName"`
	EffectiveDate      int64  `json:"effectiveDate"`
	ApproverName       string `json:"approverName"`
}

type GetDisposalDetailReq struct {
	DistributeListID string `form:"distributeListId"` // 发放清单ID
}

type GetDisposalDetailResp struct {
	FileName        string           `json:"fileName"`        // 文件名称
	FileNumber      string           `json:"fileNumber"`      // 文件编号
	DisposalRecords []DisposalRecord `json:"disposalRecords"` // 处置记录列表
}

type GetDistributeDetailReq struct {
	ID string `form:"id"` // 发放记录ID
}

type GetDistributeDetailResp struct {
	ID                 string                      `json:"id"`                 // 发放记录ID
	WorkflowID         string                      `json:"workflowId"`         // 流程ID
	Applicant          string                      `json:"applicant"`          // 申请人ID
	ApplicantName      string                      `json:"applicantName"`      // 申请人姓名
	ApplyDate          int64                       `json:"applyDate"`          // 申请日期
	DistributeType     int32                       `json:"distributeType"`     // 发放类型（1内部发放 | 2外部发放）
	FileType           int32                       `json:"fileType"`           // 文件类型（1内部文件 | 2外部文件）
	FileCategory       string                      `json:"fileCategory"`       // 文件类别
	TypeDictNodeID     string                      `json:"typeDictNodeId"`     // 类型字典节点ID
	Reason             string                      `json:"reason"`             // 发放原因
	OtherReason        string                      `json:"otherReason"`        // 其他原因
	WishDistributeDate int64                       `json:"wishDistributeDate"` // 期望发放日期
	Status             int32                       `json:"status"`             // 状态（1待提交 | 2待审批 | 3已审批 | 4已驳回）
	DistributeList     []DistributeInventoryDetail `json:"distributeList"`     // 发放清单
	CreatedAt          int64                       `json:"createdAt"`          // 创建时间
	UpdatedAt          int64                       `json:"updatedAt"`          // 更新时间
}

type GetDistributeInventoryReq struct {
	ID string `form:"id"`
}

type GetDistributeInventoryResp struct {
	DisposalStatus string                `json:"disposalStatus"` // 处置状态
	Data           []DistributeInventory `json:"data"`           // 发放清单
}

type GetDistributeListInfo struct {
	ID                 string           `json:"id"`                 // 主键id
	Applicant          string           `json:"applicant"`          // 申请人
	ApplyDate          int64            `json:"applyDate"`          // 申请时间
	DistributeType     int              `json:"distributeType"`     // 发放类型，1内部发放 | 2外部发放
	FileType           int              `json:"fileType"`           // 文件类型，1内部文件 | 2外部文件
	FileCategory       string           `json:"fileCategory"`       // 文件类别
	Reason             string           `json:"reason"`             // 发放原因
	OtherReason        string           `json:"otherReason"`        // 其他原因
	WishDistributeDate int64            `json:"wishDistributeDate"` // 期望发放时间
	Status             int              `json:"status"`             // 状态，1待提交 | 2待审批 | 3已审批 | 4已驳回
	WorkflowID         string           `json:"workflowId"`         // 工作流id
	ApprovalInfo       ApprovalInfo     `json:"approver"`           // 审批人
	DistributeCount    int              `json:"distributeCount"`    // 发放份数
	ReceivedCount      int              `json:"receivedCount"`      // 签收份数
	DisposalCount      int              `json:"disposalCount"`      // 处置份数
	RecycleStatus      string           `json:"recycleStatus"`      // 回收状态
	DistributeUsers    []DistributeUser `json:"distributeUsers"`    // 发放用户
}

type GetDistributeListReq struct {
	PageInfo
	FileNumber     string   `json:"fileNumber,optional"`     // 文件编号
	FileName       string   `json:"fileName,optional"`       // 文件名称
	FileType       int      `json:"fileType,optional"`       // 文件类型
	FileCategory   []string `json:"fileCategory,optional"`   // 文件类别
	DistributeType int      `json:"distributeType,optional"` // 发放类型
	Status         int      `json:"status,optional"`         // 状态，1待提交 | 2待审批 | 3已审批 | 4已驳回
	Applicant      string   `json:"applicant,optional"`      // 申请人
}

type GetDistributeListResp struct {
	Total int64                   `json:"total"`
	Data  []GetDistributeListInfo `json:"data"`
}

type GetDistributeUserPermissionsReq struct {
	DocumentId string `form:"documentId"`      // 发放记录ID
	UserID     string `form:"userId,optional"` // 用户ID
	Status     int    `form:"status,optional"` // 状态 1发放审批中 | 2已发放 | 3回收审批中 |4 已回收｜5 处置审批中
}

type GetDistributeUserPermissionsResp struct {
	List []DistributeUserPermission `json:"list"` // 用户权限记录列表
}

type GetDocPermissionUsersReq struct {
	FileID         string `form:"fileId"`                  // 文档id
	FileForm       int32  `form:"fileForm,optional"`       // 文档形式 1电子文档 | 2纸质文档
	FilePermission int32  `form:"filePermission,optional"` // 文档权限 1查看 | 2查看/下载 | 3一次下载
}

type GetDocPermissionUsersResp struct {
	OrganizationUserTree *OrganizationUserTree `json:"organizationUserInfo"`
	Users                []OrganizationUsers   `json:"users"`
}

type GetDocumentDistributeRecordsReq struct {
	DocumentID string `form:"documentId"` // 文档ID
	PageInfo
}

type GetDocumentDistributeRecordsResp struct {
	Data []DocumentDistributeRecord `json:"data"` // 发放回收记录列表
	PageInfo
}

type GetExternalDocumentReq struct {
	DistributeID string `form:"id"`
}

type GetExternalDocumentsReq struct {
	PageInfo
	Number                         string   `json:"number,optional"`
	Name                           string   `json:"name,optional"`
	OriginalNumber                 string   `json:"originalNumber,optional"`
	OriginalDocNumber              string   `json:"originalDocNumber,optional"`
	PublishDocNumber               string   `json:"publishDocNumber,optional"`
	PublishDepartment              string   `json:"publishDepartment,optional"`
	TypeDictionaryNodeIds          []string `json:"typeDictionaryNodeIds,optional"`
	DomainDictionaryNodeId         string   `json:"domainDictionaryNodeId,optional"`
	AuthenticationDictionaryNodeId string   `json:"authenticationDictionaryNodeId,optional"`
	BeAttachedFile                 int8     `json:"beAttachedFile,optional"`
	Status                         int      `json:"status,optional"`
	OrgType                        int      `json:"orgType,optional"`
}

type GetExternalDocumentsResp struct {
	PageInfo
	Data []ExternalDocumentInfo `json:"data"`
}

type GetExternalElectronicDownloadRecordsReq struct {
	DocumentID string `form:"documentId"` // 文档ID
	PageInfo
}

type GetExternalElectronicDownloadRecordsResp struct {
	Data []ExternalElectronicDownloadRecord `json:"data"` // 外发电子文件下载记录列表
	PageInfo
}

type GetInternalDocumentReq struct {
	ID string `form:"id"`
}

type GetInternalDocumentResp struct {
	ID                string       `json:"id"`
	No                string       `json:"no"`
	VersionNo         string       `json:"versionNo"`
	OriginalNo        string       `json:"originalNo"`
	OriginalVersionNo string       `json:"originalVersionNo"`
	Name              string       `json:"name"`
	DocCategoryID     string       `json:"docCategoryId"`
	DepartmentID      string       `json:"departmentId"`
	AuthorID          string       `json:"authorId"`
	AuthorNickname    string       `json:"authorNickname"`
	PublishDate       int64        `json:"publishDate"`
	EffectiveDate     int64        `json:"effectiveDate"`
	Status            int          `json:"status"`
	ApprovalInfo      ApprovalInfo `json:"approvalInfo"`
	FileID            string       `json:"fileId"`
}

type GetInternalDocumentsReq struct {
	PageInfo
	DocCategoryIDs []string `json:"docCategoryIds,optional"`
	DepartmentIDs  []string `json:"departmentIds,optional"`
	Status         int      `json:"status,optional"`
	HasAttachment  int8     `json:"hasAttachment,optional"`
	Name           string   `json:"name,optional"`
	No             string   `json:"no,optional"`
	OriginalNo     string   `json:"originalNo,optional"`
}

type GetInternalDocumentsResp struct {
	PageInfo
	Data []InternalDocumentInfo `json:"data"`
}

type GetInternalPaperDownloadRecordsReq struct {
	DocumentID string `form:"documentId"` // 文档ID
	PageInfo
}

type GetInternalPaperDownloadRecordsResp struct {
	Data []InternalPaperDownloadRecord `json:"data"` // 内发纸质文件下载记录列表
	PageInfo
}

type GetLoanRecordDocumentsReq struct {
	Id string `form:"id"` // 借阅记录id
}

type GetLoanRecordDocumentsResp struct {
	Data []LoanDocument `json:"data"` // 文档清单
}

type GetLoanRecordsReq struct {
	PageInfo
	DocumentNo         string `form:"documentNo,optional"`         // 文档编号
	DocumentName       string `form:"documentName,optional"`       // 文档名称
	DocumentModule     string `form:"documentModule,optional"`     // 文档模块
	DocumentCategoryId string `form:"documentCategoryId,optional"` // 文档类别ID
	UserNickname       string `form:"userNickname,optional"`       // 借阅人名称
	ApprovalStatus     string `form:"approvalStatus,optional"`     // 审批状态
}

type GetLoanRecordsResp struct {
	Total int64        `json:"total"` // 总数
	Data  []LoanRecord `json:"data"`  // 借阅记录列表
}

type GetRecycleInfoByDistributeIdReq struct {
	DistributeListID string `form:"distributeListId"` // 发放清单ID
}

type GetRecycleInfoByDistributeIdResp struct {
	FileName       string          `json:"fileName"`       // 文件名称
	FileNumber     string          `json:"fileNumber"`     // 文件编号
	RecycleRecords []RecycleRecord `json:"recycleRecords"` // 回收记录列表
}

type GetSignatureHistoryReq struct {
	PageInfo
}

type GetSignatureHistoryResp struct {
	Data []SignatureHistoryItem `json:"data"`
	PageInfo
}

type GetSignatureTaskStatusReq struct {
	TaskId string `form:"taskId"`
}

type GetSignatureTaskStatusResp struct {
	SignatureBase64 string `json:"signatureBase64,optional"`
}

type HandoverPerson struct {
	HandoverID     string `json:"handoverId"`     // 交还人ID
	HandoverName   string `json:"handoverName"`   // 交还人名字
	FileForm       int32  `json:"fileForm"`       // 文件形式（1电子文件 | 2纸质文件）
	FilePermission int32  `json:"filePermission"` // 文件权限（1查询 | 2查询/下载 | 3一次下载）
}

type ImportBookReq struct {
	DictionaryID string     `json:"dictionaryId"`
	ExcelID      string     `json:"excelId"`
	FileInfo     []FileInfo `json:"fileInfo,optional"`
}

type ImportBookResp struct {
}

type ImportDocumentLibraryReq struct {
	MainFileID         string   `json:"mainFileId"`
	ListFileIDs        []string `json:"listFileIds,optional"`
	TypeDictionaryID   string   `json:"typeDictionaryId"`            // 类型字典id
	DomainDictionaryID string   `json:"domainDictionaryId,optional"` // 领域字典id
	AuthDictionaryID   string   `json:"authDictionaryId,optional"`   // 认证字典id
	ModuleType         int      `json:"moduleType"`                  // 模块类型 1-书籍库 2-内部库 3-外部库
}

type ImportDocumentLibraryResp struct {
}

type InternalDocumentInfo struct {
	ID                string       `json:"id"`
	No                string       `json:"no"`
	VersionNo         string       `json:"versionNo"`
	OriginalNo        string       `json:"originalNo"`
	OriginalVersionNo string       `json:"originalVersionNo"`
	Name              string       `json:"name"`
	DocCategoryID     string       `json:"docCategoryId"`
	DocCategoryName   string       `json:"docCategoryName"`
	DepartmentID      string       `json:"departmentId"`
	DepartmentName    string       `json:"departmentName"`
	AuthorID          string       `json:"authorId"`
	AuthorNickname    string       `json:"authorNickname"`
	PublishDate       int64        `json:"publishDate"`
	EffectiveDate     int64        `json:"effectiveDate"`
	ApprovalInfo      ApprovalInfo `json:"approvalInfo"`
	Status            int          `json:"status"`
}

type InternalElectronicDownloadRecord struct {
	UserID       string `json:"userId"`       // 用户ID
	UserNickname string `json:"userNickname"` // 用户昵称
	Status       int32  `json:"status"`       // 状态枚举：1-未签收 | 2-已签收 | 3-回收
}

type InternalElectronicQueryRecord struct {
	UserID       string `json:"userId"`       // 用户ID
	UserNickname string `json:"userNickname"` // 用户昵称
	Status       int32  `json:"status"`       // 状态枚举：1-未签收 | 2-已签收 | 3-回收
}

type InternalPaperDownloadRecord struct {
	RecordID                string `json:"recordId"`                // 记录ID
	WorkflowID              string `json:"workflowId"`              // 流程ID
	DistributeRecordID      string `json:"distributeRecordId"`      // 发放记录ID
	RecipientUserID         string `json:"recipientUserId"`         // 纸质文件接收人ID
	RecipientUserName       string `json:"recipientUserName"`       // 纸质文件接收人姓名
	PaperFileStatus         int32  `json:"paperFileStatus"`         // 纸质文件状态（1未回收 | 2回收中 | 3已回收）
	FileDisposeStatus       int32  `json:"fileDisposeStatus"`       // 文件处置状态（1未处置 | 2处置中 | 3已处置）
	DisposalMethod          string `json:"disposalMethod"`          // 处置方法（已处置时显示）
	DistributeApplicant     string `json:"distributeApplicant"`     // 发放人ID
	DistributeApplicantName string `json:"distributeApplicantName"` // 发放人姓名
	DistributeApplyTime     int64  `json:"distributeApplyTime"`     // 发放申请时间
	RecycleApplicant        string `json:"recycleApplicant"`        // 回收人ID
	RecycleApplicantName    string `json:"recycleApplicantName"`    // 回收人姓名
	RecycleApplyTime        int64  `json:"recycleApplyTime"`        // 回收申请时间
	InventoryID             string `json:"inventoryId"`             // 清单ID（发放记录文件ID，用于发起处置申请）
}

type LoanDocument struct {
	DocumentId           string `json:"documentId"`                    // 文档id
	DocumentName         string `json:"documentName,optional"`         // 文档名称
	DocumentValidity     int32  `json:"documentValidity,optional"`     // 文档有效性
	DocumentModuleType   int32  `json:"documentModuleType"`            // 文档模块：1书籍，2内部文档，3外部文档
	DocumentModuleName   string `json:"documentModuleName,optional"`   // 文档模块名称
	DocumentCategoryName string `json:"documentCategoryName,optional"` // 文档类别名称
	DocumentNo           string `json:"documentNo,optional"`           // 文档编号
	DocumentVersionNo    string `json:"documentVersionNo"`             // 文档版次号
	BorrowStatus         int32  `json:"borrowStatus,optional"`         // 借阅状态
}

type LoanRecord struct {
	Id                    string `json:"id"`                    // 借阅记录ID
	BorrowTime            int64  `json:"borrowTime"`            // 借阅时间
	DueTime               int64  `json:"dueTime"`               // 归还时间
	BorrowReason          string `json:"borrowReason"`          // 借阅原因
	UserNickname          string `json:"userNickname"`          // 借阅人姓名
	ReviewerName          string `json:"reviewerName"`          // 审核人姓名
	ApproverName          string `json:"approverName"`          // 批准人姓名
	RecoverNames          string `json:"recoverNames"`          // 回收人姓名
	RecoverDocumentsCount int    `json:"recoverDocumentsCount"` // 已回收文档数量
	BorrowDocumentsCount  int    `json:"borrowDocumentsCount"`  // 借阅文档数量
	ApprovalStatus        int    `json:"approvalStatus"`        // 审批状态：1-待提交，2-待审批，3-已驳回，4-已审批
	ApprovalApplyTime     int64  `json:"approvalApplyTime"`     // 申请时间
}

type MoveBusinessDictionaryNodeReq struct {
	ID   string `json:"id"`
	Sort int    `json:"sort"`
}

type MoveBusinessDictionaryNodeResp struct {
}

type OrganizationUserTree struct {
	ParentId          string                  `json:"parentId"`
	OrgID             string                  `json:"orgId"`
	OrgName           string                  `json:"orgName"`
	OrganizationUsers []OrganizationUsers     `json:"userInfo"`
	Children          []*OrganizationUserTree `json:"children"`
}

type OrganizationUsers struct {
	UserID   string `json:"userId"`
	Nickname string `json:"nickname"`
	Status   int    `json:"status"`
}

type PageInfo struct {
	Page     uint64 `json:"page,optional" validate:"number" form:"page,optional"`
	PageSize uint64 `json:"pageSize,optional" validate:"number,max=100000" form:"pageSize,optional"`
	Total    uint64 `json:"total,optional"`
	NoPage   bool   `json:"noPage,optional" form:"noPage,optional"`
}

type Permission struct {
	FileForm       int         `json:"fileForm"`           // 文档形式
	FilePermission int         `json:"filePermission"`     // 文档权限
	Recipient      string      `json:"recipient,optional"` // 接收方
	ReceivedBy     []Recipient `json:"receivedBy"`         // 接收人
}

type PermissionDetail struct {
	FileForm       int32                  `json:"fileForm"`       // 文件形式（1电子文件 | 2纸质文件）
	FilePermission int32                  `json:"filePermission"` // 文件权限（1查询 | 2查询/下载 | 3一次下载）
	Recipient      string                 `json:"recipient"`      // 接收方
	ReceivedBy     []DistributeUserDetail `json:"receivedBy"`     // 接收人详情列表
}

type PermissionOperationReq struct {
	DocumentID     string `json:"documentId"`     // 文档id
	FileForm       int64  `json:"fileForm"`       // 文件形式,1电子文件 | 2纸质文件
	FilePermission int64  `json:"filePermission"` // 文件权限,1查阅 | 2查阅/下载 | 3一次下载
	InventoryID    string `json:"inventoryId"`    // 清单id
	OperationType  int64  `json:"operationType"`  // 操作类型 1查阅 2下载
}

type PermissionOperationResp struct {
	FileID     string `json:"fileId"`               // 文件id，预览地址，仅借阅查询时生效
	PreviewURL string `json:"previewUrl,omitempty"` // 预览地址，预览地址，仅借阅查询时生效
}

type PermissionResp struct {
	FileForm        int32        `json:"fileForm"`        // 文件形式（1电子文件 | 2纸质文件）
	FilePermission  int32        `json:"filePermission"`  // 文件权限（1查阅 | 2查阅/下载 | 3一次下载）
	DistributeCount int          `json:"distributeCount"` // 发放份数
	RecycleCount    int          `json:"recycleCount"`    // 回收份数
	DisposalCount   int          `json:"disposalCount"`   // 处置份数
	ReceivedBy      []ReceivedBy `json:"receivedBy"`      // 接收人
}

type PlagiarismCheckReq struct {
	Ids []string `json:"ids"`
}

type PlagiarismCheckResp struct {
}

type ReceivedBy struct {
	UserID      string `json:"userId"`      // 用户id
	Nickname    string `json:"nickname"`    // 用户昵称
	Status      int    `json:"status"`      // 状态（1未回收 | 2回收审批中 | 3已回收 | 4处置审批中 | 5已处置）
	RecycleDate int64  `json:"recycleDate"` // 回收时间
}

type Recipient struct {
	UserId   string `json:"userId"`
	UserName string `json:"userName"`
}

type RecycleRecord struct {
	RecycleInitiator string           `json:"recycleInitiator"` // 回收发起人
	RecycleReason    string           `json:"recycleReason"`    // 回收原因
	HandoverPersons  []HandoverPerson `json:"handoverPersons"`  // 交还人信息列表
	Auditors         []string         `json:"auditors"`         // 审批人列表
	Approvers        []string         `json:"approvers"`        // 批准人列表
	RecycleDate      int64            `json:"recycleDate"`      // 回收日期（毫秒级时间戳）
}

type SignatureHistoryItem struct {
	ID                 string `json:"id"`
	SignatureBase64    string `json:"signatureBase64"`
	AuthLetterFileID   string `json:"authLetterFileId"`
	AuthLetterFileName string `json:"authLetterFileName"`
	EffectiveDate      int64  `json:"effectiveDate"`
	ExpirationDate     int64  `json:"expirationDate"`
}

type TemporaryStorageDistributeInfoResp struct {
}

type UpdateBookResp struct {
}

type UpdateBusinessDictionaryNodeReq struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Remark string `json:"remark"`
	Status bool   `json:"status"`
	Code   string `json:"code"`
}

type UpdateBusinessDictionaryNodeResp struct {
}

type UpdateLoanReq struct {
	Id           string         `json:"id"`           // 借阅申请id
	BorrowTime   int64          `json:"borrowTime"`   // 借阅时间
	DueTime      int64          `json:"dueTime"`      // 归还时间
	BorrowReason string         `json:"borrowReason"` // 借阅原因
	Documents    []LoanDocument `json:"documents"`    // 文档列表
}

type UpdateLoanResp struct {
}

type UploadSignatureReq struct {
	TaskId          string `json:"taskId"`
	SignatureBase64 string `json:"signatureBase64"`
}

type UploadSignatureResp struct {
}

type WorkflowInfoReq struct {
	TenantID       string `json:"tenantId"`
	OrganizationID string `json:"organizationId"`
	WorkflowID     string `json:"workflowId"`
	SponsorID      string `json:"sponsorId"`
	FormContent    string `json:"formContent"`
	CreatedAt      int64  `json:"createdAt"`
	BusinessID     string `json:"businessId"`
	BusinessCode   string `json:"businessCode"`
}

type WorkflowInfoResp struct {
}
