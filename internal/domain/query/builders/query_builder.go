package builders

import (
	"context"

	"nebula/internal/domain/query/filters"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
)

// QueryBuilder 查询构建器
// 功能：使用建造者模式构建复杂的查询参数
type QueryBuilder struct {
	pageReq     mapper.PageBorrowRecordReq
	filterChain *filters.FilterChain
	ctx         context.Context
	req         *types.GetLoanRecordsReq
	svcCtx      *svc.ServiceContext
}

// NewQueryBuilder 创建查询构建器
// 功能：创建查询构建器实例
// 参数：ctx - 上下文，req - 请求参数，svcCtx - 服务上下文
// 返回值：查询构建器实例
func NewQueryBuilder(ctx context.Context, req *types.GetLoanRecordsReq, svcCtx *svc.ServiceContext) *QueryBuilder {
	return &QueryBuilder{
		filterChain: filters.NewFilterChain(),
		ctx:         ctx,
		req:         req,
		svcCtx:      svcCtx,
	}
}

// WithPagination 设置分页参数
// 功能：设置分页相关参数
// 参数：page - 页码，pageSize - 页大小，noPage - 是否不分页
// 返回值：查询构建器实例（支持链式调用）
func (qb *QueryBuilder) WithPagination(page, pageSize int, noPage bool) *QueryBuilder {
	// 实现步骤：
	// 1. 设置分页参数到pageReq中
	// 2. 返回自身以支持链式调用

	qb.pageReq.Page = page
	qb.pageReq.PageSize = pageSize
	qb.pageReq.NoPage = noPage
	return qb
}

// AddFilter 添加过滤器
// 功能：向查询构建器中添加过滤器
// 参数：filter - 过滤器策略
// 返回值：查询构建器实例（支持链式调用）
func (qb *QueryBuilder) AddFilter(filter filters.FilterStrategy) *QueryBuilder {
	// 实现步骤：
	// 1. 将过滤器添加到过滤器链中
	// 2. 返回自身以支持链式调用

	qb.filterChain.AddFilter(filter)
	return qb
}

// WithApprovalStatusFilter 添加审批状态过滤器
// 功能：添加审批状态过滤器的便捷方法
// 返回值：查询构建器实例（支持链式调用）
func (qb *QueryBuilder) WithApprovalStatusFilter() *QueryBuilder {
	return qb.AddFilter(&filters.ApprovalStatusFilter{})
}

// WithDocumentModuleFilter 添加文档模块过滤器
// 功能：添加文档模块过滤器的便捷方法
// 返回值：查询构建器实例（支持链式调用）
func (qb *QueryBuilder) WithDocumentModuleFilter() *QueryBuilder {
	return qb.AddFilter(&filters.DocumentModuleFilter{})
}

// WithUserNicknameFilter 添加用户昵称过滤器
// 功能：添加用户昵称过滤器的便捷方法
// 参数：svcCtx - 服务上下文
// 返回值：查询构建器实例（支持链式调用）
func (qb *QueryBuilder) WithUserNicknameFilter(svcCtx *svc.ServiceContext) *QueryBuilder {
	return qb.AddFilter(filters.NewUserNicknameFilter(svcCtx))
}

// WithDocumentNoFilter 添加文档编号过滤器
// 功能：添加文档编号过滤器的便捷方法
// 参数：svcCtx - 服务上下文
// 返回值：查询构建器实例（支持链式调用）
func (qb *QueryBuilder) WithDocumentNoFilter(svcCtx *svc.ServiceContext) *QueryBuilder {
	return qb.AddFilter(filters.NewDocumentNoFilter(svcCtx))
}

// WithDocumentNameFilter 添加文档名称过滤器
// 功能：添加文档名称过滤器的便捷方法
// 参数：svcCtx - 服务上下文
// 返回值：查询构建器实例（支持链式调用）
func (qb *QueryBuilder) WithDocumentNameFilter(svcCtx *svc.ServiceContext) *QueryBuilder {
	return qb.AddFilter(filters.NewDocumentNameFilter(svcCtx))
}

// WithDocumentCategoryFilter 添加文档类别过滤器
// 功能：添加文档类别过滤器的便捷方法
// 返回值：查询构建器实例（支持链式调用）
func (qb *QueryBuilder) WithDocumentCategoryFilter() *QueryBuilder {
	return qb.AddFilter(&filters.DocumentCategoryFilter{})
}

// WithAllFilters 添加所有过滤器
// 功能: 根据请求参数自动添加所有相关的过滤器
// 返回值: QueryBuilder实例，支持链式调用
func (qb *QueryBuilder) WithAllFilters() *QueryBuilder {
	// 添加审批状态过滤器
	if qb.req.ApprovalStatus != "" {
		qb.WithApprovalStatusFilter()
	}

	// 添加文档模块过滤器
	if qb.req.DocumentModule != "" {
		qb.WithDocumentModuleFilter()
	}

	// 添加用户昵称过滤器
	if qb.req.UserNickname != "" {
		qb.WithUserNicknameFilter(qb.svcCtx)
	}

	// 添加文档编号过滤器
	if qb.req.DocumentNo != "" {
		qb.WithDocumentNoFilter(qb.svcCtx)
	}

	// 添加文档名称过滤器
	if qb.req.DocumentName != "" {
		qb.WithDocumentNameFilter(qb.svcCtx)
	}

	// 添加文档类别过滤器
	if qb.req.DocumentCategoryId != "" {
		qb.WithDocumentCategoryFilter()
	}

	return qb
}

// Build 构建最终的查询参数
// 功能: 应用所有过滤器并构建查询参数
// 返回值: 构建好的查询参数, 是否为空结果, 错误信息
func (qb *QueryBuilder) Build() (*mapper.PageBorrowRecordReq, bool, error) {
	// 应用所有过滤器
	isEmpty, err := qb.filterChain.Apply(qb.ctx, qb.req, &qb.pageReq)
	if err != nil {
		return nil, false, err
	}

	return &qb.pageReq, isEmpty, nil
}

// Reset 重置构建器
// 功能: 清空所有过滤器和参数，重新开始构建
func (qb *QueryBuilder) Reset() *QueryBuilder {
	qb.filterChain.Clear()
	qb.pageReq = mapper.PageBorrowRecordReq{}
	return qb
}

// GetFilterCount 获取过滤器数量
// 功能：返回当前构建器中过滤器的数量
// 返回值：过滤器数量
func (qb *QueryBuilder) GetFilterCount() int {
	return qb.filterChain.Size()
}