package query

import (
	"context"

	"nebula/internal/domain/query/assemblers"
	"nebula/internal/domain/query/builders"
	"nebula/internal/domain/query/filters"
	"nebula/internal/domain/query/services"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
)

// QueryService 统一查询服务接口
// 功能：对外提供统一的查询服务，封装内部的查询组件
type QueryService struct {
	documentQueryService   *services.DocumentQueryService
	concurrentQueryService *services.ConcurrentQueryService
	loanRecordAssembler    *assemblers.LoanRecordAssembler
	queryBuilder           *builders.QueryBuilder
	filterChain            *filters.FilterChain
	svcCtx                 *svc.ServiceContext
}

// NewQueryService 创建查询服务
// 功能：创建统一查询服务实例，初始化所有查询组件
// 参数：svcCtx - 服务上下文
// 返回值：查询服务实例
func NewQueryService(svcCtx *svc.ServiceContext) *QueryService {
	return &QueryService{
		documentQueryService:   services.NewDocumentQueryService(svcCtx),
		concurrentQueryService: services.NewConcurrentQueryService(svcCtx),
		loanRecordAssembler:    assemblers.NewLoanRecordAssembler(),
		filterChain:            filters.NewFilterChain(),
		svcCtx:                 svcCtx,
	}
}

// GetLoanRecords 获取借阅记录
// 功能：统一的借阅记录查询入口，整合所有查询逻辑
// 参数：ctx - 上下文，req - 查询请求
// 返回值：借阅记录响应，错误信息
// 异常：参数验证失败、数据库查询失败、数据组装失败
func (qs *QueryService) GetLoanRecords(ctx context.Context, req *types.GetLoanRecordsReq) (*types.GetLoanRecordsResp, error) {
	// 实现步骤：
	// 1. 构建分页查询请求
	// 2. 设置过滤器链
	// 3. 应用过滤器
	// 4. 执行数据库查询
	// 5. 并发查询关联数据
	// 6. 组装响应数据

	// 1. 构建分页查询请求
	pageReq := &mapper.PageBorrowRecordReq{
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		NoPage:   req.NoPage,
	}

	// 2. 设置过滤器链
	qs.setupFilterChain()

	// 3. 应用过滤器
	isEmpty, err := qs.filterChain.Apply(ctx, req, pageReq)
	if err != nil {
		return nil, err
	}
	if isEmpty {
		return &types.GetLoanRecordsResp{
			Data:  []types.LoanRecord{},
			Total: 0,
		}, nil
	}

	// 4. 执行数据库查询
	borrowerClient := mapper.NewBorrowRecordClient(qs.svcCtx.DocvaultDB)
	borrowerRecords, total, err := borrowerClient.GetBorrowRecordStatistics(ctx, *pageReq)
	if err != nil {
		return nil, err
	}

	if len(borrowerRecords) == 0 {
		return &types.GetLoanRecordsResp{
			Data:  []types.LoanRecord{},
			Total: int64(total),
		}, nil
	}

	// 5. 并发查询关联数据
	queryResult, err := qs.concurrentQueryService.QueryConcurrently(ctx, borrowerRecords)
	if err != nil {
		return nil, err
	}

	// 组装最终响应数据
	assemblerQueryResult := &assemblers.QueryResult{
		UserNicknames:  queryResult.UserNicknames,
		DocumentCounts: queryResult.DocumentCounts,
	}

	return qs.loanRecordAssembler.AssembleWithPagination(borrowerRecords, assemblerQueryResult, int(total)), nil
}

// setupFilterChain 设置过滤器链
// 功能：配置过滤器链，添加所有需要的过滤器
func (qs *QueryService) setupFilterChain() {
	// 实现步骤：
	// 1. 清空现有过滤器
	// 2. 按顺序添加过滤器

	// 1. 清空现有过滤器
	qs.filterChain.Clear()

	// 2. 按顺序添加过滤器
	qs.filterChain.
		AddFilter(&filters.ApprovalStatusFilter{}).
		AddFilter(&filters.DocumentModuleFilter{}).
		AddFilter(filters.NewUserNicknameFilter(qs.svcCtx)).
		AddFilter(&filters.DocumentCategoryFilter{}).
		AddFilter(filters.NewDocumentNoFilter(qs.svcCtx)).
		AddFilter(filters.NewDocumentNameFilter(qs.svcCtx))
}

// QueryDocumentIDsByNo 根据文档编号查询文档ID
// 功能：对外提供文档编号查询接口
// 参数：ctx - 上下文，documentNo - 文档编号，moduleType - 模块类型
// 返回值：文档ID列表，错误信息
func (qs *QueryService) QueryDocumentIDsByNo(ctx context.Context, documentNo, moduleType string) ([]string, error) {
	return qs.documentQueryService.QueryDocumentIDsByNo(ctx, documentNo, moduleType)
}

// QueryDocumentIDsByName 根据文档名称查询文档ID
// 功能：对外提供文档名称查询接口
// 参数：ctx - 上下文，documentName - 文档名称，moduleType - 模块类型
// 返回值：文档ID列表，错误信息
func (qs *QueryService) QueryDocumentIDsByName(ctx context.Context, documentName, moduleType string) ([]string, error) {
	return qs.documentQueryService.QueryDocumentIDsByName(ctx, documentName, moduleType)
}

// QueryConcurrently 并发查询
// 功能：对外提供并发查询接口
// 参数：ctx - 上下文，borrowerRecords - 借阅记录列表
// 返回值：查询结果，错误信息
func (qs *QueryService) QueryConcurrently(ctx context.Context, borrowerRecords []mapper.BorrowRecordView) (*services.QueryResult, error) {
	return qs.concurrentQueryService.QueryConcurrently(ctx, borrowerRecords)
}
