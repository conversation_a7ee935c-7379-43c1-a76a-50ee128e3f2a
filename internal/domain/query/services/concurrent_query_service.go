package services

import (
	"context"
	"sync"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
)

// CollectedData 收集的数据结构
// 功能：存储从借阅记录中收集的需要查询的数据
type CollectedData struct {
	UserIDs         []string // 用户ID列表
	BorrowRecordIDs []string // 借阅记录ID列表
}

// QueryResult 查询结果结构
// 功能：存储并发查询的结果
type QueryResult struct {
	UserNicknames  map[string]string                              // 用户昵称映射
	DocumentCounts map[string]*mapper.BorrowRecordDocumentCount // 文档数量统计映射
}

// ConcurrentQueryService 并发查询服务
// 功能：处理用户昵称和文档统计的并发查询
type ConcurrentQueryService struct {
	svcCtx *svc.ServiceContext
}

// NewConcurrentQueryService 创建并发查询服务
// 功能：创建并发查询服务实例
// 参数：svcCtx - 服务上下文
// 返回值：并发查询服务实例
func NewConcurrentQueryService(svcCtx *svc.ServiceContext) *ConcurrentQueryService {
	return &ConcurrentQueryService{
		svcCtx: svcCtx,
	}
}

// QueryConcurrently 并发查询用户昵称和文档数量统计
// 功能：使用协程并发查询用户昵称和文档数量统计
// 参数：ctx - 上下文，borrowerRecords - 借阅记录列表
// 返回值：查询结果，错误信息
// 异常：数据库查询失败、并发执行错误
func (s *ConcurrentQueryService) QueryConcurrently(ctx context.Context, borrowerRecords []mapper.BorrowRecordView) (*QueryResult, error) {
	// 实现步骤：
	// 1. 从借阅记录中收集需要查询的数据
	// 2. 启动两个协程分别查询用户昵称和文档数量统计
	// 3. 等待所有协程完成
	// 4. 检查错误并返回结果

	// 1. 收集需要查询的数据
	collectedData := s.collectDataFromRecords(borrowerRecords)

	// 2. 并发查询
	var wg sync.WaitGroup
	var userNicknames map[string]string
	var documentCounts map[string]*mapper.BorrowRecordDocumentCount
	var userErr, documentCountErr error

	// 协程1: 查询用户昵称
	if len(collectedData.UserIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			userClient := mapper.NewUserClient(s.svcCtx.PhoenixDB)
			userNicknames, userErr = userClient.BatchGetUserNicknames(ctx, collectedData.UserIDs)
		}()
	} else {
		userNicknames = make(map[string]string)
	}

	// 协程2: 查询文档数量统计
	if len(collectedData.BorrowRecordIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			borrowClient := mapper.NewBorrowRecordClient(s.svcCtx.DocvaultDB)
			documentCounts, documentCountErr = borrowClient.BatchGetDocumentCounts(ctx, collectedData.BorrowRecordIDs)
		}()
	} else {
		documentCounts = make(map[string]*mapper.BorrowRecordDocumentCount)
	}

	// 3. 等待所有协程完成
	wg.Wait()

	// 4. 检查错误
	if userErr != nil {
		return nil, userErr
	}
	if documentCountErr != nil {
		return nil, documentCountErr
	}

	return &QueryResult{
		UserNicknames:  userNicknames,
		DocumentCounts: documentCounts,
	}, nil
}

// collectDataFromRecords 从借阅记录中收集需要查询的数据
// 功能：提取借阅记录中的用户ID和借阅记录ID
// 参数：borrowerRecords - 借阅记录列表
// 返回值：收集到的数据结构
func (s *ConcurrentQueryService) collectDataFromRecords(borrowerRecords []mapper.BorrowRecordView) *CollectedData {
	// 实现步骤：
	// 1. 初始化数据收集容器
	// 2. 遍历借阅记录，收集用户ID和借阅记录ID
	// 3. 去重用户ID
	// 4. 返回收集到的数据

	userIDs := make([]string, 0)
	borrowRecordIDs := make([]string, 0, len(borrowerRecords))
	userIDSet := make(map[string]bool)

	for _, record := range borrowerRecords {
		// 收集借阅记录ID
		borrowRecordIDs = append(borrowRecordIDs, record.ID)

		// 收集用户ID（去重）
		if !userIDSet[record.UserID] {
			userIDs = append(userIDs, record.UserID)
			userIDSet[record.UserID] = true
		}
		
		// 收集回收用户ID（去重）
		if record.RecoverUserID != "" && !userIDSet[record.RecoverUserID] {
			userIDs = append(userIDs, record.RecoverUserID)
			userIDSet[record.RecoverUserID] = true
		}
	}

	return &CollectedData{
		UserIDs:         userIDs,
		BorrowRecordIDs: borrowRecordIDs,
	}
}

// QueryUserNicknames 查询用户昵称
// 功能：批量查询用户昵称
// 参数：ctx - 上下文，userIDs - 用户ID列表
// 返回值：用户ID到昵称的映射，错误信息
func (s *ConcurrentQueryService) QueryUserNicknames(ctx context.Context, userIDs []string) (map[string]string, error) {
	// 实现步骤：
	// 1. 检查用户ID列表是否为空
	// 2. 创建用户客户端
	// 3. 批量查询用户昵称
	// 4. 返回查询结果

	if len(userIDs) == 0 {
		return make(map[string]string), nil
	}

	userClient := mapper.NewUserClient(s.svcCtx.PhoenixDB)
	return userClient.BatchGetUserNicknames(ctx, userIDs)
}

// QueryDocumentCounts 查询文档数量统计
// 功能：批量查询借阅记录的文档数量统计
// 参数：ctx - 上下文，borrowRecordIDs - 借阅记录ID列表
// 返回值：借阅记录ID到文档数量统计的映射，错误信息
func (s *ConcurrentQueryService) QueryDocumentCounts(ctx context.Context, borrowRecordIDs []string) (map[string]*mapper.BorrowRecordDocumentCount, error) {
	// 实现步骤：
	// 1. 检查借阅记录ID列表是否为空
	// 2. 创建借阅记录客户端
	// 3. 批量查询文档数量统计
	// 4. 返回查询结果

	if len(borrowRecordIDs) == 0 {
		return make(map[string]*mapper.BorrowRecordDocumentCount), nil
	}

	borrowClient := mapper.NewBorrowRecordClient(s.svcCtx.DocvaultDB)
	return borrowClient.BatchGetDocumentCounts(ctx, borrowRecordIDs)
}