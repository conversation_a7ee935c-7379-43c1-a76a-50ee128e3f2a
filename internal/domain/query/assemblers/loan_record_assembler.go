package assemblers

import (
	"strings"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/types"
)

// LoanRecordAssembler 借阅记录组装器
// 功能：将借阅记录和查询结果组合成最终的响应数据
type LoanRecordAssembler struct{}

// NewLoanRecordAssembler 创建借阅记录组装器
// 功能：创建借阅记录组装器实例
// 返回值：借阅记录组装器实例
func NewLoanRecordAssembler() *LoanRecordAssembler {
	return &LoanRecordAssembler{}
}

// Assemble 组装借阅记录响应数据
// 功能：将借阅记录和查询结果组合成最终的响应数据
// 参数：borrowerRecords - 借阅记录列表，queryResult - 查询结果
// 返回值：格式化后的借阅记录列表
func (a *LoanRecordAssembler) Assemble(borrowerRecords []mapper.BorrowRecordView, queryResult *QueryResult) []types.LoanRecord {
	// 实现步骤：
	// 1. 按借阅记录ID分组
	// 2. 为每个借阅记录收集所有回收人信息
	// 3. 设置用户昵称、文档数量统计等信息
	// 4. 将回收人昵称用逗号连接

	// 1. 按借阅记录ID分组
	recordGroups := a.groupRecordsByID(borrowerRecords)

	// 2. 组装每个借阅记录
	loanRecords := make([]types.LoanRecord, 0, len(recordGroups))
	for recordID, records := range recordGroups {
		loanRecord := a.assembleSingleRecord(recordID, records, queryResult)
		loanRecords = append(loanRecords, loanRecord)
	}

	return loanRecords
}

// groupRecordsByID 按借阅记录ID分组
// 功能：将借阅记录按ID进行分组
// 参数：borrowerRecords - 借阅记录列表
// 返回值：按ID分组的借阅记录映射
func (a *LoanRecordAssembler) groupRecordsByID(borrowerRecords []mapper.BorrowRecordView) map[string][]mapper.BorrowRecordView {
	// 实现步骤：
	// 1. 创建分组映射
	// 2. 遍历借阅记录，按ID分组
	// 3. 返回分组结果

	recordGroups := make(map[string][]mapper.BorrowRecordView)
	for _, record := range borrowerRecords {
		recordGroups[record.ID] = append(recordGroups[record.ID], record)
	}
	return recordGroups
}

// assembleSingleRecord 组装单个借阅记录
// 功能：组装单个借阅记录的完整信息
// 参数：recordID - 借阅记录ID，records - 该记录的所有相关数据，queryResult - 查询结果
// 返回值：组装好的借阅记录
func (a *LoanRecordAssembler) assembleSingleRecord(recordID string, records []mapper.BorrowRecordView, queryResult *QueryResult) types.LoanRecord {
	// 实现步骤：
	// 1. 使用第一条记录作为基础信息
	// 2. 设置用户昵称
	// 3. 设置文档数量统计
	// 4. 收集并设置回收人信息
	// 5. 返回组装好的记录

	// 使用第一条记录作为基础信息（借阅记录的基本信息都相同）
	firstRecord := records[0]
	loanRecord := types.LoanRecord{
		Id:                firstRecord.ID,
		BorrowTime:        firstRecord.BorrowTime.UnixMilli(),
		DueTime:           firstRecord.DueTime.UnixMilli(),
		BorrowReason:      firstRecord.BorrowReason,
		ApprovalStatus:    firstRecord.ApprovalStatus,
		ApprovalApplyTime: firstRecord.ApprovalApplyTime.UnixMilli(),
	}

	// 设置用户昵称
	loanRecord.UserNickname = a.getUserNickname(firstRecord.UserID, queryResult.UserNicknames)

	// 设置文档数量统计
	a.setDocumentCounts(&loanRecord, recordID, queryResult.DocumentCounts)

	// 设置回收人信息
	loanRecord.RecoverNames = a.assembleRecoverNames(records, queryResult.UserNicknames)

	return loanRecord
}

// getUserNickname 获取用户昵称
// 功能：从用户昵称映射中获取指定用户的昵称
// 参数：userID - 用户ID，userNicknames - 用户昵称映射
// 返回值：用户昵称
func (a *LoanRecordAssembler) getUserNickname(userID string, userNicknames map[string]string) string {
	// 实现步骤：
	// 1. 从映射中查找用户昵称
	// 2. 如果找到则返回昵称，否则返回空字符串

	if nickname, exists := userNicknames[userID]; exists {
		return nickname
	}
	return ""
}

// setDocumentCounts 设置文档数量统计
// 功能：设置借阅记录的文档数量统计信息
// 参数：loanRecord - 借阅记录，recordID - 记录ID，documentCounts - 文档数量统计映射
func (a *LoanRecordAssembler) setDocumentCounts(loanRecord *types.LoanRecord, recordID string, documentCounts map[string]*mapper.BorrowRecordDocumentCount) {
	// 实现步骤：
	// 1. 从映射中查找文档数量统计
	// 2. 如果找到则设置统计信息，否则设置为0

	if documentCount, exists := documentCounts[recordID]; exists {
		loanRecord.RecoverDocumentsCount = documentCount.RecoverDocumentsCount
		loanRecord.BorrowDocumentsCount = documentCount.BorrowDocumentsCount
	} else {
		loanRecord.RecoverDocumentsCount = 0
		loanRecord.BorrowDocumentsCount = 0
	}
}

// assembleRecoverNames 组装回收人姓名
// 功能：收集所有回收人ID并组装成姓名字符串
// 参数：records - 借阅记录列表，userNicknames - 用户昵称映射
// 返回值：回收人姓名字符串（逗号分隔）
func (a *LoanRecordAssembler) assembleRecoverNames(records []mapper.BorrowRecordView, userNicknames map[string]string) string {
	// 实现步骤：
	// 1. 收集所有回收人ID（去重）
	// 2. 获取回收人昵称
	// 3. 用逗号连接所有昵称
	// 4. 返回连接后的字符串

	// 收集所有回收人ID（去重）
	recoverUserIDs := make(map[string]bool)
	for _, record := range records {
		if record.RecoverUserID != "" {
			recoverUserIDs[record.RecoverUserID] = true
		}
	}

	// 构建回收人姓名字符串
	recoverNames := make([]string, 0, len(recoverUserIDs))
	for userID := range recoverUserIDs {
		if nickname, exists := userNicknames[userID]; exists && nickname != "" {
			recoverNames = append(recoverNames, nickname)
		}
	}

	// 用逗号连接所有回收人姓名
	return strings.Join(recoverNames, ",")
}

// AssembleWithPagination 带分页信息的组装
// 功能：组装借阅记录并包含分页信息
// 参数：borrowerRecords - 借阅记录列表，queryResult - 查询结果，total - 总数
// 返回值：完整的响应数据
func (a *LoanRecordAssembler) AssembleWithPagination(borrowerRecords []mapper.BorrowRecordView, queryResult *QueryResult, total int) *types.GetLoanRecordsResp {
	// 实现步骤：
	// 1. 组装借阅记录列表
	// 2. 创建响应对象
	// 3. 设置总数和数据
	// 4. 返回响应对象

	loanRecords := a.Assemble(borrowerRecords, queryResult)

	return &types.GetLoanRecordsResp{
		Total: int64(total),
		Data:  loanRecords,
	}
}