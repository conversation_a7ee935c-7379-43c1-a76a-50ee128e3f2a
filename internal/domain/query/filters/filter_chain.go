package filters

import (
	"context"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/types"
)

// FilterChain 过滤器链
// 功能：使用责任链模式组织多个过滤器
type FilterChain struct {
	filters []FilterStrategy
}

// NewFilterChain 创建过滤器链
// 功能：创建过滤器链实例
// 返回值：过滤器链实例
func NewFilterChain() *FilterChain {
	return &FilterChain{
		filters: make([]FilterStrategy, 0),
	}
}

// AddFilter 添加过滤器
// 功能：向过滤器链中添加一个过滤器
// 参数：filter - 过滤器策略
// 返回值：过滤器链实例（支持链式调用）
func (fc *FilterChain) AddFilter(filter FilterStrategy) *FilterChain {
	// 实现步骤：
	// 1. 将过滤器添加到过滤器列表中
	// 2. 返回自身以支持链式调用

	fc.filters = append(fc.filters, filter)
	return fc
}

// Apply 应用所有过滤器
// 功能：按顺序应用所有过滤器，如果任何一个过滤器返回空结果，则停止执行
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
func (fc *FilterChain) Apply(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
	// 实现步骤：
	// 1. 遍历所有过滤器
	// 2. 依次应用每个过滤器
	// 3. 如果任何过滤器返回错误或空结果，立即返回
	// 4. 所有过滤器都成功应用后返回成功

	for _, filter := range fc.filters {
		isEmpty, err := filter.Apply(ctx, req, pageReq)
		if err != nil {
			return false, err
		}
		if isEmpty {
			return true, nil
		}
	}

	return false, nil
}

// Size 获取过滤器数量
// 功能：返回过滤器链中过滤器的数量
// 返回值：过滤器数量
func (fc *FilterChain) Size() int {
	return len(fc.filters)
}

// Clear 清空过滤器链
// 功能：清空过滤器链中的所有过滤器
func (fc *FilterChain) Clear() {
	fc.filters = fc.filters[:0]
}