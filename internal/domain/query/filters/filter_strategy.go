package filters

import (
	"context"
	"strconv"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
)

// FilterStrategy 过滤策略接口
// 功能：定义过滤器的统一接口
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
type FilterStrategy interface {
	Apply(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (isEmpty bool, err error)
}

// ApprovalStatusFilter 审批状态过滤器
type ApprovalStatusFilter struct{}

// Apply 应用审批状态过滤
// 功能：将字符串类型的审批状态转换为整数指针类型并应用过滤
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
func (f *ApprovalStatusFilter) Apply(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
	// 实现步骤：
	// 1. 检查审批状态参数是否为空
	// 2. 转换字符串为整数
	// 3. 设置到分页请求参数中

	if req.ApprovalStatus == "" {
		return false, nil
	}

	status, err := strconv.Atoi(req.ApprovalStatus)
	if err != nil {
		return false, nil // 忽略转换错误，不应用过滤
	}

	pageReq.ApprovalStatus = &status
	return false, nil
}

// DocumentModuleFilter 文档模块过滤器
type DocumentModuleFilter struct{}

// Apply 应用文档模块过滤
// 功能：将字符串类型的文档模块转换为整数指针类型并应用过滤
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
func (f *DocumentModuleFilter) Apply(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
	// 实现步骤：
	// 1. 检查文档模块参数是否为空
	// 2. 转换字符串为整数
	// 3. 设置到分页请求参数中

	if req.DocumentModule == "" {
		return false, nil
	}

	moduleType, err := strconv.Atoi(req.DocumentModule)
	if err != nil {
		return false, nil // 忽略转换错误，不应用过滤
	}

	pageReq.ModuleType = &moduleType
	return false, nil
}

// UserNicknameFilter 用户昵称过滤器
type UserNicknameFilter struct {
	svcCtx *svc.ServiceContext
}

// NewUserNicknameFilter 创建用户昵称过滤器
// 功能：创建用户昵称过滤器实例
// 参数：svcCtx - 服务上下文
// 返回值：用户昵称过滤器实例
func NewUserNicknameFilter(svcCtx *svc.ServiceContext) *UserNicknameFilter {
	return &UserNicknameFilter{
		svcCtx: svcCtx,
	}
}

// Apply 应用用户昵称过滤
// 功能：根据用户昵称模糊查询获取用户ID并应用过滤
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
func (f *UserNicknameFilter) Apply(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
	// 实现步骤：
	// 1. 检查用户昵称参数是否为空
	// 2. 根据昵称查询用户ID列表
	// 3. 检查是否找到匹配的用户
	// 4. 设置用户ID到分页请求参数中

	if req.UserNickname == "" {
		return false, nil
	}

	userClient := mapper.NewUserClient(f.svcCtx.PhoenixDB)
	userIDs, err := userClient.GetUserIDsByNickname(ctx, req.UserNickname)
	if err != nil {
		return false, err
	}

	// 如果没有找到匹配的用户，返回空结果标识
	if len(userIDs) == 0 {
		return true, nil
	}

	// 只取第一个匹配的用户ID
	pageReq.UserID = userIDs[0]
	return false, nil
}

// DocumentCategoryFilter 文档类别过滤器
type DocumentCategoryFilter struct{}

// Apply 应用文档类别过滤
// 功能：应用文档类别ID过滤
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
func (f *DocumentCategoryFilter) Apply(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
	// 实现步骤：
	// 1. 检查文档类别ID参数是否为空
	// 2. 设置到分页请求参数中

	if req.DocumentCategoryId == "" {
		return false, nil
	}

	pageReq.DocumentCategoryId = req.DocumentCategoryId
	return false, nil
}