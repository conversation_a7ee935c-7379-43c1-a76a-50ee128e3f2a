package filters

import (
	"context"

	"nebula/internal/domain/query/services"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
)

// DocumentNoFilter 文档编号过滤器
type DocumentNoFilter struct {
	documentQueryService *services.DocumentQueryService
}

// NewDocumentNoFilter 创建文档编号过滤器
// 功能：创建文档编号过滤器实例
// 参数：svcCtx - 服务上下文
// 返回值：文档编号过滤器实例
func NewDocumentNoFilter(svcCtx *svc.ServiceContext) *DocumentNoFilter {
	return &DocumentNoFilter{
		documentQueryService: services.NewDocumentQueryService(svcCtx),
	}
}

// Apply 应用文档编号过滤
// 功能：根据文档编号模糊查询获取文档ID列表并应用过滤
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
func (f *DocumentNoFilter) Apply(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
	// 实现步骤：
	// 1. 检查文档编号参数是否为空
	// 2. 查询文档ID列表
	// 3. 检查是否找到匹配的文档
	// 4. 设置文档ID列表到分页请求参数中

	if req.DocumentNo == "" {
		return false, nil
	}

	// 查询文档ID列表
	documentIDs, err := f.documentQueryService.QueryDocumentIDsByNo(ctx, req.DocumentNo, req.DocumentModule)
	if err != nil {
		return false, err
	}

	// 如果没有找到匹配的文档，返回空结果标识
	if len(documentIDs) == 0 {
		return true, nil
	}

	pageReq.DocumentIDs = documentIDs
	return false, nil
}

// DocumentNameFilter 文档名称过滤器
type DocumentNameFilter struct {
	documentQueryService *services.DocumentQueryService
}

// NewDocumentNameFilter 创建文档名称过滤器
// 功能：创建文档名称过滤器实例
// 参数：svcCtx - 服务上下文
// 返回值：文档名称过滤器实例
func NewDocumentNameFilter(svcCtx *svc.ServiceContext) *DocumentNameFilter {
	return &DocumentNameFilter{
		documentQueryService: services.NewDocumentQueryService(svcCtx),
	}
}

// Apply 应用文档名称过滤
// 功能：根据文档名称模糊查询获取文档ID列表并应用过滤
// 参数：ctx - 上下文，req - 原始请求，pageReq - 分页请求参数
// 返回值：是否为空结果，错误信息
func (f *DocumentNameFilter) Apply(ctx context.Context, req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
	// 实现步骤：
	// 1. 检查文档名称参数是否为空
	// 2. 查询文档ID列表
	// 3. 检查是否找到匹配的文档
	// 4. 处理与现有文档ID列表的交集
	// 5. 设置文档ID列表到分页请求参数中

	if req.DocumentName == "" {
		return false, nil
	}

	// 查询文档ID列表
	documentIDs, err := f.documentQueryService.QueryDocumentIDsByName(ctx, req.DocumentName, req.DocumentModule)
	if err != nil {
		return false, err
	}

	// 如果没有找到匹配的文档，返回空结果标识
	if len(documentIDs) == 0 {
		return true, nil
	}

	// 如果已经有 DocumentIDs，则取交集
	if len(pageReq.DocumentIDs) > 0 {
		intersectionIDs := f.documentQueryService.IntersectDocumentIDs(pageReq.DocumentIDs, documentIDs)
		if len(intersectionIDs) == 0 {
			return true, nil
		}
		pageReq.DocumentIDs = intersectionIDs
	} else {
		pageReq.DocumentIDs = documentIDs
	}

	return false, nil
}