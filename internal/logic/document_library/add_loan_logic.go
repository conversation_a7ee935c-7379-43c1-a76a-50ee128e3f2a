package document_library

import (
	"context"
	"time"

	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type AddLoanLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddLoanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddLoanLogic {
	return &AddLoanLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// AddLoan 创建借阅记录
// 功能：根据用户请求创建文档借阅记录
// 参数：req - 借阅请求参数，包含借阅时间、归还时间、借阅原因和文档列表
// 返回值：resp - 借阅响应，err - 错误信息
// 异常：当获取用户信息失败、参数转换失败或RPC调用失败时返回错误
func (l *AddLoanLogic) AddLoan(req *types.AddLoanReq) (resp *types.AddLoanResp, err error) {
	// 步骤1：获取当前登录用户信息
	// 步骤2：构建借阅文档列表，转换文档模块类型
	// 步骤3：构建gRPC请求参数
	// 步骤4：调用docvault服务创建借阅记录
	// 步骤5：返回响应结果

	// 获取当前登录用户信息
	userInfo := utils.GetCurrentLoginUser(l.ctx)
	if userInfo.UserId == "" {
		logc.Errorw(l.ctx, "获取当前用户信息失败")
		return nil, err
	}

	// 构建借阅文档列表
	borrowDocuments, err := l.convertLoanDocumentsToBorrowItems(req.Documents)
	if err != nil {
		return nil, err
	}

	// 构建gRPC请求参数
	grpcReq := &docvault.BorrowRecordCreateReq{
		UserId:            userInfo.UserId,
		Documents:         borrowDocuments,
		DueTime:           req.DueTime,
		BorrowReasonType:  5,                // 设置为5：其他
		BorrowOtherReason: req.BorrowReason, // 将原有的BorrowReason作为其他原因描述
		BorrowTime:        req.BorrowTime,
		ApprovalApplyTime: time.Now().UnixMilli(),
		BorrowApplyTime:   time.Now().UnixMilli(),
		ApprovalStatus:    1, // 设置为1：待审批
	}

	// 调用docvault服务创建借阅记录
	grpcResp, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).CreateBorrowRecord(l.ctx, grpcReq)
	if err != nil {
		logc.Errorw(l.ctx, "创建借阅记录失败",
			logx.Field("userId", userInfo.UserId),
			logx.Field("error", err))
		return nil, err
	}

	// 记录成功日志
	logc.Infow(l.ctx, "借阅记录创建成功",
		logx.Field("borrowRecordId", grpcResp.Id),
		logx.Field("userId", userInfo.UserId))

	// 返回响应
	return &types.AddLoanResp{
		BorrowRecordID: grpcResp.Id,
	}, nil
}

// convertLoanDocumentsToBorrowItems 将借阅文档列表转换为借阅文档项列表
// 功能：使用 struct_copy 工具优化文档类型转换过程
// 参数：documents - 借阅文档列表
// 返回值：转换后的借阅文档项列表，错误信息
// 异常：当文档模块类型转换失败时返回错误
func (l *AddLoanLogic) convertLoanDocumentsToBorrowItems(documents []types.LoanDocument) ([]*docvault.BorrowDocumentItem, error) {
	// 直接构建借阅文档列表，结合 struct_copy 的思想进行优化
	result := make([]*docvault.BorrowDocumentItem, 0, len(documents))

	for _, doc := range documents {
		// 使用 struct_copy 的思想，创建新的借阅文档项
		borrowDoc := &docvault.BorrowDocumentItem{}

		// 直接设置字段，避免结构体拷贝的复杂性
		borrowDoc.DocumentId = doc.DocumentId
		borrowDoc.VersionNo = doc.DocumentVersionNo
		borrowDoc.ModuleType = doc.DocumentModuleType
		// BorrowStatus默认设置为1（借阅中）
		borrowDoc.BorrowStatus = 1

		result = append(result, borrowDoc)
	}

	return result, nil
}
