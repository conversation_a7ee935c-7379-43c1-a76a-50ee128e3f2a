package document_library

import (
	"context"
	"testing"

	"nebula/internal/svc"
	"nebula/internal/types"

	. "github.com/smartystreets/goconvey/convey"
)

func TestGetInternalPaperDownloadRecords(t *testing.T) {
	Convey("测试获取内发纸质文件一次下载变更记录", t, func() {
		// 创建测试上下文
		ctx := context.Background()

		// 创建模拟的服务上下文
		// 注意：这里需要根据实际情况配置数据库连接
		svcCtx := &svc.ServiceContext{
			// DocvaultDB: mockDB, // 这里应该使用模拟的数据库
		}

		// 创建逻辑实例
		logic := NewGetInternalPaperDownloadRecordsLogic(ctx, svcCtx)

		Convey("当文档ID为空时", func() {
			req := &types.GetInternalPaperDownloadRecordsReq{
				DocumentID: "",
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
			}

			resp, err := logic.GetInternalPaperDownloadRecords(req)

			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldC<PERSON>ainSubstring, "文档ID不能为空")
			So(resp, ShouldBeNil)
		})

		Convey("当文档ID有效时", func() {
			// 注意：这个测试需要模拟数据库，暂时跳过实际数据库操作
			SkipConvey("需要模拟数据库连接", func() {
				req := &types.GetInternalPaperDownloadRecordsReq{
					DocumentID: "test-doc-id",
					PageInfo: types.PageInfo{
						Page:     1,
						PageSize: 10,
					},
				}

				resp, err := logic.GetInternalPaperDownloadRecords(req)

				So(err, ShouldBeNil)
				So(resp, ShouldNotBeNil)
				So(resp.Data, ShouldNotBeNil)
				So(resp.PageInfo.Page, ShouldEqual, 1)
				So(resp.PageInfo.PageSize, ShouldEqual, 10)
			})
		})
	})
}

func TestGetPaperFileStatus(t *testing.T) {
	Convey("测试获取纸质文件状态", t, func() {
		logic := &GetInternalPaperDownloadRecordsLogic{}

		Convey("当处置状态为1（未回收）时", func() {
			status := logic.getPaperFileStatus(1)
			So(status, ShouldEqual, 1)
		})

		Convey("当处置状态为2（回收审批中）时", func() {
			status := logic.getPaperFileStatus(2)
			So(status, ShouldEqual, 2)
		})

		Convey("当处置状态为3（已回收）时", func() {
			status := logic.getPaperFileStatus(3)
			So(status, ShouldEqual, 3)
		})

		Convey("当处置状态为4（处置审批中）时", func() {
			status := logic.getPaperFileStatus(4)
			So(status, ShouldEqual, 3)
		})

		Convey("当处置状态为5（已处置）时", func() {
			status := logic.getPaperFileStatus(5)
			So(status, ShouldEqual, 3)
		})
	})
}

func TestGetFileDisposeStatus(t *testing.T) {
	Convey("测试获取文件处置状态", t, func() {
		logic := &GetInternalPaperDownloadRecordsLogic{}

		Convey("当处置状态为1（未回收）时", func() {
			status := logic.getFileDisposeStatus(1)
			So(status, ShouldEqual, 1) // 未处置
		})

		Convey("当处置状态为4（处置审批中）时", func() {
			status := logic.getFileDisposeStatus(4)
			So(status, ShouldEqual, 2) // 处置中
		})

		Convey("当处置状态为5（已处置）时", func() {
			status := logic.getFileDisposeStatus(5)
			So(status, ShouldEqual, 3) // 已处置
		})
	})
}

func TestSortRecordsByDistributeApplyTimeDesc(t *testing.T) {
	Convey("测试按发放申请时间倒序排列", t, func() {
		logic := &GetInternalPaperDownloadRecordsLogic{}

		records := []types.InternalPaperDownloadRecord{
			{RecordID: "1", DistributeApplyTime: 1000},
			{RecordID: "2", DistributeApplyTime: 3000},
			{RecordID: "3", DistributeApplyTime: 2000},
		}

		logic.sortRecordsByDistributeApplyTimeDesc(records)

		So(records[0].RecordID, ShouldEqual, "2") // 时间最新的排在前面
		So(records[1].RecordID, ShouldEqual, "3")
		So(records[2].RecordID, ShouldEqual, "1") // 时间最早的排在后面
	})
}

func TestGetUserNickname(t *testing.T) {
	Convey("测试获取用户昵称", t, func() {
		// 注意：这个测试需要模拟QuickNameTranslator，暂时跳过实际调用
		SkipConvey("需要模拟QuickNameTranslator", func() {
			ctx := context.Background()
			svcCtx := &svc.ServiceContext{
				// QuickNameTranslator: mockTranslator,
			}
			logic := &GetInternalPaperDownloadRecordsLogic{
				ctx:    ctx,
				svcCtx: svcCtx,
			}

			Convey("当用户ID为空时", func() {
				nickname := logic.getUserNickname("")
				So(nickname, ShouldEqual, "")
			})

			Convey("当用户ID有效时", func() {
				// 这里需要模拟QuickNameTranslator的返回值
				nickname := logic.getUserNickname("user123")
				So(nickname, ShouldNotBeEmpty)
			})
		})
	})
}
