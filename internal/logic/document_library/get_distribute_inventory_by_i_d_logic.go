package document_library

import (
	"context"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDistributeInventoryByIDLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDistributeInventoryByIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDistributeInventoryByIDLogic {
	return &GetDistributeInventoryByIDLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDistributeInventoryByIDLogic) GetDistributeInventoryByID(req *types.GetDistributeInventoryReq) (resp *types.GetDistributeInventoryResp, err error) {
	distributeApplication, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).
		GetDistributeApplicationById(l.ctx, &docvault.GetDistributeApplicationReq{Id: req.ID})
	if err != nil {
		l.Logger.Errorf("获取发放清单信息失败：%v", err)
		return nil, err
	}

	// 数据转换
	distributeInventories, disposalStatus := l.dataConversion(distributeApplication.Data)

	return &types.GetDistributeInventoryResp{
		DisposalStatus: disposalStatus,
		Data:           distributeInventories,
	}, nil
}

func (l *GetDistributeInventoryByIDLogic) dataConversion(inventories []*docvault.DistributeInventory) ([]types.DistributeInventory, string) {
	if len(inventories) == 0 {
		return nil, ""
	}
	var distributeInventories []types.DistributeInventory
	sumRecycleCount := 0  // 总回收份数
	sumDisposalCount := 0 // 总处置份数
	for _, inventory := range inventories {
		// 电子文件-查阅
		eFileLook := types.PermissionResp{
			FileForm:       1,
			FilePermission: 1,
		}
		// 电子文件-查阅/下载
		eFileLookAndDownload := types.PermissionResp{
			FileForm:       1,
			FilePermission: 2,
		}
		// 纸质文件-一次下载
		paperDocumentOnceDownload := types.PermissionResp{
			FileForm:       2,
			FilePermission: 3,
		}
		// 电子文件-一次下载
		eFileOnceDownload := types.PermissionResp{
			FileForm:       1,
			FilePermission: 3,
		}
		// 拆开，加一个字段，告诉前端 内发：1电子文件-查阅 | 内发：2电子文件-查阅/下载 | 内发：3纸质文件-一次下载 | 外发：4电子文件-一次下载
		// 1电子文件 | 2纸质文件
		// 1查阅 | 2查阅/下载 | 3一次下载
		for _, permission := range inventory.Permissions {
			var receivedBy []types.ReceivedBy
			distributeCount := len(receivedBy) // 发放份数
			recycleCount := 0                  // 回收份数
			disposalCount := 0                 // 处置份数
			for _, user := range permission.ReceivedBy {
				receivedBy = append(receivedBy, types.ReceivedBy{
					UserID:      user.UserId,
					Nickname:    user.Nickname,
					Status:      int(user.Status),
					RecycleDate: user.RecycleDate,
				})
				// 纸质文件一次下载，记录回收份数和处置份数
				if permission.FileForm == 2 && permission.FilePermission == 3 {
					if user.Status >= 3 {
						recycleCount++
					}
					if user.Status == 5 {
						disposalCount++
					}
				}
			}
			sumRecycleCount += recycleCount
			sumDisposalCount += disposalCount
			// 电子文件-查阅
			if permission.FileForm == 1 && permission.FilePermission == 1 {
				eFileLook.ReceivedBy = receivedBy
			}
			// 电子文件-查阅/下载
			if permission.FileForm == 1 && permission.FilePermission == 2 {
				eFileLookAndDownload.ReceivedBy = receivedBy
			}
			// 纸质文件-一次下载
			if permission.FileForm == 2 && permission.FilePermission == 3 {
				paperDocumentOnceDownload.ReceivedBy = receivedBy
				paperDocumentOnceDownload.DisposalCount = disposalCount
				paperDocumentOnceDownload.RecycleCount = recycleCount
				paperDocumentOnceDownload.DistributeCount = distributeCount
			}
			// 电子文件-一次下载
			if permission.FileForm == 1 && permission.FilePermission == 3 {
				eFileOnceDownload.ReceivedBy = receivedBy
			}
		}
		distributeInventories = append(distributeInventories, types.DistributeInventory{
			ID:                        inventory.Id,
			FileId:                    inventory.FileId,
			FileName:                  inventory.FileName,
			Number:                    inventory.Number,
			Version:                   inventory.Version,
			EFileLook:                 eFileLook,
			EFileLookAndDownload:      eFileLookAndDownload,
			PaperDocumentOnceDownload: paperDocumentOnceDownload,
			EFileOnceDownload:         eFileOnceDownload,
			Recipient:                 inventory.Recipient,
		})
	}
	disposalStatus := ""
	if sumDisposalCount == 0 {
		disposalStatus = "未处置"
	} else if sumDisposalCount > 0 && sumDisposalCount < sumRecycleCount {
		disposalStatus = "部分处置"
	} else {
		disposalStatus = "已处置"
	}
	return distributeInventories, disposalStatus
}
