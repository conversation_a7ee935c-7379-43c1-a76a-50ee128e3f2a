package document_library

import (
	"context"
	"testing"
	"time"

	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/zrpc"

	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
)

// DeleteTestData 删除测试数据结构
type DeleteTestData struct {
	BorrowRecords           []BorrowRecord
	BorrowDocumentRelations []BorrowDocumentRelation
	Users                   []mapper.User
	InternalDocuments       []mapper.InternalDocumentLibrary
	ExternalDocuments       []mapper.ExternalDocumentLibrary
}

// setupDeleteTestEnvironment 设置删除测试环境
// 功能：创建数据库连接和服务上下文
// 返回值：服务上下文、清理函数、错误信息
func setupDeleteTestEnvironment() (*svc.ServiceContext, func(), error) {
	// 直接读取配置文件
	var testConfig config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &testConfig)

	// 初始化数据库连接
	docvaultDB := mapper.NewDocvaultDB(testConfig)
	phoenixDB := mapper.NewPhoenixDB(testConfig)
	nebulaDB := mapper.NewNebulaDB(testConfig)

	// 创建gRPC连接
	docvaultRpcConn := zrpc.MustNewClient(testConfig.DocvaultRPC).Conn()

	// 创建服务上下文
	svcCtx := &svc.ServiceContext{
		Config:          testConfig,
		DocvaultDB:      docvaultDB,
		PhoenixDB:       phoenixDB,
		NebulaDB:        nebulaDB,
		DocvaultRpcConn: docvaultRpcConn,
	}

	// 先清理可能存在的测试数据
	cleanupDeleteTestData(svcCtx)

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupDeleteTestData(svcCtx)
		// 关闭gRPC连接
		if docvaultRpcConn != nil {
			docvaultRpcConn.Close()
		}
	}

	return svcCtx, cleanup, nil
}

// cleanupDeleteTestData 清理删除测试数据
// 功能：删除测试过程中创建的数据
// 参数：svcCtx - 服务上下文
func cleanupDeleteTestData(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除测试借阅文档关系
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("borrow_record_id LIKE ?", "test_delete_%").Delete(&BorrowDocumentRelation{})
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_delete_%").Delete(&BorrowDocumentRelation{})

	// 删除测试借阅记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_delete_%").Delete(&BorrowRecord{})

	// 删除测试内部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_delete_%").Delete(&mapper.InternalDocumentLibrary{})

	// 删除测试外部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_delete_%").Delete(&mapper.ExternalDocumentLibrary{})

	// 删除测试用户（使用硬删除）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_delete_%").Delete(&mapper.User{})
}

// createDeleteTestData 创建删除测试数据
// 功能：在数据库中创建测试所需的数据
// 参数：svcCtx - 服务上下文
// 返回值：测试数据结构、错误信息
func createDeleteTestData(svcCtx *svc.ServiceContext) (*DeleteTestData, error) {
	ctx := context.Background()
	now := time.Now()

	// 1. 创建测试用户
	email1 := "<EMAIL>"
	email2 := "<EMAIL>"
	users := []mapper.User{
		{
			ID:        "test_delete_user_001",
			Nickname:  "删除测试用户1",
			Username:  "deletetestuser1",
			Email:     &email1,
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
		{
			ID:        "test_delete_user_002",
			Nickname:  "删除测试用户2",
			Username:  "deletetestuser2",
			Email:     &email2,
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, err
		}
	}

	// 2. 创建测试内部文档
	internalDocs := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_delete_internal_doc_001",
			No:             "DEL-INT-001",
			Name:           "删除测试内部文档1",
			VersionNo:      1,
			Status:         1,
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_delete_org_001",
			FileID:         "test_delete_file_001",
			DocCategoryID:  "test_delete_category_001",
			DepartmentID:   "test_delete_dept_001",
			AuthorID:       "test_delete_user_001",
			CreatedBy:      "test_delete_user_001",
			UpdatedBy:      "test_delete_user_001",
		},
		{
			ID:             "test_delete_internal_doc_002",
			No:             "DEL-INT-002",
			Name:           "删除测试内部文档2",
			VersionNo:      1,
			Status:         1,
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_delete_org_001",
			FileID:         "test_delete_file_002",
			DocCategoryID:  "test_delete_category_001",
			DepartmentID:   "test_delete_dept_001",
			AuthorID:       "test_delete_user_001",
			CreatedBy:      "test_delete_user_001",
			UpdatedBy:      "test_delete_user_001",
		},
	}

	// 插入内部文档数据
	for _, doc := range internalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, err
		}
	}

	// 3. 创建测试借阅记录
	borrowRecords := []BorrowRecord{
		{
			ID:                "test_delete_borrow_001",
			UserID:            "test_delete_user_001",
			BorrowTime:        now.Add(-7 * 24 * time.Hour),
			DueTime:           now.Add(7 * 24 * time.Hour),
			BorrowReason:      "删除测试需要",
			ApprovalStatus:    1,
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-6 * 24 * time.Hour),
			CreatedAt:         now.Add(-7 * 24 * time.Hour),
			UpdatedAt:         now.Add(-6 * 24 * time.Hour),
			CreatedBy:         "test_delete_user_001",
			UpdatedBy:         "test_delete_user_001",
		},
		{
			ID:                "test_delete_borrow_002",
			UserID:            "test_delete_user_002",
			BorrowTime:        now.Add(-5 * 24 * time.Hour),
			DueTime:           now.Add(10 * 24 * time.Hour),
			BorrowReason:      "删除测试需要2",
			ApprovalStatus:    1,
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-4 * 24 * time.Hour),
			CreatedAt:         now.Add(-5 * 24 * time.Hour),
			UpdatedAt:         now.Add(-4 * 24 * time.Hour),
			CreatedBy:         "test_delete_user_002",
			UpdatedBy:         "test_delete_user_002",
		},
	}

	// 插入借阅记录数据
	for _, record := range borrowRecords {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&record).Error; err != nil {
			return nil, err
		}
	}

	// 4. 创建借阅文档关系
	borrowDocRelations := []BorrowDocumentRelation{
		{
			ID:             "test_delete_relation_001",
			BorrowRecordID: "test_delete_borrow_001",
			DocumentID:     "test_delete_internal_doc_001",
			VersionNo:      "V1.0",
			ModuleType:     2,
			BorrowStatus:   1,
			CreatedAt:      now.Add(-7 * 24 * time.Hour),
			UpdatedAt:      now.Add(-7 * 24 * time.Hour),
		},
		{
			ID:             "test_delete_relation_002",
			BorrowRecordID: "test_delete_borrow_001",
			DocumentID:     "test_delete_internal_doc_002",
			VersionNo:      "V1.0",
			ModuleType:     2,
			BorrowStatus:   1,
			CreatedAt:      now.Add(-7 * 24 * time.Hour),
			UpdatedAt:      now.Add(-7 * 24 * time.Hour),
		},
		{
			ID:             "test_delete_relation_003",
			BorrowRecordID: "test_delete_borrow_002",
			DocumentID:     "test_delete_internal_doc_001",
			VersionNo:      "V1.0",
			ModuleType:     2,
			BorrowStatus:   1,
			CreatedAt:      now.Add(-5 * 24 * time.Hour),
			UpdatedAt:      now.Add(-5 * 24 * time.Hour),
		},
	}

	// 插入借阅文档关系数据
	for _, relation := range borrowDocRelations {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, err
		}
	}

	return &DeleteTestData{
		BorrowRecords:           borrowRecords,
		BorrowDocumentRelations: borrowDocRelations,
		Users:                   users,
		InternalDocuments:       internalDocs,
		ExternalDocuments:       []mapper.ExternalDocumentLibrary{},
	}, nil
}

// TestDeleteLoan 测试删除借阅记录功能
func TestDeleteLoan(t *testing.T) {
	convey.Convey("测试删除借阅记录功能", t, func() {
		convey.Convey("测试正常删除借阅记录", func() {
			// 设置测试环境
			svcCtx, cleanup, err := setupDeleteTestEnvironment()
			convey.So(err, convey.ShouldBeNil)
			defer cleanup()

			// 创建测试数据
			testData, err := createDeleteTestData(svcCtx)
			convey.So(err, convey.ShouldBeNil)
			convey.So(testData, convey.ShouldNotBeNil)

			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), utils.UserIdKey, "test_delete_user_001")
			ctx = context.WithValue(ctx, "organizationId", "test_delete_org_001")

			// 创建logic实例
			logic := NewDeleteLoanLogic(ctx, svcCtx)

			// 准备删除请求
			req := &types.DeleteLoanReq{
				BorrowRecordId: "test_delete_borrow_001",
			}

			// 执行删除操作
			resp, err := logic.DeleteLoan(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)

			// 验证借阅记录已被删除
			var count int64
			svcCtx.DocvaultDB.GetDB().WithContext(ctx).Model(&BorrowRecord{}).Where("id = ?", "test_delete_borrow_001").Count(&count)
			convey.So(count, convey.ShouldEqual, 0)

			// 验证相关的借阅文档关系也被删除
			svcCtx.DocvaultDB.GetDB().WithContext(ctx).Model(&BorrowDocumentRelation{}).Where("borrow_record_id = ?", "test_delete_borrow_001").Count(&count)
			convey.So(count, convey.ShouldEqual, 0)
		})

		convey.Convey("测试删除不存在的借阅记录", func() {
			// 设置测试环境
			svcCtx, cleanup, err := setupDeleteTestEnvironment()
			convey.So(err, convey.ShouldBeNil)
			defer cleanup()

			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), utils.UserIdKey, "test_delete_user_001")

			// 创建logic实例
			logic := NewDeleteLoanLogic(ctx, svcCtx)

			// 准备删除请求（使用不存在的借阅记录ID）
			req := &types.DeleteLoanReq{
				BorrowRecordId: "non_existent_borrow_id",
			}

			// 执行删除操作
			resp, err := logic.DeleteLoan(req)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(resp, convey.ShouldBeNil)
		})

		convey.Convey("测试删除空ID的借阅记录", func() {
			// 设置测试环境
			svcCtx, cleanup, err := setupDeleteTestEnvironment()
			convey.So(err, convey.ShouldBeNil)
			defer cleanup()

			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), utils.UserIdKey, "test_delete_user_001")

			// 创建logic实例
			logic := NewDeleteLoanLogic(ctx, svcCtx)

			// 准备删除请求（使用空ID）
			req := &types.DeleteLoanReq{
				BorrowRecordId: "",
			}

			// 执行删除操作
			resp, err := logic.DeleteLoan(req)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(resp, convey.ShouldBeNil)
		})

		convey.Convey("测试删除其他用户的借阅记录", func() {
			// 设置测试环境
			svcCtx, cleanup, err := setupDeleteTestEnvironment()
			convey.So(err, convey.ShouldBeNil)
			defer cleanup()

			// 创建测试数据
			testData, err := createDeleteTestData(svcCtx)
			convey.So(err, convey.ShouldBeNil)
			convey.So(testData, convey.ShouldNotBeNil)

			// 创建带有不同用户ID的上下文（尝试删除其他用户的记录）
			ctx := context.WithValue(context.Background(), utils.UserIdKey, "test_delete_user_002")

			// 创建logic实例
			logic := NewDeleteLoanLogic(ctx, svcCtx)

			// 准备删除请求（尝试删除user_001的借阅记录）
			req := &types.DeleteLoanReq{
				BorrowRecordId: "test_delete_borrow_001",
			}

			// 执行删除操作
			resp, err := logic.DeleteLoan(req)
			// 根据实际业务逻辑，允许删除其他用户的记录
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)

			// 验证借阅记录已被删除
			var count int64
			svcCtx.DocvaultDB.GetDB().WithContext(ctx).Model(&BorrowRecord{}).Where("id = ?", "test_delete_borrow_001").Count(&count)
			convey.So(count, convey.ShouldEqual, 0)
		})
	})
}
