package document_library

import (
	"context"
	"fmt"
	"testing"
	"time"

	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/zrpc"
)

// TestAddLoan 测试创建借阅记录功能
func TestAddLoan(t *testing.T) {
	convey.Convey("测试创建借阅记录功能", t, func() {
		// 设置测试环境
		svcCtx, cleanup, err := setupTestEnvironmentForAddLoan()
		convey.So(err, convey.ShouldBeNil)
		defer cleanup()

		// 创建测试数据
		testData, err := createTestDataForAddLoan(svcCtx)
		convey.So(err, convey.ShouldBeNil)
		convey.So(testData, convey.ShouldNotBeNil)

		// 创建带有用户信息的上下文
		userInfo := &utils.UserLoginInfo{
			UserId:         "test_add_user_001",
			TenantId:       "test_tenant_001",
			OrganizationId: "test_org_001",
			DeviceKind:     1,
			IsVirtualUser:  false,
		}
		ctx := userInfo.SetContext(context.Background())

		convey.Convey("测试正常创建借阅记录", func() {
			// 清理可能存在的数据
			cleanupTestDataForAddLoan(svcCtx)
			logic := NewAddLoanLogic(ctx, svcCtx)

			// 测试创建借阅记录
			req := &types.AddLoanReq{
				BorrowTime:   time.Now().Unix(),
				DueTime:      time.Now().Add(7 * 24 * time.Hour).Unix(),
				BorrowReason: "测试借阅需要",
				Documents: []types.LoanDocument{
					{
						DocumentId:         "test_add_internal_doc_001",
						DocumentVersionNo:  "V1.0",
						DocumentModuleType: 2, // 内部文档
					},
					{
						DocumentId:         "test_add_external_doc_001",
						DocumentVersionNo:  "V1.0",
						DocumentModuleType: 3, // 外部文档
					},
				},
			}

			resp, err := logic.AddLoan(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试单个文档借阅", func() {
			// 清理可能存在的数据
			cleanupTestDataForAddLoan(svcCtx)
			logic := NewAddLoanLogic(ctx, svcCtx)

			// 测试借阅单个内部文档
			req := &types.AddLoanReq{
				BorrowTime:   time.Now().Unix(),
				DueTime:      time.Now().Add(14 * 24 * time.Hour).Unix(),
				BorrowReason: "单个文档测试",
				Documents: []types.LoanDocument{
					{
						DocumentId:         "test_add_internal_doc_002",
						DocumentVersionNo:  "V1.0",
						DocumentModuleType: 2, // 内部文档
					},
				},
			}

			resp, err := logic.AddLoan(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试外部文档借阅", func() {
			// 清理可能存在的数据
			cleanupTestDataForAddLoan(svcCtx)
			logic := NewAddLoanLogic(ctx, svcCtx)

			// 测试借阅外部文档
			req := &types.AddLoanReq{
				BorrowTime:   time.Now().Unix(),
				DueTime:      time.Now().Add(10 * 24 * time.Hour).Unix(),
				BorrowReason: "外部文档测试",
				Documents: []types.LoanDocument{
					{
						DocumentId:         "test_add_external_doc_002",
						DocumentVersionNo:  "V1.0",
						DocumentModuleType: 3, // 外部文档
					},
				},
			}

			resp, err := logic.AddLoan(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试空文档列表", func() {
			// 清理可能存在的数据
			cleanupTestDataForAddLoan(svcCtx)
			logic := NewAddLoanLogic(ctx, svcCtx)

			req := &types.AddLoanReq{
				BorrowTime:   time.Now().Unix(),
				DueTime:      time.Now().Add(7 * 24 * time.Hour).Unix(),
				BorrowReason: "测试空文档列表",
				Documents:    []types.LoanDocument{}, // 空文档列表
			}

			resp, err := logic.AddLoan(req)
			// 根据业务逻辑，空文档列表可能会被gRPC服务拒绝
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(resp, convey.ShouldBeNil)
		})
	})
}

// setupTestEnvironmentForAddLoan 设置添加借阅记录测试环境
// 功能：创建数据库连接和服务上下文
// 返回值：服务上下文、清理函数、错误信息
func setupTestEnvironmentForAddLoan() (*svc.ServiceContext, func(), error) {
	// 1. 创建测试配置
	// 2. 初始化数据库连接
	// 3. 创建服务上下文
	// 4. 返回清理函数

	// 创建测试配置（从配置文件读取）
	var testConfig config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &testConfig)

	// 初始化数据库连接
	docvaultDB := mapper.NewDocvaultDB(testConfig)
	phoenixDB := mapper.NewPhoenixDB(testConfig)
	nebulaDB := mapper.NewNebulaDB(testConfig)

	// 初始化gRPC连接
	docvaultRpcConn := zrpc.MustNewClient(testConfig.DocvaultRPC).Conn()

	// 创建服务上下文
	svcCtx := &svc.ServiceContext{
		Config:          testConfig,
		DocvaultDB:      docvaultDB,
		PhoenixDB:       phoenixDB,
		NebulaDB:        nebulaDB,
		DocvaultRpcConn: docvaultRpcConn,
	}

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupTestDataForAddLoan(svcCtx)
		// 关闭gRPC连接
		if docvaultRpcConn != nil {
			docvaultRpcConn.Close()
		}
	}

	return svcCtx, cleanup, nil
}

// createTestDataForAddLoan 创建添加借阅记录测试数据
// 功能：在数据库中创建测试所需的数据
// 参数：svcCtx - 服务上下文
// 返回值：测试数据结构、错误信息
func createTestDataForAddLoan(svcCtx *svc.ServiceContext) (*TestDataForAddLoan, error) {
	// 实现步骤：
	// 1. 创建测试用户
	// 2. 创建测试文档
	// 3. 创建业务字典节点关系

	ctx := context.Background()
	now := time.Now()

	// 先清理可能存在的测试数据
	cleanupTestDataForAddLoan(svcCtx)

	// 1. 创建测试用户
	users := []mapper.User{
		{
			ID:        "test_add_user_001",
			Username:  "test_add_user_001",
			Nickname:  "添加借阅测试用户1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
		{
			ID:        "test_add_user_002",
			Username:  "test_add_user_002",
			Nickname:  "添加借阅测试用户2",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, fmt.Errorf("创建测试用户失败: %w", err)
		}
	}

	// 2. 创建测试内部文档
	internalDocs := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_add_internal_doc_001",
			No:             "ADD-INT-001",
			Name:           "添加借阅测试内部文档1",
			VersionNo:      1,
			Status:         1,
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			FileID:         "test_add_file_001",
			DocCategoryID:  "test_add_category_001",
			DepartmentID:   "test_dept_001",
			AuthorID:       "test_add_user_001",
			CreatedBy:      "test_add_user_001",
			UpdatedBy:      "test_add_user_001",
			EffectiveDate:  now.Add(30 * 24 * time.Hour), // 30天后过期
		},
		{
			ID:             "test_add_internal_doc_002",
			No:             "ADD-INT-002",
			Name:           "添加借阅测试内部文档2",
			VersionNo:      1,
			Status:         1,
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_org_001",
			FileID:         "test_add_file_002",
			DocCategoryID:  "test_add_category_001",
			DepartmentID:   "test_dept_001",
			AuthorID:       "test_add_user_001",
			CreatedBy:      "test_add_user_001",
			UpdatedBy:      "test_add_user_001",
			EffectiveDate:  now.Add(60 * 24 * time.Hour), // 60天后过期
		},
	}

	// 插入内部文档数据
	for _, doc := range internalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试内部文档失败: %w", err)
		}
	}

	// 3. 创建测试外部文档
	externalDocs := []mapper.ExternalDocumentLibrary{
		{
			ID:                   "test_add_external_doc_001",
			Number:               "ADD-EXT-001",
			Name:                 "添加借阅测试外部文档1",
			Version:              "V1.0",
			Status:               1,
			CreatedAt:            now,
			UpdatedAt:            now,
			OrganizationID:       "test_org_001",
			FileID:               "test_add_file_003",
			CreatedBy:            "test_add_user_001",
			UpdatedBy:            "test_add_user_001",
			TenantID:             "test_tenant_001",
			TypeDictionaryNodeId: "test_add_category_002",
			EffectiveDate:        now.Add(45 * 24 * time.Hour), // 45天后过期
		},
		{
			ID:                   "test_add_external_doc_002",
			Number:               "ADD-EXT-002",
			Name:                 "添加借阅测试外部文档2",
			Version:              "V1.0",
			Status:               1,
			CreatedAt:            now,
			UpdatedAt:            now,
			OrganizationID:       "test_org_001",
			FileID:               "test_add_file_004",
			CreatedBy:            "test_add_user_001",
			UpdatedBy:            "test_add_user_001",
			TenantID:             "test_tenant_001",
			TypeDictionaryNodeId: "test_add_category_002",
			EffectiveDate:        now.Add(90 * 24 * time.Hour), // 90天后过期
		},
	}

	// 插入外部文档数据
	for _, doc := range externalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, fmt.Errorf("创建测试外部文档失败: %w", err)
		}
	}

	// 4. 创建业务字典节点关系
	businessDictRelations := []mapper.BusinessDictionaryNodeRelation{
		{
			NodeID: "test_add_category_001",
			Names:  "添加借阅内部文档类别",
		},
		{
			NodeID: "test_add_category_002",
			Names:  "添加借阅外部文档类别",
		},
	}

	// 插入业务字典节点关系数据
	for _, relation := range businessDictRelations {
		if err := svcCtx.NebulaDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, fmt.Errorf("创建测试业务字典节点关系失败: %w", err)
		}
	}

	return &TestDataForAddLoan{
		Users:                           users,
		InternalDocuments:               internalDocs,
		ExternalDocuments:               externalDocs,
		BusinessDictionaryNodeRelations: businessDictRelations,
	}, nil
}

// cleanupTestDataForAddLoan 清理添加借阅记录测试数据
// 功能：删除测试过程中创建的数据
// 参数：svcCtx - 服务上下文
func cleanupTestDataForAddLoan(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除所有空ID的借阅文档关系记录（解决主键重复问题）
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Exec("DELETE FROM borrow_document_relations WHERE id = ''")

	// 删除借阅文档关系（可能在测试过程中创建）
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("borrow_record_id LIKE ?", "test_add_borrow_%").Delete(&mapper.BorrowDocumentRelation{})

	// 删除借阅记录（可能在测试过程中创建）
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Exec("DELETE FROM borrow_records WHERE id LIKE ?", "test_add_borrow_%")

	// 删除所有空ID的借阅记录（解决主键重复问题）
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Exec("DELETE FROM borrow_records WHERE id = ''")

	// 删除内部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_add_internal_doc_%").Delete(&mapper.InternalDocumentLibrary{})

	// 删除外部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_add_external_doc_%").Delete(&mapper.ExternalDocumentLibrary{})

	// 删除业务字典节点关系
	svcCtx.NebulaDB.GetDB().WithContext(ctx).Unscoped().Where("node_id LIKE ?", "test_add_category_%").Delete(&mapper.BusinessDictionaryNodeRelation{})

	// 彻底删除用户（包括软删除的记录）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_add_user_%").Delete(&mapper.User{})
}

// TestDataForAddLoan 添加借阅记录测试数据结构
type TestDataForAddLoan struct {
	Users                           []mapper.User
	InternalDocuments               []mapper.InternalDocumentLibrary
	ExternalDocuments               []mapper.ExternalDocumentLibrary
	BusinessDictionaryNodeRelations []mapper.BusinessDictionaryNodeRelation
}

// TestConvertLoanDocumentsToBorrowItems 测试文档转换功能
func TestConvertLoanDocumentsToBorrowItems(t *testing.T) {
	convey.Convey("测试文档转换功能", t, func() {
		// 创建测试逻辑实例
		ctx := context.Background()
		logic := &AddLoanLogic{
			ctx: ctx,
		}

		convey.Convey("测试正常文档转换", func() {
			documents := []types.LoanDocument{
				{
					DocumentId:         "doc_001",
					DocumentVersionNo:  "V1.0",
					DocumentModuleType: 2,
				},
				{
					DocumentId:         "doc_002",
					DocumentVersionNo:  "V2.0",
					DocumentModuleType: 3,
				},
			}

			result, err := logic.convertLoanDocumentsToBorrowItems(documents)
			convey.So(err, convey.ShouldBeNil)
			convey.So(len(result), convey.ShouldEqual, 2)

			// 验证第一个文档
			convey.So(result[0].DocumentId, convey.ShouldEqual, "doc_001")
			convey.So(result[0].VersionNo, convey.ShouldEqual, "V1.0")
			convey.So(result[0].ModuleType, convey.ShouldEqual, 2)
			convey.So(result[0].BorrowStatus, convey.ShouldEqual, 1)

			// 验证第二个文档
			convey.So(result[1].DocumentId, convey.ShouldEqual, "doc_002")
			convey.So(result[1].VersionNo, convey.ShouldEqual, "V2.0")
			convey.So(result[1].ModuleType, convey.ShouldEqual, 3)
			convey.So(result[1].BorrowStatus, convey.ShouldEqual, 1)
		})

		convey.Convey("测试空文档列表转换", func() {
			documents := []types.LoanDocument{}

			result, err := logic.convertLoanDocumentsToBorrowItems(documents)
			convey.So(err, convey.ShouldBeNil)
			convey.So(len(result), convey.ShouldEqual, 0)
		})

		convey.Convey("测试单个文档转换", func() {
			documents := []types.LoanDocument{
				{
					DocumentId:         "single_doc",
					DocumentVersionNo:  "V3.0",
					DocumentModuleType: 2,
				},
			}

			result, err := logic.convertLoanDocumentsToBorrowItems(documents)
			convey.So(err, convey.ShouldBeNil)
			convey.So(len(result), convey.ShouldEqual, 1)
			convey.So(result[0].DocumentId, convey.ShouldEqual, "single_doc")
			convey.So(result[0].VersionNo, convey.ShouldEqual, "V3.0")
			convey.So(result[0].ModuleType, convey.ShouldEqual, 2)
			convey.So(result[0].BorrowStatus, convey.ShouldEqual, 1)
		})
	})
}
