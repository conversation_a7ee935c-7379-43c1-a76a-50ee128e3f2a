package document_library

import (
	"context"
	"fmt"
	"sort"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetExternalElectronicDownloadRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetExternalElectronicDownloadRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetExternalElectronicDownloadRecordsLogic {
	return &GetExternalElectronicDownloadRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetExternalElectronicDownloadRecords 获取外发电子文件一次下载变更记录
// 功能: 根据文档ID分页查询外发电子文件一次下载变更记录，只显示通过发放审批的数据
// 参数:
//   - req: 请求参数，包含文档ID和分页信息
//
// 返回值:
//   - resp: 外发电子文件下载记录列表响应
//   - err: 错误信息
func (l *GetExternalElectronicDownloadRecordsLogic) GetExternalElectronicDownloadRecords(req *types.GetExternalElectronicDownloadRecordsReq) (resp *types.GetExternalElectronicDownloadRecordsResp, err error) {
	// 1. 查询发放记录文件
	distributeFiles, err := l.queryDistributeFiles(req.DocumentID)
	if err != nil {
		return nil, err
	}

	if len(distributeFiles) == 0 {
		return l.buildEmptyResponse(req), nil
	}

	// 2. 查询发放记录
	records, fileRecordMap, err := l.queryDistributeRecords(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 3. 查询并过滤权限记录
	externalDownloadPermissions, err := l.queryAndFilterPermissions(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 4. 构建响应数据
	externalDownloadRecords := l.buildRecords(externalDownloadPermissions, fileRecordMap, records)

	// 5. 排序和分页
	l.sortRecordsByDistributeApplyTimeDesc(externalDownloadRecords)
	pagedRecords, total := l.applyPagination(externalDownloadRecords, req.Page, req.PageSize)

	return &types.GetExternalElectronicDownloadRecordsResp{
		Data: pagedRecords,
		PageInfo: types.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// queryDistributeFiles 查询发放记录文件
func (l *GetExternalElectronicDownloadRecordsLogic) queryDistributeFiles(documentID string) ([]mapper.DistributeRecordFile, error) {
	distributeRecordFileClient := mapper.NewDistributeRecordFileClient(l.svcCtx.DocvaultDB)
	distributeFiles, err := distributeRecordFileClient.FindByFileID(l.ctx, documentID)
	if err != nil {
		l.Logger.Errorf("查询发放记录文件失败: %v", err)
		return nil, fmt.Errorf("查询发放记录文件失败")
	}
	return distributeFiles, nil
}

// buildEmptyResponse 构建空响应
func (l *GetExternalElectronicDownloadRecordsLogic) buildEmptyResponse(req *types.GetExternalElectronicDownloadRecordsReq) *types.GetExternalElectronicDownloadRecordsResp {
	return &types.GetExternalElectronicDownloadRecordsResp{
		Data: []types.ExternalElectronicDownloadRecord{},
		PageInfo: types.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    0,
		},
	}
}

// queryDistributeRecords 查询发放记录
func (l *GetExternalElectronicDownloadRecordsLogic) queryDistributeRecords(distributeFiles []mapper.DistributeRecordFile) ([]mapper.DistributeRecord, map[string]mapper.DistributeRecordFile, error) {
	// 提取发放记录ID
	distributeRecordIDs := make([]string, 0, len(distributeFiles))
	for _, file := range distributeFiles {
		distributeRecordIDs = append(distributeRecordIDs, file.RecordID)
	}

	// 去重
	distributeRecordIDs = utils.SliceDuplicate(distributeRecordIDs)

	// 查询发放记录
	records, err := mapper.NewDistributeRecordClient(l.svcCtx.DocvaultDB).FindByIDs(l.ctx, distributeRecordIDs)
	if err != nil {
		l.Logger.Errorf("查询发放记录失败: %v", err)
		return nil, nil, fmt.Errorf("查询发放记录失败")
	}
	// 只保留已审批的记录
	approvedRecords := make([]mapper.DistributeRecord, 0)
	for _, record := range records {
		if record.Status == 3 { // 已审批
			approvedRecords = append(approvedRecords, record)
		}
	}

	// 构建文件记录映射
	fileRecordMap := make(map[string]mapper.DistributeRecordFile)
	for _, file := range distributeFiles {
		fileRecordMap[file.ID] = file
	}

	return approvedRecords, fileRecordMap, nil
}

// queryAndFilterPermissions 查询并过滤权限记录
func (l *GetExternalElectronicDownloadRecordsLogic) queryAndFilterPermissions(distributeFiles []mapper.DistributeRecordFile) ([]mapper.DistributeRecordPermission, error) {
	// 提取文件记录ID
	fileRecordIDs := make([]string, 0, len(distributeFiles))
	for _, file := range distributeFiles {
		fileRecordIDs = append(fileRecordIDs, file.ID)
	}

	// 去重
	fileRecordIDs = utils.SliceDuplicate(fileRecordIDs)

	// 查询权限记录
	distributeRecordPermissionClient := mapper.NewDistributeRecordPermissionClient(l.svcCtx.DocvaultDB)
	permissions, err := distributeRecordPermissionClient.FindByFileRecordIDs(l.ctx, fileRecordIDs)
	if err != nil {
		l.Logger.Errorf("查询权限记录失败: %v", err)
		return nil, fmt.Errorf("查询权限记录失败")
	}

	// 过滤外发电子文件一次下载权限
	externalDownloadPermissions := make([]mapper.DistributeRecordPermission, 0)
	for _, perm := range permissions {
		if perm.FileForm == 1 && perm.FilePermission == 3 { // 电子文件 + 一次下载
			externalDownloadPermissions = append(externalDownloadPermissions, perm)
		}
	}

	return externalDownloadPermissions, nil
}

// buildRecords 构建响应数据
func (l *GetExternalElectronicDownloadRecordsLogic) buildRecords(permissions []mapper.DistributeRecordPermission, fileRecordMap map[string]mapper.DistributeRecordFile, records []mapper.DistributeRecord) []types.ExternalElectronicDownloadRecord {
	// 构建记录映射
	recordMap := make(map[string]mapper.DistributeRecord)
	for _, record := range records {
		recordMap[record.ID] = record
	}

	// 构建响应数据
	externalDownloadRecords := make([]types.ExternalElectronicDownloadRecord, 0, len(permissions))
	for _, perm := range permissions {
		// 获取文件记录
		fileRecord, exists := fileRecordMap[perm.FileRecordID]
		if !exists {
			continue
		}

		// 获取发放记录
		distributeRecord, exists := recordMap[fileRecord.RecordID]
		if !exists {
			continue
		}

		// 构建记录
		record := types.ExternalElectronicDownloadRecord{
			RecordID:                perm.ID,
			WorkflowID:              distributeRecord.WorkflowID,
			DistributeRecordID:      distributeRecord.ID, // 发放记录ID
			RecipientUserID:         perm.UserID,
			RecipientUserName:       l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, perm.UserID),
			DistributeApplicant:     distributeRecord.Applicant,
			DistributeApplicantName: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, distributeRecord.Applicant),
			DistributeApplyTime:     distributeRecord.ApplyDate,
			IsDownloaded:            perm.SignForStatus == 2, // 2表示已签收（已下载）
			InventoryID:             fileRecord.ID,
		}
		externalDownloadRecords = append(externalDownloadRecords, record)
	}

	return externalDownloadRecords
}

// sortRecordsByDistributeApplyTimeDesc 按发放申请时间降序排序
func (l *GetExternalElectronicDownloadRecordsLogic) sortRecordsByDistributeApplyTimeDesc(records []types.ExternalElectronicDownloadRecord) {
	sort.Slice(records, func(i, j int) bool {
		return records[i].DistributeApplyTime > records[j].DistributeApplyTime
	})
}

// applyPagination 应用分页
func (l *GetExternalElectronicDownloadRecordsLogic) applyPagination(records []types.ExternalElectronicDownloadRecord, page, pageSize uint64) ([]types.ExternalElectronicDownloadRecord, uint64) {
	total := uint64(len(records))
	start := int((page - 1) * pageSize)
	end := int(start + int(pageSize))

	if start >= len(records) {
		return []types.ExternalElectronicDownloadRecord{}, total
	} else if end > len(records) {
		return records[start:], total
	} else {
		return records[start:end], total
	}
}
