package document_library

import (
	"context"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/utils"

	"nebula/internal/infrastructure/adapter/clientx/entity"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDocPermissionUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDocPermissionUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDocPermissionUsersLogic {
	return &GetDocPermissionUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDocPermissionUsersLogic) GetDocPermissionUsers(req *types.GetDocPermissionUsersReq) (resp *types.GetDocPermissionUsersResp, err error) {
	user, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).GetDocPermissionUsers(l.ctx, &docvault.GetDocPermissionUsersReq{
		FileId:         req.FileID,
		FileForm:       req.FileForm,
		FilePermission: req.FilePermission,
	})
	if err != nil {
		l.Logger.Errorf("获取文件权限关联用户失败：%v", err)
		return nil, err
	}
	// 封装用户发放回收状态
	distributeDocPermissionUserMap := make(map[string]int32)
	for _, v := range user.WaitForApprovalUsers {
		distributeDocPermissionUserMap[v] = 1 // 待审批
	}
	for _, v := range user.NotRecycledUsers {
		distributeDocPermissionUserMap[v] = 2 // 未回收
	}
	for _, v := range user.RecycleFlowUsers {
		distributeDocPermissionUserMap[v] = 3 // 回收审批中
	}
	// 查询组织用户树
	orgUserTree, err := l.svcCtx.PhoenixClient.GetAllOrganizationsAndUsersByOrgId(l.ctx, utils.GetContextOrganizationID(l.ctx), 2)
	if err != nil {
		l.Logger.Errorf("获取组织用户树失败：%v", err)
		return nil, err
	}

	// 数据封装
	var organizationUserTree *types.OrganizationUserTree
	var users []types.OrganizationUsers
	if len(orgUserTree.Data) > 0 {
		organizationUserTree, users = convertOrgUserTreeV2(&orgUserTree.Data[0], distributeDocPermissionUserMap)
	}

	return &types.GetDocPermissionUsersResp{
		// 左边发放人员列表树
		OrganizationUserTree: organizationUserTree,
		// 右边回收人员列表
		Users: users,
	}, nil
}

func convertOrgUserTreeV2(src *entity.OrganizationUserInfo, distributeDocPermissionUserMap map[string]int32) (*types.OrganizationUserTree, []types.OrganizationUsers) {
	// 如果src为空，则返回nil
	if src == nil {
		return nil, nil
	}
	// 创建OrganizationUserTree节点
	node := &types.OrganizationUserTree{
		ParentId: src.ParentId,
		OrgID:    src.OrgID,
		OrgName:  src.OrgName,
	}
	// 创建skippedUsers切片
	var skippedUsers []types.OrganizationUsers
	for _, u := range src.OrganizationUsers {
		// 1待审批 2未回收 3回收审批中
		// 查找distributeDocPermissionUserMap中是否存在u.UserID
		status, ok := distributeDocPermissionUserMap[u.UserID]
		if ok && status == 2 {
			// 创建OrganizationUsers节点
			user := types.OrganizationUsers{
				UserID:   u.UserID,
				Nickname: u.Nickname,
			}
			user.Status = 1
			skippedUsers = append(skippedUsers, user)
			continue
		}
		if ok && status == 3 {
			// 创建OrganizationUsers节点
			user := types.OrganizationUsers{
				UserID:   u.UserID,
				Nickname: u.Nickname,
			}
			user.Status = 2
			skippedUsers = append(skippedUsers, user)
			continue
		}
		// 创建OrganizationUsers节点
		user := types.OrganizationUsers{
			UserID:   u.UserID,
			Nickname: u.Nickname,
		}
		if ok && status == 1 {
			user.Status = 2
		} else {
			user.Status = 1
		}
		node.OrganizationUsers = append(node.OrganizationUsers, user)
	}

	for _, child := range src.Children {
		if childNode, childSkipped := convertOrgUserTreeV2(child, distributeDocPermissionUserMap); childNode != nil {
			node.Children = append(node.Children, childNode)
			skippedUsers = append(skippedUsers, childSkipped...)
		}
	}
	return node, skippedUsers
}
