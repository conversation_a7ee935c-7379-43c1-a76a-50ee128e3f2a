package document_library

import (
	"context"
	"strconv"
	"strings"
	"sync"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

// GetLoanRecordsLogic 借阅记录查询逻辑处理器
type GetLoanRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// CollectedData 从借阅记录中收集的数据
type CollectedData struct {
	UserIDs         []string // 需要查询的用户ID列表
	BorrowRecordIDs []string // 借阅记录ID列表
}

// QueryResult 并发查询的结果
type QueryResult struct {
	UserNicknames  map[string]string                            // 用户ID到昵称的映射
	DocumentCounts map[string]*mapper.BorrowRecordDocumentCount // 借阅记录文档数量统计
}

func NewGetLoanRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLoanRecordsLogic {
	return &GetLoanRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetLoanRecords 获取借阅记录
// 功能：查询借阅记录并返回包含用户昵称和文档名称的详细信息
// 参数：req - 查询请求参数
// 返回值：resp - 查询结果响应，err - 错误信息
// 异常：数据库查询失败、并发查询失败等
func (l *GetLoanRecordsLogic) GetLoanRecords(req *types.GetLoanRecordsReq) (resp *types.GetLoanRecordsResp, err error) {
	// 实现步骤：
	// 1. 查询借阅记录
	// 2. 收集需要查询的ID信息
	// 3. 并发查询用户昵称和文档名称
	// 4. 构建响应数据

	// 1. 查询借阅记录
	borrowerRecords, total, err := l.queryBorrowRecords(req)
	if err != nil {
		return nil, err
	}

	// 2. 收集需要查询的ID信息
	collectedData := l.collectDataFromRecords(borrowerRecords)

	// 3. 并发查询用户昵称和文档名称
	queryResult, err := l.queryConcurrently(collectedData)
	if err != nil {
		return nil, err
	}

	// 4. 构建响应数据
	loanRecords := l.buildLoanRecords(borrowerRecords, queryResult)

	resp = &types.GetLoanRecordsResp{
		Total: int64(total),
		Data:  loanRecords,
	}

	return
}

// queryBorrowRecords 查询借阅记录
// 功能：根据请求参数查询借阅记录
// 参数：req - 查询请求参数
// 返回值：借阅记录列表、总数、错误信息
func (l *GetLoanRecordsLogic) queryBorrowRecords(req *types.GetLoanRecordsReq) ([]mapper.BorrowRecordView, int, error) {
	// 1. 构建基础查询参数
	pageReq := l.buildBasePageReq(req)

	// 2. 应用各种过滤条件
	if err := l.applyApprovalStatusFilter(req, &pageReq); err != nil {
		return nil, 0, err
	}

	if err := l.applyDocumentModuleFilter(req, &pageReq); err != nil {
		return nil, 0, err
	}

	if isEmpty, err := l.applyUserNicknameFilter(req, &pageReq); err != nil {
		return nil, 0, err
	} else if isEmpty {
		return []mapper.BorrowRecordView{}, 0, nil
	}

	if isEmpty, err := l.applyDocumentNoFilter(req, &pageReq); err != nil {
		return nil, 0, err
	} else if isEmpty {
		return []mapper.BorrowRecordView{}, 0, nil
	}

	if isEmpty, err := l.applyDocumentNameFilter(req, &pageReq); err != nil {
		return nil, 0, err
	} else if isEmpty {
		return []mapper.BorrowRecordView{}, 0, nil
	}

	l.applyDocumentCategoryFilter(req, &pageReq)

	// 3. 执行查询
	records, total, err := mapper.NewBorrowRecordClient(l.svcCtx.DocvaultDB).GetBorrowRecordStatistics(l.ctx, pageReq)
	return records, int(total), err
}

// buildBasePageReq 构建基础分页请求参数
// 参数: req - 原始请求参数
// 返回值: 分页请求参数
// 功能: 从原始请求中提取基础分页信息
func (l *GetLoanRecordsLogic) buildBasePageReq(req *types.GetLoanRecordsReq) mapper.PageBorrowRecordReq {
	return mapper.PageBorrowRecordReq{
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		NoPage:   req.NoPage,
	}
}

// applyApprovalStatusFilter 应用审批状态过滤
// 参数: req - 原始请求, pageReq - 分页请求参数
// 返回值: 错误信息
// 功能: 将字符串类型的审批状态转换为整数指针类型并应用过滤
func (l *GetLoanRecordsLogic) applyApprovalStatusFilter(req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) error {
	if req.ApprovalStatus != "" {
		if status, err := strconv.Atoi(req.ApprovalStatus); err == nil {
			pageReq.ApprovalStatus = &status
		}
	}
	return nil
}

// applyDocumentModuleFilter 应用文档模块过滤
// 参数: req - 原始请求, pageReq - 分页请求参数
// 返回值: 错误信息
// 功能: 将字符串类型的文档模块转换为整数指针类型并应用过滤
func (l *GetLoanRecordsLogic) applyDocumentModuleFilter(req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) error {
	if req.DocumentModule != "" {
		if moduleType, err := strconv.Atoi(req.DocumentModule); err == nil {
			pageReq.ModuleType = &moduleType
		}
	}
	return nil
}

// applyUserNicknameFilter 应用用户昵称过滤
// 参数: req - 原始请求, pageReq - 分页请求参数
// 返回值: 是否为空结果, 错误信息
// 功能: 根据用户昵称模糊查询获取用户ID并应用过滤
func (l *GetLoanRecordsLogic) applyUserNicknameFilter(req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
	if req.UserNickname == "" {
		return false, nil
	}

	userClient := mapper.NewUserClient(l.svcCtx.PhoenixDB)
	userIDs, err := userClient.GetUserIDsByNickname(l.ctx, req.UserNickname)
	if err != nil {
		return false, err
	}

	// 如果没有找到匹配的用户，返回空结果标识
	if len(userIDs) == 0 {
		return true, nil
	}

	// 只取第一个匹配的用户ID
	pageReq.UserID = userIDs[0]
	return false, nil
}

// applyDocumentNoFilter 应用文档编号过滤
// 参数: req - 原始请求, pageReq - 分页请求参数
// 返回值: 是否为空结果, 错误信息
// 功能: 根据文档编号模糊查询获取文档ID列表并应用过滤
func (l *GetLoanRecordsLogic) applyDocumentNoFilter(req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
	if req.DocumentNo == "" {
		return false, nil
	}

	// 查询文档ID列表
	documentIDs, err := l.queryDocumentIDsByNo(req.DocumentNo, req.DocumentModule)
	if err != nil {
		return false, err
	}

	// 如果没有找到匹配的文档，返回空结果标识
	if len(documentIDs) == 0 {
		return true, nil
	}

	pageReq.DocumentIDs = documentIDs
	return false, nil
}

// applyDocumentNameFilter 应用文档名称过滤
// 参数: req - 原始请求, pageReq - 分页请求参数
// 返回值: 是否为空结果, 错误信息
// 功能: 根据文档名称模糊查询获取文档ID列表并应用过滤
func (l *GetLoanRecordsLogic) applyDocumentNameFilter(req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) (bool, error) {
	if req.DocumentName == "" {
		return false, nil
	}

	// 查询文档ID列表
	documentIDs, err := l.queryDocumentIDsByName(req.DocumentName, req.DocumentModule)
	if err != nil {
		return false, err
	}

	// 如果没有找到匹配的文档，返回空结果标识
	if len(documentIDs) == 0 {
		return true, nil
	}

	// 如果已经有 DocumentIDs，则取交集
	if len(pageReq.DocumentIDs) > 0 {
		intersectionIDs := l.intersectDocumentIDs(pageReq.DocumentIDs, documentIDs)
		if len(intersectionIDs) == 0 {
			return true, nil
		}
		pageReq.DocumentIDs = intersectionIDs
	} else {
		pageReq.DocumentIDs = documentIDs
	}

	return false, nil
}

// applyDocumentCategoryFilter 应用文档类别过滤
// 参数: req - 原始请求, pageReq - 分页请求参数
// 功能: 应用文档类别ID过滤
func (l *GetLoanRecordsLogic) applyDocumentCategoryFilter(req *types.GetLoanRecordsReq, pageReq *mapper.PageBorrowRecordReq) {
	if req.DocumentCategoryId != "" {
		pageReq.DocumentCategoryId = req.DocumentCategoryId
	}
}

// queryDocumentIDsByNo 根据文档编号查询文档ID列表
// 参数: documentNo - 文档编号, moduleType - 文档模块类型
// 返回值: 文档ID列表, 错误信息
// 功能: 根据文档模块类型查询对应文档库中的文档ID
func (l *GetLoanRecordsLogic) queryDocumentIDsByNo(documentNo, moduleType string) ([]string, error) {
	var documentIDs []string

	// 根据文档模块类型决定查询哪个文档库
	if moduleType != "" {
		switch moduleType {
		case "2": // 内部文档
			internalClient := mapper.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
			ids, err := internalClient.GetDocumentIDsByNo(l.ctx, documentNo)
			if err != nil {
				return nil, err
			}
			documentIDs = append(documentIDs, ids...)
		case "3": // 外部文档
			externalClient := mapper.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
			ids, err := externalClient.GetDocumentIDsByNo(l.ctx, documentNo)
			if err != nil {
				return nil, err
			}
			documentIDs = append(documentIDs, ids...)
		}
	} else {
		// 如果没有指定文档模块，则同时查询内部和外部文档
		internalClient := mapper.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
		internalIDs, err := internalClient.GetDocumentIDsByNo(l.ctx, documentNo)
		if err != nil {
			return nil, err
		}
		documentIDs = append(documentIDs, internalIDs...)

		externalClient := mapper.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
		externalIDs, err := externalClient.GetDocumentIDsByNo(l.ctx, documentNo)
		if err != nil {
			return nil, err
		}
		documentIDs = append(documentIDs, externalIDs...)
	}

	return documentIDs, nil
}

// queryDocumentIDsByName 根据文档名称查询文档ID列表
// 参数: documentName - 文档名称, moduleType - 文档模块类型
// 返回值: 文档ID列表, 错误信息
// 功能: 根据文档模块类型查询对应文档库中的文档ID
func (l *GetLoanRecordsLogic) queryDocumentIDsByName(documentName, moduleType string) ([]string, error) {
	var documentIDs []string

	// 根据文档模块类型决定查询哪个文档库
	if moduleType != "" {
		switch moduleType {
		case "2": // 内部文档
			internalClient := mapper.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
			ids, err := internalClient.GetDocumentIDsByName(l.ctx, documentName)
			if err != nil {
				return nil, err
			}
			documentIDs = append(documentIDs, ids...)
		case "3": // 外部文档
			externalClient := mapper.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
			ids, err := externalClient.GetDocumentIDsByName(l.ctx, documentName)
			if err != nil {
				return nil, err
			}
			documentIDs = append(documentIDs, ids...)
		}
	} else {
		// 如果没有指定文档模块，则同时查询内部和外部文档
		internalClient := mapper.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
		internalIDs, err := internalClient.GetDocumentIDsByName(l.ctx, documentName)
		if err != nil {
			return nil, err
		}
		documentIDs = append(documentIDs, internalIDs...)

		externalClient := mapper.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
		externalIDs, err := externalClient.GetDocumentIDsByName(l.ctx, documentName)
		if err != nil {
			return nil, err
		}
		documentIDs = append(documentIDs, externalIDs...)
	}

	return documentIDs, nil
}

// intersectDocumentIDs 计算两个文档ID列表的交集
// 参数: list1 - 第一个文档ID列表, list2 - 第二个文档ID列表
// 返回值: 交集文档ID列表
// 功能: 计算两个文档ID列表的交集，用于多条件查询时的结果合并
func (l *GetLoanRecordsLogic) intersectDocumentIDs(list1, list2 []string) []string {
	documentIDSet := make(map[string]bool)
	for _, id := range list1 {
		documentIDSet[id] = true
	}

	var intersectionIDs []string
	for _, id := range list2 {
		if documentIDSet[id] {
			intersectionIDs = append(intersectionIDs, id)
		}
	}

	return intersectionIDs
}

// collectDataFromRecords 从借阅记录中收集需要查询的数据
// 功能：提取借阅记录中的用户ID和借阅记录ID
// 参数：borrowerRecords - 借阅记录列表
// 返回值：收集到的数据结构
func (l *GetLoanRecordsLogic) collectDataFromRecords(borrowerRecords []mapper.BorrowRecordView) *CollectedData {
	userIDs := make([]string, 0, 0)
	borrowRecordIDs := make([]string, 0, 0)
	userIDSet := make(map[string]bool)

	for _, record := range borrowerRecords {
		// 收集借阅记录ID
		borrowRecordIDs = append(borrowRecordIDs, record.ID)

		// 收集用户ID
		if !userIDSet[record.UserID] {
			userIDs = append(userIDs, record.UserID)
			userIDSet[record.UserID] = true
		}
		if record.RecoverUserID != "" && !userIDSet[record.RecoverUserID] {
			userIDs = append(userIDs, record.RecoverUserID)
			userIDSet[record.RecoverUserID] = true
		}
	}

	return &CollectedData{
		UserIDs:         userIDs,
		BorrowRecordIDs: borrowRecordIDs,
	}
}

// queryConcurrently 并发查询用户昵称和文档数量统计
// 功能：使用协程并发查询用户昵称和文档数量统计
// 参数：collectedData - 收集到的需要查询的数据
// 返回值：查询结果、错误信息
// 异常：数据库查询失败、并发执行错误
func (l *GetLoanRecordsLogic) queryConcurrently(collectedData *CollectedData) (*QueryResult, error) {
	// 实现步骤：
	// 1. 启动两个协程分别查询用户昵称和文档数量统计
	// 2. 等待所有协程完成
	// 3. 检查错误并返回结果
	var wg sync.WaitGroup
	var userNicknames map[string]string
	var documentCounts map[string]*mapper.BorrowRecordDocumentCount
	var userErr, documentCountErr error

	// 协程1: 查询用户昵称
	if len(collectedData.UserIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			userClient := mapper.NewUserClient(l.svcCtx.PhoenixDB)
			userNicknames, userErr = userClient.BatchGetUserNicknames(l.ctx, collectedData.UserIDs)
		}()
	} else {
		userNicknames = make(map[string]string)
	}

	// 协程2: 查询文档数量统计
	if len(collectedData.BorrowRecordIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			borrowerClient := mapper.NewBorrowRecordClient(l.svcCtx.DocvaultDB)
			documentCounts, documentCountErr = borrowerClient.BatchGetDocumentCounts(l.ctx, collectedData.BorrowRecordIDs)
		}()
	} else {
		documentCounts = make(map[string]*mapper.BorrowRecordDocumentCount)
	}

	// 等待所有协程完成
	wg.Wait()

	// 检查错误
	if userErr != nil {
		return nil, userErr
	}
	if documentCountErr != nil {
		return nil, documentCountErr
	}

	return &QueryResult{
		UserNicknames: userNicknames,

		DocumentCounts: documentCounts,
	}, nil
}

// buildLoanRecords 构建借阅记录响应数据
// 功能：将借阅记录和查询结果组合成最终的响应数据
// 参数：borrowerRecords - 借阅记录列表，queryResult - 查询结果
// 返回值：格式化后的借阅记录列表
func (l *GetLoanRecordsLogic) buildLoanRecords(borrowerRecords []mapper.BorrowRecordView, queryResult *QueryResult) []types.LoanRecord {
	// 实现步骤：
	// 1. 按借阅记录ID分组
	// 2. 为每个借阅记录收集所有回收人信息
	// 3. 设置用户昵称、文档数量统计等信息
	// 4. 将回收人昵称用逗号连接

	// 按借阅记录ID分组
	recordGroups := make(map[string][]mapper.BorrowRecordView)
	for _, record := range borrowerRecords {
		recordGroups[record.ID] = append(recordGroups[record.ID], record)
	}

	loanRecords := make([]types.LoanRecord, 0, len(recordGroups))
	for recordID, records := range recordGroups {
		// 使用第一条记录作为基础信息（借阅记录的基本信息都相同）
		firstRecord := records[0]
		loanRecord := types.LoanRecord{
			Id:                firstRecord.ID,
			UserNickname:      queryResult.UserNicknames[firstRecord.UserID],
			BorrowTime:        firstRecord.BorrowTime.UnixMilli(),
			DueTime:           firstRecord.DueTime.UnixMilli(),
			BorrowReason:      firstRecord.BorrowReason,
			ApprovalStatus:    firstRecord.ApprovalStatus,
			ApprovalApplyTime: firstRecord.ApprovalApplyTime.UnixMilli(),
		}

		// 设置文档数量统计
		if documentCount, exists := queryResult.DocumentCounts[recordID]; exists {
			loanRecord.RecoverDocumentsCount = documentCount.RecoverDocumentsCount
			loanRecord.BorrowDocumentsCount = documentCount.BorrowDocumentsCount
		} else {
			loanRecord.RecoverDocumentsCount = 0
			loanRecord.BorrowDocumentsCount = 0
		}

		// 注意：LoanRecord结构体中没有单个文档名称字段，因为一个借阅记录可能包含多个文档
		// 如果需要查看具体的文档信息，应该通过 GetLoanRecordDocuments 接口获取

		// 收集所有回收人ID（去重）
		recoverUserIDs := make(map[string]bool)
		for _, record := range records {
			if record.RecoverUserID != "" {
				recoverUserIDs[record.RecoverUserID] = true
			}
		}

		// 构建回收人姓名字符串
		recoverNames := make([]string, 0, len(recoverUserIDs))
		for userID := range recoverUserIDs {
			if nickname, exists := queryResult.UserNicknames[userID]; exists && nickname != "" {
				recoverNames = append(recoverNames, nickname)
			}
		}
		// 用逗号连接所有回收人姓名
		loanRecord.RecoverNames = strings.Join(recoverNames, ",")

		loanRecords = append(loanRecords, loanRecord)
	}

	return loanRecords
}
