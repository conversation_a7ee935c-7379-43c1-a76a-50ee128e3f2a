package document_library

import (
	"context"

	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteLoanLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteLoanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteLoanLogic {
	return &DeleteLoanLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// DeleteLoan 删除借阅记录
// 功能：根据借阅记录ID删除指定的借阅记录
// 参数：req - 删除借阅记录请求参数，包含借阅记录ID
// 返回值：resp - 删除响应，err - 错误信息
// 异常：当获取用户信息失败、借阅记录ID为空或RPC调用失败时返回错误
func (l *DeleteLoanLogic) DeleteLoan(req *types.DeleteLoanReq) (resp *types.DeleteLoanResp, err error) {
	// 步骤1：验证请求参数
	// 步骤2：获取当前登录用户信息
	// 步骤3：构建gRPC请求参数
	// 步骤4：调用docvault服务删除借阅记录
	// 步骤5：返回响应结果

	// 获取当前登录用户信息
	userInfo := utils.GetCurrentLoginUser(l.ctx)

	// 构建gRPC请求参数
	grpcReq := &docvault.BorrowRecordDeleteReq{
		BorrowRecordId: req.BorrowRecordId,
	}

	// 调用docvault服务删除借阅记录
	_, err = docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).DeleteBorrowRecord(l.ctx, grpcReq)
	if err != nil {
		logc.Errorw(l.ctx, "删除借阅记录失败",
			logx.Field("borrowRecordId", req.BorrowRecordId),
			logx.Field("userId", userInfo.UserId),
			logx.Field("error", err))
		return nil, err
	}

	// 返回响应
	return &types.DeleteLoanResp{}, nil
}
