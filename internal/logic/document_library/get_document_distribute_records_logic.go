package document_library

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDocumentDistributeRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDocumentDistributeRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDocumentDistributeRecordsLogic {
	return &GetDocumentDistributeRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetDocumentDistributeRecords 获取文档发放回收记录
// 功能: 根据文档ID分页查询发放回收记录，只显示通过审批的数据
// 参数:
//   - req: 请求参数，包含文档ID和分页信息
//
// 返回值:
//   - resp: 发放回收记录列表响应
//   - err: 错误信息
func (l *GetDocumentDistributeRecordsLogic) GetDocumentDistributeRecords(req *types.GetDocumentDistributeRecordsReq) (resp *types.GetDocumentDistributeRecordsResp, err error) {
	// 1. 查询发放记录文件
	distributeFiles, err := l.queryDistributeFiles(req.DocumentID)
	if err != nil {
		return nil, err
	}

	if len(distributeFiles) == 0 {
		return l.buildEmptyResponse(req), nil
	}

	// 2. 查询发放记录
	records, _, err := l.queryDistributeRecords(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 3. 查询权限记录
	permissions, err := l.queryPermissions(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 4. 构建权限变更记录映射
	permissionMap := l.buildPermissionChangeMap(permissions, distributeFiles)

	// 5. 构建响应数据
	distributeRecords := l.buildRecords(records, permissionMap)

	// 6. 排序和分页
	l.sortRecordsByApplyTimeDesc(distributeRecords)
	pagedRecords, total := l.applyPagination(distributeRecords, req.Page, req.PageSize)

	return &types.GetDocumentDistributeRecordsResp{
		Data: pagedRecords,
		PageInfo: types.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    total,
		},
	}, nil
}

// PermissionChangeRecord 权限变更记录
type PermissionChangeRecord struct {
	InternalElectronicQuery    string // 内发：电子文件-查询权限变更记录
	InternalElectronicDownload string // 内发：电子文件-查询/下载权限变更记录
}

// buildPermissionChangeMap 构建权限变更记录映射
// 功能: 根据权限记录和文件记录构建权限变更记录映射
// 参数:
//   - permissions: 权限记录列表
//   - distributeFiles: 发放记录文件列表
//
// 返回值:
//   - map[string]PermissionChangeRecord: recordID -> 权限变更记录
func (l *GetDocumentDistributeRecordsLogic) buildPermissionChangeMap(permissions []mapper.DistributeRecordPermission, distributeFiles []mapper.DistributeRecordFile) map[string]PermissionChangeRecord {
	// 构建文件记录ID到发放记录ID的映射
	fileToRecordMap := make(map[string]string)
	for _, file := range distributeFiles {
		fileToRecordMap[file.ID] = file.RecordID
	}

	// 按发放记录ID分组权限记录
	recordPermissionMap := make(map[string][]mapper.DistributeRecordPermission)
	for _, permission := range permissions {
		recordID := fileToRecordMap[permission.FileRecordID]
		if recordID != "" {
			recordPermissionMap[recordID] = append(recordPermissionMap[recordID], permission)
		}
	}

	// 构建权限变更记录
	result := make(map[string]PermissionChangeRecord)
	for recordID, perms := range recordPermissionMap {
		changeRecord := PermissionChangeRecord{}

		// 按权限类型分组
		queryUsers := make([]string, 0)    // 查询权限用户
		downloadUsers := make([]string, 0) // 查询/下载权限用户

		for _, perm := range perms {
			// 只处理电子文件（FileForm=1）
			if perm.FileForm == 1 {
				statusText := l.getPermissionStatusText(perm.SignForStatus, perm.DisposeStatus)
				userText := fmt.Sprintf("%s（%s）", perm.UserName, statusText)

				if perm.FilePermission == 1 { // 查询权限
					queryUsers = append(queryUsers, userText)
				} else if perm.FilePermission == 2 { // 查询/下载权限
					downloadUsers = append(downloadUsers, userText)
				}
			}
		}

		changeRecord.InternalElectronicQuery = strings.Join(queryUsers, "、")
		changeRecord.InternalElectronicDownload = strings.Join(downloadUsers, "、")

		result[recordID] = changeRecord
	}

	return result
}

// getPermissionStatusText 获取权限状态文本
// 功能: 根据签收状态和处置状态获取状态文本
// 参数:
//   - signForStatus: 签收状态（1未签收 | 2已签收）
//   - disposeStatus: 处置状态（1未回收 | 2回收审批中 | 3已回收 | 4处置审批中 | 5已处置）
//
// 返回值:
//   - string: 状态文本
func (l *GetDocumentDistributeRecordsLogic) getPermissionStatusText(signForStatus, disposeStatus int32) string {
	// 优先判断处置状态
	if disposeStatus >= 3 { // 已回收或已处置
		return "回收"
	}

	// 判断签收状态
	if signForStatus == 2 { // 已签收
		return "已签收"
	}

	return "未签收" // 默认未签收
}

// queryDistributeFiles 查询发放记录文件
func (l *GetDocumentDistributeRecordsLogic) queryDistributeFiles(documentID string) ([]mapper.DistributeRecordFile, error) {
	distributeRecordFileClient := mapper.NewDistributeRecordFileClient(l.svcCtx.DocvaultDB)
	distributeFiles, err := distributeRecordFileClient.FindByFileID(documentID)
	if err != nil {
		l.Logger.Errorf("查询发放记录文件失败: %v", err)
		return nil, fmt.Errorf("查询发放记录失败")
	}
	return distributeFiles, nil
}

// buildEmptyResponse 构建空响应
func (l *GetDocumentDistributeRecordsLogic) buildEmptyResponse(req *types.GetDocumentDistributeRecordsReq) *types.GetDocumentDistributeRecordsResp {
	return &types.GetDocumentDistributeRecordsResp{
		Data: []types.DocumentDistributeRecord{},
		PageInfo: types.PageInfo{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    0,
		},
	}
}

// queryDistributeRecords 查询发放记录
func (l *GetDocumentDistributeRecordsLogic) queryDistributeRecords(distributeFiles []mapper.DistributeRecordFile) ([]mapper.DistributeRecord, map[string][]mapper.DistributeRecordFile, error) {
	distributeRecordClient := mapper.NewDistributeRecordClient(l.svcCtx.DocvaultDB)

	recordIDs := make([]string, 0, len(distributeFiles))
	fileRecordMap := make(map[string][]mapper.DistributeRecordFile)
	for _, file := range distributeFiles {
		recordIDs = append(recordIDs, file.RecordID)
		if fileRecordMap[file.RecordID] == nil {
			fileRecordMap[file.RecordID] = make([]mapper.DistributeRecordFile, 0)
		}
		fileRecordMap[file.RecordID] = append(fileRecordMap[file.RecordID], file)
	}

	uniqueRecordIDs := utils.SliceDuplicate(recordIDs)
	records := make([]mapper.DistributeRecord, 0)
	for _, recordID := range uniqueRecordIDs {
		record, err := distributeRecordClient.FindByID(recordID)
		if err != nil {
			l.Logger.Errorf("查询发放记录失败, recordID: %s, error: %v", recordID, err)
			continue
		}
		if record.Status == 3 {
			records = append(records, record)
		}
	}

	return records, fileRecordMap, nil
}

// queryPermissions 查询权限记录
func (l *GetDocumentDistributeRecordsLogic) queryPermissions(distributeFiles []mapper.DistributeRecordFile) ([]mapper.DistributeRecordPermission, error) {
	distributeRecordPermissionClient := mapper.NewDistributeRecordPermissionClient(l.svcCtx.DocvaultDB)

	fileRecordIDs := make([]string, 0, len(distributeFiles))
	for _, file := range distributeFiles {
		fileRecordIDs = append(fileRecordIDs, file.ID)
	}

	permissions, err := distributeRecordPermissionClient.FindByFileRecordIDs(fileRecordIDs)
	if err != nil {
		l.Logger.Errorf("查询权限记录失败: %v", err)
		return nil, fmt.Errorf("查询权限记录失败")
	}

	return permissions, nil
}

// buildRecords 构建响应数据
func (l *GetDocumentDistributeRecordsLogic) buildRecords(records []mapper.DistributeRecord, permissionMap map[string]PermissionChangeRecord) []types.DocumentDistributeRecord {
	distributeRecords := make([]types.DocumentDistributeRecord, 0, len(records))
	for _, record := range records {
		distributeRecord := types.DocumentDistributeRecord{
			RecordID:                   record.ID,
			WorkflowID:                 record.WorkflowID,
			ApplyTime:                  record.ApplyDate,
			InternalElectronicQuery:    permissionMap[record.ID].InternalElectronicQuery,
			InternalElectronicDownload: permissionMap[record.ID].InternalElectronicDownload,
			Applicant:                  record.Applicant,
			ApplicantName:              l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, record.Applicant),
		}
		distributeRecords = append(distributeRecords, distributeRecord)
	}
	return distributeRecords
}

// applyPagination 应用分页
func (l *GetDocumentDistributeRecordsLogic) applyPagination(records []types.DocumentDistributeRecord, page, pageSize uint64) ([]types.DocumentDistributeRecord, uint64) {
	total := uint64(len(records))
	start := int((page - 1) * pageSize)
	end := int(start + int(pageSize))

	if start >= len(records) {
		return []types.DocumentDistributeRecord{}, total
	} else if end > len(records) {
		return records[start:], total
	} else {
		return records[start:end], total
	}
}

// sortRecordsByApplyTimeDesc 按申请时间倒序排列
func (l *GetDocumentDistributeRecordsLogic) sortRecordsByApplyTimeDesc(records []types.DocumentDistributeRecord) {
	sort.Slice(records, func(i, j int) bool {
		return records[i].ApplyTime > records[j].ApplyTime
	})
}
