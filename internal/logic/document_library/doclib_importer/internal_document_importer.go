package doclib_importer

import (
	"context"
	"errors"
	"fmt"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/clientx/entity"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/respx"
	"nebula/internal/svc"
	"nebula/internal/utils"
	"nebula/internal/utils/statusx"
	"path"
	"strings"
	"time"
)

type InternalDocumentExcelInfo struct {
	Name              string
	DocCategoryName   string
	DepartmentCode    string
	AuthorMobile      string
	PublishDate       int64
	EffectiveDate     int64
	OriginalNo        string
	OriginalVersionNo string
}

// InternalDocumentImporter 为内部文档实现 Importer 接口。
type InternalDocumentImporter struct {
	svcCtx *svc.ServiceContext
	ImporterAbility[InternalDocumentExcelInfo]
	dictionaryRelation  []DictionaryRelation
	fileNameMap         map[string]string
	organizationInfoMap map[string]entity.OrganizationInfo
	docCategoryMap      map[string]mapper.BusinessDictionaryNodeRelation
	userInfoMap         map[string]entity.UserInfo
}

// NewInternalDocumentImporter 创建一个新的 InternalDocumentImporter。
func NewInternalDocumentImporter(svcCtx *svc.ServiceContext) *InternalDocumentImporter {
	return &InternalDocumentImporter{
		svcCtx:          svcCtx,
		ImporterAbility: NewImporterAbility[InternalDocumentExcelInfo](svcCtx),
	}
}

// 解析
func (t *InternalDocumentImporter) Parse(ctx context.Context, req ImportRequest, rows [][]string) ([]InternalDocumentExcelInfo, error) {
	excelInfos := make([]InternalDocumentExcelInfo, 0, len(rows))
	for i, row := range rows {
		if len(row) < 6 {
			continue // 跳过无效行
		}
		info := InternalDocumentExcelInfo{
			Name:            strings.TrimSpace(row[0]),
			DocCategoryName: strings.TrimSpace(row[1]),
			DepartmentCode:  strings.TrimSpace(row[2]),
			AuthorMobile:    strings.TrimSpace(row[3]),
		}
		publishDate, err := time.Parse("2006/1/2", strings.TrimSpace(row[4]))
		if err != nil {
			return nil, fmt.Errorf("第%d行发布日期解析失败: %v", i+2, err)
		}
		effectiveDate, err := time.Parse("2006/1/2", strings.TrimSpace(row[5]))
		if err != nil {
			return nil, fmt.Errorf("第%d行有效期解析失败: %v", i+2, err)
		}
		info.PublishDate = publishDate.UnixMilli()
		info.EffectiveDate = effectiveDate.UnixMilli()
		if len(row) > 6 {
			info.OriginalNo = strings.TrimSpace(row[6])
		}
		if len(row) > 7 {
			info.OriginalVersionNo = strings.TrimSpace(row[7])
		}

		excelInfos = append(excelInfos, info)
	}
	return excelInfos, nil
}

// 实现 ImporterAbility 接口
func (t *InternalDocumentImporter) Validate(ctx context.Context, req ImportRequest, excelInfos []InternalDocumentExcelInfo) error {
	// 必填校验
	if len(excelInfos) == 0 {
		return errors.New("台账不能为空")
	}
	// 字段必填校验
	err := t.validateRequiredFields(excelInfos)
	if err != nil {
		return err
	}

	// 文件名校验
	if err := t.validateFileNames(ctx, req, excelInfos); err != nil {
		return err
	}
	// 分类名校验
	if err := t.validateDocCategoryNames(ctx, req, excelInfos); err != nil {
		return err
	}
	// 组织代码校验
	if err := t.validateOrganizationCodes(ctx, excelInfos); err != nil {
		return err
	}
	// 用户手机号校验
	if err := t.validateUserMobiles(ctx, excelInfos); err != nil {
		return err
	}
	return nil
}

func (t *InternalDocumentImporter) Save(ctx context.Context, req ImportRequest, data []InternalDocumentExcelInfo) error {
	// 查询组织架构 id
	organizationInfo, err := t.svcCtx.PhoenixClient.GetOrganizationInfo(ctx, utils.GetContextOrganizationID(ctx))
	if err != nil {
		return err
	}

	createReqs := t.buildCreateReqs(data, organizationInfo.Code)
	infos, err := docvault.NewInternalDocumentLibraryClient(t.svcCtx.DocvaultRpcConn).BatchCreate(ctx, &docvault.InternalDocumentBatchCreateReq{
		InternalDocumentCreateReqs: createReqs,
	})
	if err != nil {
		if statusx.IsRpcAlreadyExists(err) {
			respx.NewDefaultError("有文件已存在，导入失败，请检查后重新导入！")
		}
		return err
	}

	// 构建关系
	t.dictionaryRelation = t.buildDictionaryRelation(infos)
	return nil
}

func (t *InternalDocumentImporter) GetDictionaryRelation(ctx context.Context, req ImportRequest) ([]DictionaryRelation, error) {
	return t.dictionaryRelation, nil
}

// 文件名校验
func (t *InternalDocumentImporter) validateFileNames(ctx context.Context, req ImportRequest, excelInfos []InternalDocumentExcelInfo) error {
	if len(excelInfos) < len(req.ListFileIDs) {
		return errors.New("台账与文件不匹配，提交失败")
	}

	t.fileNameMap = make(map[string]string)
	for _, fileID := range req.ListFileIDs {
		fileName := t.svcCtx.QuickNameTranslator.TranslateFileName(ctx, fileID)
		// 去后缀
		fileName = strings.TrimSuffix(fileName, path.Ext(fileName))
		t.fileNameMap[fileName] = fileID
	}

	// 若导入的台账清单比上传的文件少，则提示台账与文件不匹配，提交失败
	if len(t.fileNameMap) != len(req.ListFileIDs) {
		return errors.New("台账与文件不匹配，提交失败")
	}

	// 若上传的文件名称中有与现有文件重名的，提示“有文件已存在，导入失败，请检查后重新导入！
	names := utils.ExtractSliceFieldAndDuplicate(excelInfos, func(excelInfo InternalDocumentExcelInfo) string {
		return excelInfo.Name
	})
	if len(names) != len(excelInfos) {
		return errors.New("有文件已存在，导入失败，请检查后重新导入！")
	}

	// 若上传的文件名称中有台账的文件名称中不存在的文件，则提示台账与文件不匹配，提交失败。
	namesMap := utils.ExtractSliceFieldToMap(excelInfos, func(excelInfo InternalDocumentExcelInfo) (string, struct{}) {
		return excelInfo.Name, struct{}{}
	})

	for k := range t.fileNameMap {
		if _, ok := namesMap[k]; !ok {
			return errors.New("台账与文件不匹配，提交失败")
		}
	}

	return nil
}

// 分类名校验
func (t *InternalDocumentImporter) validateDocCategoryNames(ctx context.Context, req ImportRequest, excelInfos []InternalDocumentExcelInfo) error {
	types := utils.ExtractSliceFieldAndDuplicate(excelInfos, func(excelInfo InternalDocumentExcelInfo) string {
		return excelInfo.DocCategoryName
	})
	if len(types) == 0 {
		return errors.New("分类类型不能为空")
	}

	count, err := mapper.NewBusinessDictionaryNodeRelationClient(t.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNames(ctx, types, req.TypeDictionaryID)
	if err != nil {
		return err
	}
	docCategoryMap := utils.ExtractSliceFieldToMap(count, func(v mapper.BusinessDictionaryNodeRelation) (string, mapper.BusinessDictionaryNodeRelation) {
		return v.Names, v
	})
	t.docCategoryMap = docCategoryMap
	for _, v := range excelInfos {
		_, ok := docCategoryMap[v.DocCategoryName]
		if !ok {
			return fmt.Errorf("分类类型%s不存在", v.DocCategoryName)
		}
	}
	return nil
}

// 组织代码校验
func (t *InternalDocumentImporter) validateOrganizationCodes(ctx context.Context, excelInfos []InternalDocumentExcelInfo) error {
	organizationCodes := make([]string, len(excelInfos))
	for i, excelInfo := range excelInfos {
		organizationCodes[i] = excelInfo.DepartmentCode
	}
	organizationInfos, err := t.svcCtx.PhoenixClient.GetOrganizationInfoByCodes(ctx, organizationCodes)
	if err != nil {
		return err
	}
	t.organizationInfoMap = utils.ExtractSliceFieldToMap(organizationInfos, func(v entity.OrganizationInfo) (string, entity.OrganizationInfo) {
		return v.Code, v
	})
	for _, v := range excelInfos {
		_, ok := t.organizationInfoMap[v.DepartmentCode]
		if !ok {
			return fmt.Errorf("部门编码%s不存在", v.DepartmentCode)
		}
	}
	return nil
}

// 用户手机号校验
func (t *InternalDocumentImporter) validateUserMobiles(ctx context.Context, excelInfos []InternalDocumentExcelInfo) error {
	userMobiles := make([]string, len(excelInfos))
	for i, excelInfo := range excelInfos {
		userMobiles[i] = excelInfo.AuthorMobile
	}
	userInfos, err := t.svcCtx.PhoenixClient.GetUserInfoByMobiles(ctx, userMobiles)
	if err != nil {
		return err
	}
	t.userInfoMap = utils.ExtractSliceFieldToMap(userInfos, func(v entity.UserInfo) (string, entity.UserInfo) {
		return v.Mobile, v
	})
	for _, v := range excelInfos {
		_, ok := t.userInfoMap[v.AuthorMobile]
		if !ok {
			return fmt.Errorf("用户手机号%s不存在", v.AuthorMobile)
		}
	}
	return nil
}

func (t *InternalDocumentImporter) buildCreateReqs(data []InternalDocumentExcelInfo, organizationCode string) []*docvault.InternalDocumentCreateReq {
	createReqs := make([]*docvault.InternalDocumentCreateReq, len(data))
	for i, v := range data {
		createReqs[i] = &docvault.InternalDocumentCreateReq{
			Name:              v.Name,
			FileId:            t.fileNameMap[v.Name],
			DocCategoryId:     t.docCategoryMap[v.DocCategoryName].NodeID,
			DepartmentId:      t.organizationInfoMap[v.DepartmentCode].Id,
			AuthorId:          t.userInfoMap[v.AuthorMobile].ID,
			PublishDate:       v.PublishDate,
			EffectiveDate:     v.EffectiveDate,
			OriginalNo:        v.OriginalNo,
			OriginalVersionNo: v.OriginalVersionNo,
			NoPrefix:          fmt.Sprintf("%s/%s", organizationCode, t.docCategoryMap[v.DocCategoryName].Codes),
		}
	}
	return createReqs
}

func (*InternalDocumentImporter) buildDictionaryRelation(infos *docvault.InternalDocumentBatchCreateResp) []DictionaryRelation {
	dictionaryRelations := make([]DictionaryRelation, len(infos.InternalDocumentInfos))
	for i, v := range infos.InternalDocumentInfos {
		dictionaryRelations[i] = DictionaryRelation{
			DictionaryNodeID: v.DocCategoryId,
			BusinessID:       v.Id,
			BusinessType:     consts.BusinessDictionaryBusinessTypeInternalDocumentCategory,
		}
	}
	return dictionaryRelations
}

func (*InternalDocumentImporter) validateRequiredFields(excelInfos []InternalDocumentExcelInfo) error {
	for _, excelInfo := range excelInfos {
		if excelInfo.Name == "" {
			return errors.New("文件名称不能为空")
		}
		if excelInfo.DocCategoryName == "" {
			return errors.New("分类类型不能为空")
		}
		if excelInfo.DepartmentCode == "" {
			return errors.New("编制部门编码不能为空")
		}
		if excelInfo.AuthorMobile == "" {
			return errors.New("编制人手机号不能为空")
		}
		if excelInfo.PublishDate == 0 {
			return errors.New("发布日期不能为空")
		}
		if excelInfo.EffectiveDate == 0 {
			return errors.New("有效期不能为空")
		}
	}
	return nil
}
