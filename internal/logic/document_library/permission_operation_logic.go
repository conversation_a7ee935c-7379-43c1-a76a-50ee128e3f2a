package document_library

import (
	"context"
	"fmt"
	"time"

	"nebula/internal/infrastructure/adapter/clientx/entity"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/kqs"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

// DistributeInfo 发放信息缓存结构
type DistributeInfo struct {
	DistributeFile   mapper.DistributeRecordFile
	DistributeRecord mapper.DistributeRecord
	DocumentName     string
	ActualFileID     string
}

type PermissionOperationLogic struct {
	logx.Logger
	ctx            context.Context
	svcCtx         *svc.ServiceContext
	distributeInfo *DistributeInfo // 缓存发放信息，避免重复查询
}

func NewPermissionOperationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PermissionOperationLogic {
	return &PermissionOperationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// PermissionOperation 权限操作接口
// 功能: 根据用户权限执行相应的文档操作，包括查阅和下载
// 业务规则:
//  1. 验证用户是否有该文档的指定权限
//  2. 如果有查阅权限，则生成水印文件并返回预览信息
//  3. 如果包含下载权限，则生成水印文件并推送到文件导出队列
//
// 参数:
//   - req: 权限操作请求，包含文档ID、文件形式、文件权限、清单ID
//
// 返回值:
//   - resp: 权限操作响应，包含文件ID和预览URL（仅查阅权限时返回）
//   - err: 错误信息
func (l *PermissionOperationLogic) PermissionOperation(req *types.PermissionOperationReq) (resp *types.PermissionOperationResp, err error) {
	// 参数验证
	if err := l.validateRequest(req); err != nil {
		l.Logger.Errorf("参数验证失败: %v", err)
		return nil, err
	}

	// 获取当前用户ID
	currentUserID := utils.GetContextUserID(l.ctx)

	// 获取发放信息（一次性查询，避免重复）
	distributeInfo, err := l.getDistributeInfo(req.InventoryID)
	if err != nil {
		l.Logger.Errorf("获取发放信息失败: %v", err)
		return nil, fmt.Errorf("获取发放信息失败: %w", err)
	}

	// 检查发放审批状态和期望发送日期
	canOperate := l.checkDistributeApprovalAndDateFromInfo(distributeInfo.DistributeRecord)
	if !canOperate {
		l.Logger.Errorf("发放记录未审批完成或期望发送日期未到，清单ID: %s", req.InventoryID)
		return nil, fmt.Errorf("发放记录未审批完成或期望发送日期未到")
	}

	// 验证用户权限
	hasPermission, err := l.checkUserPermission(currentUserID, req)
	if err != nil {
		l.Logger.Errorf("验证用户权限失败: %v", err)
		return nil, fmt.Errorf("验证用户权限失败: %w", err)
	}

	if !hasPermission {
		l.Logger.Errorf("用户 %s 没有文档 %s 的权限", currentUserID, req.DocumentID)
		return nil, fmt.Errorf("您没有该文档的访问权限")
	}

	if distributeInfo.ActualFileID == "" {
		return &types.PermissionOperationResp{}, nil
	}

	// 根据操作类型执行不同操作
	switch req.OperationType {
	case 1: // 查阅操作 - 直接返回实际文件ID，不加水印
		resp, err = l.handleViewOperationFromInfo(distributeInfo)
	case 2: // 下载操作 - 需要生成水印文件
		// 生成水印文件
		watermarkFileInfo, err := l.generateWatermarkFileFromInfo(distributeInfo.ActualFileID, currentUserID, req.FilePermission, distributeInfo)
		if err != nil {
			l.Logger.Errorf("生成水印文件失败: %v", err)
			return nil, fmt.Errorf("生成水印文件失败: %w", err)
		}

		// 根据权限类型执行不同的下载操作
		switch req.FilePermission {
		case 2: // 查阅/下载权限
			resp, err = l.handleViewDownloadPermissionFromInfo(watermarkFileInfo, currentUserID, distributeInfo)
		case 3: // 一次下载权限
			resp, err = l.handleOneTimeDownloadPermissionFromInfo(watermarkFileInfo, currentUserID, distributeInfo, req.FileForm)
		default:
			l.Logger.Errorf("下载操作不支持的权限类型: %d", req.FilePermission)
			return nil, fmt.Errorf("下载操作不支持的权限类型: %d", req.FilePermission)
		}
	default:
		return nil, fmt.Errorf("不支持的操作类型: %d", req.OperationType)
	}
	if err != nil {
		return nil, err
	}
	// 更新权限使用状态
	_, err = docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).UpdatePermissionUsedStatus(l.ctx, &docvault.UpdatePermissionUsedStatusReq{
		DistributeRecordFileId: distributeInfo.DistributeFile.ID,
		FileForm:               int32(req.FileForm),
		FilePermission:         int32(req.FilePermission),
		UserId:                 currentUserID,
		IsUsed:                 true,
	})
	if err != nil {
		return nil, fmt.Errorf("更新用户处置状态失败: %w", err)
	}

	return resp, nil
}

// getDistributeInfo 获取发放信息（带缓存）
// 功能: 获取发放记录信息，包括文档名称和实际文件ID，避免重复查询
// 参数:
//   - inventoryID: 清单ID
//
// 返回值:
//   - *DistributeInfo: 发放信息
//   - error: 错误信息
func (l *PermissionOperationLogic) getDistributeInfo(inventoryID string) (*DistributeInfo, error) {
	// 如果已经缓存，直接返回
	if l.distributeInfo != nil {
		return l.distributeInfo, nil
	}

	// 创建数据库客户端
	distributeFileClient := mapper.NewDistributeRecordFileClient(l.svcCtx.DocvaultDB)
	distributeRecordClient := mapper.NewDistributeRecordClient(l.svcCtx.DocvaultDB)

	// 根据清单ID查询发放记录文件
	distributeFile, err := distributeFileClient.FindByID(inventoryID)
	if err != nil {
		return nil, fmt.Errorf("查询发放记录文件失败: %w", err)
	}

	// 根据RecordID查询发放记录
	distributeRecord, err := distributeRecordClient.FindByID(l.ctx, distributeFile.RecordID)
	if err != nil {
		return nil, fmt.Errorf("查询发放记录失败: %w", err)
	}

	// 获取实际文件ID和文档名称
	actualFileID, documentName, err := l.getDocumentInfoByType(distributeRecord.FileType, distributeFile.FileID)
	if err != nil {
		return nil, fmt.Errorf("获取文档信息失败: %w", err)
	}

	// 缓存结果
	l.distributeInfo = &DistributeInfo{
		DistributeFile:   distributeFile,
		DistributeRecord: distributeRecord,
		DocumentName:     documentName,
		ActualFileID:     actualFileID,
	}

	return l.distributeInfo, nil
}

// getDocumentInfoByType 根据文件类型获取文档信息
// 功能: 根据文件类型查询对应的文档库，获取文件ID和文档名称
// 参数:
//   - fileType: 文件类型（1=内部文件，2=外部文件）
//   - documentID: 文档ID
//
// 返回值:
//   - string: 实际文件ID
//   - string: 文档名称
//   - error: 错误信息
func (l *PermissionOperationLogic) getDocumentInfoByType(fileType int32, documentID string) (string, string, error) {
	switch fileType {
	case 1: // 内部文件
		internalDocClient := mapper.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
		internalDoc, err := internalDocClient.GetByID(l.ctx, documentID)
		if err != nil {
			return "", "", fmt.Errorf("查询内部文档库失败: %w", err)
		}

		documentName := internalDoc.Name
		if documentName == "" {
			documentName = fmt.Sprintf("内部文档_%s", documentID)
		}

		return internalDoc.FileID, documentName, nil

	case 2: // 外部文件
		externalDocClient := mapper.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
		externalDoc, err := externalDocClient.GetByID(l.ctx, documentID)
		if err != nil {
			return "", "", fmt.Errorf("查询外部文档库失败: %w", err)
		}

		documentName := externalDoc.Name
		if documentName == "" {
			documentName = fmt.Sprintf("外部文档_%s", documentID)
		}

		return externalDoc.FileID, documentName, nil

	default:
		return "", "", fmt.Errorf("不支持的文件类型: %d", fileType)
	}
}

// checkDistributeApprovalAndDateFromInfo 从发放记录信息检查审批状态和期望发送日期
// 功能: 检查发放记录是否已审批完成且期望发送日期已到
// 参数:
//   - distributeRecord: 发放记录
//
// 返回值:
//   - bool: 是否可以操作
func (l *PermissionOperationLogic) checkDistributeApprovalAndDateFromInfo(distributeRecord mapper.DistributeRecord) bool {
	// 检查审批状态：必须是已审批(3)
	if distributeRecord.Status != 3 {
		l.Logger.Infof("发放记录未审批完成，当前状态: %d", distributeRecord.Status)
		return false
	}

	// 检查期望发送日期：必须在今天之前或等于今天
	today := time.Now().UnixMilli()
	if distributeRecord.WishDistributeDate > today {
		l.Logger.Infof("期望发送日期未到，期望日期: %d, 当前日期: %d",
			distributeRecord.WishDistributeDate, today)
		return false
	}

	l.Logger.Infof("发放记录检查通过，状态: %d, 期望日期: %d",
		distributeRecord.Status, distributeRecord.WishDistributeDate)
	return true
}

// handleViewOperationFromInfo 处理查阅操作（使用缓存信息）
// 功能: 对于查阅操作，直接使用缓存的实际文件ID，不需要生成水印
// 参数:
//   - distributeInfo: 发放信息
//
// 返回值:
//   - *types.PermissionOperationResp: 响应信息，包含实际文件ID
//   - error: 错误信息
func (l *PermissionOperationLogic) handleViewOperationFromInfo(distributeInfo *DistributeInfo) (*types.PermissionOperationResp, error) {
	resp := &types.PermissionOperationResp{
		FileID: distributeInfo.ActualFileID,
	}

	l.Logger.Infof("查阅操作处理完成，返回实际文件ID: %s", resp.FileID)
	return resp, nil
}

// generateWatermarkFileFromInfo 生成水印文件（使用缓存信息）
// 功能: 为指定文档生成带有用户信息的水印文件，根据权限类型、文件类型和发放类型生成不同的水印内容
// 参数:
//   - documentID: 文档ID（实际文件ID）
//   - userID: 用户ID
//   - filePermission: 文件权限类型 (1=查阅, 2=查阅/下载, 3=一次下载)
//   - distributeInfo: 发放信息
//
// 返回值:
//   - entity.GenerateWatermarkedFileInfo: 水印文件信息
//   - error: 错误信息
func (l *PermissionOperationLogic) generateWatermarkFileFromInfo(documentID, userID string, filePermission int64, distributeInfo *DistributeInfo) (entity.GenerateWatermarkedFileInfo, error) {
	// 获取用户昵称
	userNickname := l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, userID)
	if userNickname == "" {
		userNickname = "未知用户"
	}

	// 根据权限类型生成不同的水印内容
	var watermarkText string
	switch filePermission {
	case 1: // 查阅权限 - 在线预览水印
		watermarkText = l.generatePreviewWatermark(userNickname)
	case 2, 3: // 查阅/下载权限 或 一次下载权限 - 需要根据文件类型和发放类型生成下载水印
		var err error
		watermarkText, err = l.generateDownloadWatermarkFromInfo(distributeInfo)
		if err != nil {
			return entity.GenerateWatermarkedFileInfo{}, fmt.Errorf("生成下载水印失败: %w", err)
		}
	default:
		return entity.GenerateWatermarkedFileInfo{}, fmt.Errorf("不支持的权限类型: %d", filePermission)
	}

	watermarkReq := entity.GenerateWatermarkedFileReq{
		ID:            documentID,
		WatermarkText: watermarkText,
		// 设置默认水印样式
		FontSize: 20,
	}

	// 调用Phoenix客户端生成水印文件
	watermarkInfo, err := l.svcCtx.PhoenixClient.GenerateWatermarkedFile(l.ctx, watermarkReq)
	if err != nil {
		return entity.GenerateWatermarkedFileInfo{}, fmt.Errorf("调用Phoenix生成水印文件失败: %w", err)
	}

	return watermarkInfo, nil
}

// generatePreviewWatermark 生成在线预览水印内容
// 功能: 生成在线预览时使用的水印文本
// 参数:
//   - userNickname: 用户昵称
//
// 返回值:
//   - string: 水印文本内容
func (l *PermissionOperationLogic) generatePreviewWatermark(userNickname string) string {
	previewDate := time.Now().Format("2006-01-02")
	return fmt.Sprintf("%s %s 本文件涉密 禁止拍照或截图", previewDate, userNickname)
}

// generateDownloadWatermarkFromInfo 生成下载水印内容（使用缓存信息）
// 功能: 根据文件类型和发放类型生成相应的下载水印文本
// 参数:
//   - distributeInfo: 发放信息
//
// 返回值:
//   - string: 水印文本内容
//   - error: 错误信息
func (l *PermissionOperationLogic) generateDownloadWatermarkFromInfo(distributeInfo *DistributeInfo) (string, error) {
	// 根据文件类型和发放类型生成水印内容
	switch distributeInfo.DistributeRecord.FileType {
	case 1: // 内部文件
		return l.generateInternalFileDownloadWatermarkFromInfo(distributeInfo.DistributeRecord)
	case 2: // 外部文件
		return l.generateExternalFileDownloadWatermark(), nil
	default:
		return "", fmt.Errorf("不支持的文件类型: %d", distributeInfo.DistributeRecord.FileType)
	}
}

// generateInternalFileDownloadWatermarkFromInfo 生成内部文件下载水印内容（使用缓存信息）
// 功能: 根据发放类型生成内部文件的下载水印
// 参数:
//   - distributeRecord: 发放记录
//
// 返回值:
//   - string: 水印文本内容
//   - error: 错误信息
func (l *PermissionOperationLogic) generateInternalFileDownloadWatermarkFromInfo(distributeRecord mapper.DistributeRecord) (string, error) {
	switch distributeRecord.DistributeType {
	case 1: // 内部发放
		// 获取组织名称
		orgName := l.svcCtx.QuickNameTranslator.TranslateOrganizationName(l.ctx, distributeRecord.OrganizationID)
		return fmt.Sprintf("%s 内部使用文件", orgName), nil
	case 2: // 外部发放
		// 获取审批日期和接收方信息
		approvalDate := l.getApprovalDateFromInfo(distributeRecord)
		recipient := l.getRecipientInfoFromInfo(distributeRecord)
		return fmt.Sprintf("参考文件，变更不予通知/作废不予回收。\n发放日期：%s\n接收方：%s·严禁转发", approvalDate, recipient), nil
	default:
		// 默认使用普通内部文件下载水印
		return "文件下载后单次生效，可能存在变更，实际文件以系统内为准", nil
	}
}

// getApprovalDateFromInfo 获取审批通过日期（使用缓存信息）
// 功能: 从发放记录的审批信息中获取审批通过日期
// 参数:
//   - distributeRecord: 发放记录
//
// 返回值:
//   - string: 审批日期（格式：YYYY-MM-DD）
func (l *PermissionOperationLogic) getApprovalDateFromInfo(distributeRecord mapper.DistributeRecord) string {
	// TODO: 从ApprovalInfo JSON字段中解析审批日期
	// 这里先返回默认值，实际应该解析JSON获取审批通过的日期
	if distributeRecord.UpdatedAt.IsZero() {
		return "XXXXXX"
	}
	return distributeRecord.UpdatedAt.Format("2006-01-02")
}

// getRecipientInfoFromInfo 获取接收方信息（使用缓存信息）
// 功能: 从发放记录中获取接收方信息
// 参数:
//   - distributeRecord: 发放记录
//
// 返回值:
//   - string: 接收方信息
func (l *PermissionOperationLogic) getRecipientInfoFromInfo(distributeRecord mapper.DistributeRecord) string {
	// 如果有申请人信息，返回申请人
	if distributeRecord.Applicant != "" {
		return distributeRecord.Applicant
	}
	return "XXXXXXXXX"
}

// handleViewDownloadPermissionFromInfo 处理查阅/下载权限（使用缓存信息）
// 功能: 对于查阅/下载权限，生成水印文件并推送到导出队列，同时返回预览信息
// 参数:
//   - watermarkInfo: 水印文件信息
//   - userID: 用户ID
//   - distributeInfo: 发放信息
//
// 返回值:
//   - *types.PermissionOperationResp: 响应信息，包含文件ID和预览URL
//   - error: 错误信息
func (l *PermissionOperationLogic) handleViewDownloadPermissionFromInfo(watermarkInfo entity.GenerateWatermarkedFileInfo, userID string, distributeInfo *DistributeInfo) (*types.PermissionOperationResp, error) {
	// 推送到文件导出队列
	if err := l.pushToExportQueueFromInfo(watermarkInfo, userID, "查阅/下载", distributeInfo); err != nil {
		l.Logger.Errorf("推送到导出队列失败: %v", err)
		return nil, fmt.Errorf("推送到导出队列失败: %w", err)
	}

	// 同时返回预览信息供查阅使用
	resp := &types.PermissionOperationResp{}
	if watermarkInfo.ID != nil {
		resp.FileID = *watermarkInfo.ID
	}
	if watermarkInfo.PreviewURL != nil {
		resp.PreviewURL = *watermarkInfo.PreviewURL
	}

	l.Logger.Infof("查阅/下载权限处理完成，已推送到导出队列并返回预览信息: FileID=%s", resp.FileID)
	return resp, nil
}

// handleOneTimeDownloadPermissionFromInfo 处理一次下载权限（使用缓存信息）
// 功能: 对于一次下载权限，生成水印文件并推送到导出队列
// 参数:
//   - watermarkInfo: 水印文件信息
//   - userID: 用户ID
//   - distributeInfo: 发放信息
//
// 返回值:
//   - *types.PermissionOperationResp: 响应信息
//   - error: 错误信息
func (l *PermissionOperationLogic) handleOneTimeDownloadPermissionFromInfo(watermarkInfo entity.GenerateWatermarkedFileInfo, userID string, distributeInfo *DistributeInfo, fileForm int64) (*types.PermissionOperationResp, error) {
	// 推送到文件导出队列
	if err := l.pushToExportQueueFromInfo(watermarkInfo, userID, "一次下载", distributeInfo); err != nil {
		l.Logger.Errorf("推送到导出队列失败: %v", err)
		return nil, fmt.Errorf("推送到导出队列失败: %w", err)
	}

	// 一次下载权限不返回预览信息，只返回成功状态
	resp := &types.PermissionOperationResp{}
	if watermarkInfo.ID != nil {
		resp.FileID = *watermarkInfo.ID
	}

	return resp, nil
}

// pushToExportQueueFromInfo 推送到文件导出队列（使用缓存信息）
// 功能: 将水印文件信息推送到Kafka导出队列，使用缓存的发放信息避免重复查询
// 参数:
//   - watermarkInfo: 水印文件信息
//   - userID: 用户ID
//   - operationType: 操作类型（查阅/下载、一次下载）
//   - distributeInfo: 发放信息
//
// 返回值:
//   - error: 错误信息
func (l *PermissionOperationLogic) pushToExportQueueFromInfo(watermarkInfo entity.GenerateWatermarkedFileInfo, userID, operationType string, distributeInfo *DistributeInfo) error {
	if l.svcCtx.KafkaDataExportProducer == nil {
		return fmt.Errorf("Kafka导出生产者未初始化")
	}

	// 根据文件类型确定模块名称
	var moduleName string
	switch distributeInfo.DistributeRecord.FileType {
	case 1: // 内部文件
		moduleName = "文件管理-内部文件库"
	case 2: // 外部文件
		moduleName = "文件管理-外部文件库"
	default:
		moduleName = "文件管理-未知文件库"
	}

	exportInfo := kqs.DataExportModelInfo{
		TaskID:     l.svcCtx.IdGenerator.GenerateIDString(),
		FileName:   distributeInfo.DocumentName,
		ModuleName: moduleName,
		UserID:     userID,
		Status:     kqs.DataExportStatusProgress, // 第一步：推送导出中状态
	}

	// 第一步：推送导出中状态
	if err := l.svcCtx.KafkaDataExportProducer.SendMessage(l.ctx, exportInfo); err != nil {
		return fmt.Errorf("推送导出中状态失败: %w", err)
	}

	// 推送导出完成状态
	exportInfo.Status = kqs.DataExportStatusComplete
	if watermarkInfo.ID != nil {
		exportInfo.FileID = *watermarkInfo.ID
	}

	if err := l.svcCtx.KafkaDataExportProducer.SendMessage(l.ctx, exportInfo); err != nil {
		l.Logger.Errorf("推送导出完成状态失败: %v", err)
	} else {
		l.Logger.Infof("已推送导出完成状态到队列，TaskID: %s, FileID: %s", exportInfo.TaskID, exportInfo.FileID)
	}

	return nil
}

// validateRequest 验证请求参数
// 功能: 验证权限操作请求的必要参数
// 参数:
//   - req: 权限操作请求
//
// 返回值:
//   - error: 验证失败时返回错误信息
func (l *PermissionOperationLogic) validateRequest(req *types.PermissionOperationReq) error {
	if req.DocumentID == "" {
		return fmt.Errorf("文档ID不能为空")
	}
	if req.InventoryID == "" {
		return fmt.Errorf("清单ID不能为空")
	}
	if req.FileForm != 1 && req.FileForm != 2 {
		return fmt.Errorf("文件形式必须为1(电子文件)或2(纸质文件)")
	}
	if req.FilePermission < 1 || req.FilePermission > 3 {
		return fmt.Errorf("文件权限必须为1(查阅)、2(查阅/下载)或3(一次下载)")
	}
	return nil
}

// checkUserPermission 检查用户权限
// 功能: 验证当前用户是否有指定文档的权限
// 参数:
//   - userID: 用户ID
//   - req: 权限操作请求
//
// 返回值:
//   - bool: 是否有权限
//   - error: 错误信息
func (l *PermissionOperationLogic) checkUserPermission(userID string, req *types.PermissionOperationReq) (bool, error) {
	// 创建权限查询客户端
	permissionClient := mapper.NewDistributeRecordPermissionClient(l.svcCtx.DocvaultDB)

	// 查询用户对该文档的权限记录
	permissions, err := permissionClient.FindByFileRecordIDsWithStatus([]string{req.InventoryID})
	if err != nil {
		return false, fmt.Errorf("查询用户权限失败: %w", err)
	}

	// 检查是否有匹配的权限记录
	for _, permission := range permissions {
		// 检查用户ID、文件形式和权限级别
		if permission.UserID == userID &&
			permission.FileForm == int32(req.FileForm) &&
			permission.FilePermission == int32(req.FilePermission) {
			if permission.IsUsed && req.FilePermission == 3 {
				// 一次下载权限且已使用过，直接返回无权限
				return false, nil
			}
			return true, nil
		}
	}

	return false, nil
}

// generateExternalFileDownloadWatermark 生成外部文件下载水印内容
// 功能: 生成外部文件下载时使用的水印文本
// 返回值:
//   - string: 水印文本内容
func (l *PermissionOperationLogic) generateExternalFileDownloadWatermark() string {
	return "中一检测 内部收藏"
}
