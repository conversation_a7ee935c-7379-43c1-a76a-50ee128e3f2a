package document_library

import (
	"context"
	"fmt"
	"sort"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInternalPaperDownloadRecordsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInternalPaperDownloadRecordsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInternalPaperDownloadRecordsLogic {
	return &GetInternalPaperDownloadRecordsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetInternalPaperDownloadRecords 获取内发纸质文件一次下载变更记录
// 功能: 根据文档ID分页查询内发纸质文件一次下载变更记录，只显示通过发放审批的数据
// 参数:
//   - req: 请求参数，包含文档ID和分页信息
//
// 返回值:
//   - resp: 内发纸质文件下载记录列表响应
//   - err: 错误信息
func (l *GetInternalPaperDownloadRecordsLogic) GetInternalPaperDownloadRecords(req *types.GetInternalPaperDownloadRecordsReq) (resp *types.GetInternalPaperDownloadRecordsResp, err error) {
	// 1. 查询发放记录文件
	distributeFiles, err := l.queryDistributeFiles(req.DocumentID)
	if err != nil {
		return nil, err
	}

	if len(distributeFiles) == 0 {
		return l.buildEmptyResponse(req), nil
	}

	// 2. 查询发放记录
	records, fileRecordMap, err := l.queryDistributeRecords(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 3. 查询并过滤权限记录
	paperDownloadPermissions, err := l.queryAndFilterPermissions(distributeFiles)
	if err != nil {
		return nil, err
	}

	// 4. 构建响应数据
	paperDownloadRecords := l.buildRecords(paperDownloadPermissions, fileRecordMap, records)

	// 5. 排序和分页
	l.sortRecordsByDistributeApplyTimeDesc(paperDownloadRecords)
	pagedRecords, total := l.applyPagination(paperDownloadRecords, req.PageInfo)

	return &types.GetInternalPaperDownloadRecordsResp{
		Data: pagedRecords,
		PageInfo: types.PageInfo{
			Page:     req.PageInfo.Page,
			PageSize: req.PageInfo.PageSize,
			Total:    total,
		},
	}, nil
}

// getPaperFileStatus 获取纸质文件状态
// 功能: 根据处置状态获取纸质文件状态
// 参数:
//   - disposeStatus: 处置状态（1未回收 | 2回收审批中 | 3已回收 | 4处置审批中 | 5已处置）
//
// 返回值:
//   - int32: 纸质文件状态（1未回收 | 2回收中 | 3已回收）
func (l *GetInternalPaperDownloadRecordsLogic) getPaperFileStatus(disposeStatus int32) int32 {
	switch disposeStatus {
	case 1: // 未回收
		return 1
	case 2: // 回收审批中
		return 2
	case 3, 4, 5: // 已回收、处置审批中、已处置
		return 3
	default:
		return 1
	}
}

// getFileDisposeStatus 获取文件处置状态
// 功能: 根据处置状态获取文件处置状态
// 参数:
//   - disposeStatus: 处置状态
//
// 返回值:
//   - int32: 文件处置状态（1未处置 | 2处置中 | 3已处置）
func (l *GetInternalPaperDownloadRecordsLogic) getFileDisposeStatus(disposeStatus int32) int32 {
	switch disposeStatus {
	case 1, 2, 3: // 未回收、回收审批中、已回收
		return 1 // 未处置
	case 4: // 处置审批中
		return 2 // 处置中
	case 5: // 已处置
		return 3 // 已处置
	default:
		return 1
	}
}

// getUserNickname 获取用户昵称
// 功能: 根据用户ID从Redis查询用户昵称，如果获取失败则返回用户ID
// 参数:
//   - userID: 用户ID
//
// 返回值:
//   - string: 用户昵称或用户ID
func (l *GetInternalPaperDownloadRecordsLogic) getUserNickname(userID string) string {
	if userID == "" {
		return ""
	}

	// 使用QuickNameTranslator从Redis查询用户昵称
	nickname := l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, userID)
	if nickname == "" {
		// 如果查询失败，返回用户ID作为备用
		return userID
	}

	return nickname
}

// queryDistributeFiles 查询发放记录文件
func (l *GetInternalPaperDownloadRecordsLogic) queryDistributeFiles(documentID string) ([]mapper.DistributeRecordFile, error) {
	distributeRecordFileClient := mapper.NewDistributeRecordFileClient(l.svcCtx.DocvaultDB)
	distributeFiles, err := distributeRecordFileClient.FindByFileID(documentID)
	if err != nil {
		l.Logger.Errorf("查询发放记录文件失败: %v", err)
		return nil, fmt.Errorf("查询发放记录失败")
	}
	return distributeFiles, nil
}

// buildEmptyResponse 构建空响应
func (l *GetInternalPaperDownloadRecordsLogic) buildEmptyResponse(req *types.GetInternalPaperDownloadRecordsReq) *types.GetInternalPaperDownloadRecordsResp {
	return &types.GetInternalPaperDownloadRecordsResp{
		Data: []types.InternalPaperDownloadRecord{},
		PageInfo: types.PageInfo{
			Page:     req.PageInfo.Page,
			PageSize: req.PageInfo.PageSize,
			Total:    0,
		},
	}
}

// queryDistributeRecords 查询发放记录
func (l *GetInternalPaperDownloadRecordsLogic) queryDistributeRecords(distributeFiles []mapper.DistributeRecordFile) ([]mapper.DistributeRecord, map[string]mapper.DistributeRecordFile, error) {
	distributeRecordClient := mapper.NewDistributeRecordClient(l.svcCtx.DocvaultDB)

	recordIDs := make([]string, 0, len(distributeFiles))
	fileRecordMap := make(map[string]mapper.DistributeRecordFile)
	for _, file := range distributeFiles {
		recordIDs = append(recordIDs, file.RecordID)
		fileRecordMap[file.ID] = file
	}

	uniqueRecordIDs := utils.SliceDuplicate(recordIDs)
	records := make([]mapper.DistributeRecord, 0)
	for _, recordID := range uniqueRecordIDs {
		record, err := distributeRecordClient.FindByID(l.ctx, recordID)
		if err != nil {
			l.Logger.Errorf("查询发放记录失败, recordID: %s, error: %v", recordID, err)
			continue
		}
		if record.Status == 3 && record.DistributeType == 1 {
			records = append(records, record)
		}
	}

	return records, fileRecordMap, nil
}

// queryAndFilterPermissions 查询并过滤权限记录
func (l *GetInternalPaperDownloadRecordsLogic) queryAndFilterPermissions(distributeFiles []mapper.DistributeRecordFile) ([]mapper.DistributeRecordPermission, error) {
	distributeRecordPermissionClient := mapper.NewDistributeRecordPermissionClient(l.svcCtx.DocvaultDB)

	fileRecordIDs := make([]string, 0, len(distributeFiles))
	for _, file := range distributeFiles {
		fileRecordIDs = append(fileRecordIDs, file.ID)
	}

	permissions, err := distributeRecordPermissionClient.FindByFileRecordIDs(fileRecordIDs)
	if err != nil {
		l.Logger.Errorf("查询权限记录失败: %v", err)
		return nil, fmt.Errorf("查询权限记录失败")
	}

	paperDownloadPermissions := make([]mapper.DistributeRecordPermission, 0)
	for _, perm := range permissions {
		if perm.FileForm == 2 && perm.FilePermission == 3 {
			paperDownloadPermissions = append(paperDownloadPermissions, perm)
		}
	}

	return paperDownloadPermissions, nil
}

// buildRecords 构建响应数据
func (l *GetInternalPaperDownloadRecordsLogic) buildRecords(permissions []mapper.DistributeRecordPermission, fileRecordMap map[string]mapper.DistributeRecordFile, records []mapper.DistributeRecord) []types.InternalPaperDownloadRecord {
	paperDownloadRecords := make([]types.InternalPaperDownloadRecord, 0)

	for _, perm := range permissions {
		fileRecord := fileRecordMap[perm.FileRecordID]

		var distributeRecord *mapper.DistributeRecord
		for _, record := range records {
			if record.ID == fileRecord.RecordID {
				distributeRecord = &record
				break
			}
		}

		if distributeRecord == nil {
			continue
		}

		paperDownloadRecord := types.InternalPaperDownloadRecord{
			RecordID:                perm.ID,
			WorkflowID:              distributeRecord.WorkflowID,
			DistributeRecordID:      distributeRecord.ID, // 发放记录ID
			RecipientUserID:         perm.UserID,
			RecipientUserName:       perm.UserName,
			PaperFileStatus:         l.getPaperFileStatus(perm.DisposeStatus),
			FileDisposeStatus:       l.getFileDisposeStatus(perm.DisposeStatus),
			DisposalMethod:          "",
			DistributeApplicant:     distributeRecord.Applicant,
			DistributeApplicantName: l.getUserNickname(distributeRecord.Applicant),
			DistributeApplyTime:     distributeRecord.ApplyDate,
			RecycleApplicant:        "",
			RecycleApplicantName:    "",
			RecycleApplyTime:        0,
			InventoryID:             fileRecord.ID, // 清单ID（发放记录文件ID）
		}

		paperDownloadRecords = append(paperDownloadRecords, paperDownloadRecord)
	}

	return paperDownloadRecords
}

// applyPagination 应用分页
func (l *GetInternalPaperDownloadRecordsLogic) applyPagination(records []types.InternalPaperDownloadRecord, pageInfo types.PageInfo) ([]types.InternalPaperDownloadRecord, uint64) {
	total := uint64(len(records))
	start := int((pageInfo.Page - 1) * pageInfo.PageSize)
	end := int(start + int(pageInfo.PageSize))

	if start >= len(records) {
		return []types.InternalPaperDownloadRecord{}, total
	} else if end > len(records) {
		return records[start:], total
	} else {
		return records[start:end], total
	}
}

// sortRecordsByDistributeApplyTimeDesc 按发放申请时间倒序排列
func (l *GetInternalPaperDownloadRecordsLogic) sortRecordsByDistributeApplyTimeDesc(records []types.InternalPaperDownloadRecord) {
	sort.Slice(records, func(i, j int) bool {
		return records[i].DistributeApplyTime > records[j].DistributeApplyTime
	})
}
