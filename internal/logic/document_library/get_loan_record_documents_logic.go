package document_library

import (
	"context"
	"fmt"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLoanRecordDocumentsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLoanRecordDocumentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLoanRecordDocumentsLogic {
	return &GetLoanRecordDocumentsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetLoanRecordDocuments 获取借阅记录文档清单
// 功能: 根据借阅记录ID查询该记录下的所有文档信息
// 参数: req - 查询请求参数，包含借阅记录ID
// 返回值: resp - 文档清单响应，err - 错误信息
// 异常: 当借阅记录ID为空或查询失败时返回错误
func (l *GetLoanRecordDocumentsLogic) GetLoanRecordDocuments(req *types.GetLoanRecordDocumentsReq) (resp *types.GetLoanRecordDocumentsResp, err error) {
	// 实现步骤：
	// 1. 验证请求参数
	// 2. 调用mapper层查询借阅文档关系
	// 3. 转换数据格式为响应结构
	// 4. 返回结果

	// 验证请求参数
	if req.Id == "" {
		return nil, fmt.Errorf("借阅记录ID不能为空")
	}

	// 创建借阅记录查询客户端
	borrowerClient := mapper.NewBorrowRecordClient(l.svcCtx.DocvaultDB)

	// 查询借阅记录下的文档关系
	documentRelations, err := borrowerClient.GetDocumentsByBorrowRecordID(l.ctx, req.Id)
	if err != nil {
		l.Logger.Errorf("查询借阅记录文档失败: %v", err)
		return nil, fmt.Errorf("查询借阅记录文档失败: %w", err)
	}

	// 批量查询文档详细信息
	documentDetails, categoryNames, err := l.batchQueryDocumentDetails(documentRelations)
	if err != nil {
		l.Logger.Errorf("批量查询文档详细信息失败: %v", err)
		return nil, fmt.Errorf("批量查询文档详细信息失败: %w", err)
	}

	// 转换为响应格式
	loanDocuments := make([]types.LoanDocument, 0, len(documentRelations))
	for _, relation := range documentRelations {
		loanDoc := types.LoanDocument{
			DocumentId:         relation.DocumentID,
			DocumentModuleType: int32(relation.ModuleType),
			DocumentVersionNo:  relation.VersionNo,
			BorrowStatus:       int32(relation.BorrowStatus),
		}

		// 转换moduleType为中文
		loanDoc.DocumentModuleName = l.convertModuleTypeToChineseName(relation.ModuleType)

		// 从批量查询结果中填充文档详细信息
		l.fillDocumentDetailsFromMap(&loanDoc, relation.DocumentID, relation.ModuleType, documentDetails, categoryNames)

		loanDocuments = append(loanDocuments, loanDoc)
	}

	// 构造响应
	resp = &types.GetLoanRecordDocumentsResp{
		Data: loanDocuments,
	}

	return resp, nil
}

// convertModuleTypeToChineseName 将模块类型转换为中文名称
// 1：书籍，2：内部文档，3：外部文档
func (l *GetLoanRecordDocumentsLogic) convertModuleTypeToChineseName(moduleType int) string {
	switch moduleType {
	case 1:
		return "书籍"
	case 2:
		return "内部文档"
	case 3:
		return "外部文档"
	default:
		return "未知类型"
	}
}

// fillDocumentDetails 填充文档详细信息
// batchQueryDocumentDetails 批量查询文档详细信息，避免N+1查询问题
func (l *GetLoanRecordDocumentsLogic) batchQueryDocumentDetails(relations []mapper.BorrowDocumentRelation) (map[string]interface{}, map[string]string, error) {
	// 按模块类型分组收集文档ID
	internalDocIDs := make([]string, 0)
	externalDocIDs := make([]string, 0)
	categoryIDs := make(map[string]bool)

	for _, relation := range relations {
		switch relation.ModuleType {
		case 2:
			internalDocIDs = append(internalDocIDs, relation.DocumentID)
		case 3:
			externalDocIDs = append(externalDocIDs, relation.DocumentID)
		}
	}

	documentDetails := make(map[string]interface{})

	// 批量查询内部文档
	if len(internalDocIDs) > 0 {
		internalDocs, err := l.getInternalDocumentsByIDs(internalDocIDs)
		if err != nil {
			return nil, nil, fmt.Errorf("批量查询内部文档失败: %w", err)
		}
		for _, doc := range internalDocs {
			documentDetails[doc.ID] = &doc
			if doc.DocCategoryID != "" {
				categoryIDs[doc.DocCategoryID] = true
			}
		}
	}

	// 批量查询外部文档
	if len(externalDocIDs) > 0 {
		externalClient := mapper.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
		externalDocs, err := externalClient.GetByIDs(l.ctx, externalDocIDs)
		if err != nil {
			return nil, nil, fmt.Errorf("批量查询外部文档失败: %w", err)
		}
		for _, doc := range externalDocs {
			documentDetails[doc.ID] = &doc
			if doc.TypeDictionaryNodeId != "" {
				categoryIDs[doc.TypeDictionaryNodeId] = true
			}
		}
	}

	// 批量查询类别名称
	categoryNames := make(map[string]string)
	if len(categoryIDs) > 0 {
		categoryIDList := make([]string, 0, len(categoryIDs))
		for id := range categoryIDs {
			categoryIDList = append(categoryIDList, id)
		}
		categoryRelations, err := l.getCategoryNamesByIDs(categoryIDList)
		if err != nil {
			return nil, nil, fmt.Errorf("批量查询类别名称失败: %w", err)
		}
		for _, category := range categoryRelations {
			categoryNames[category.NodeID] = category.Names
		}
	}

	return documentDetails, categoryNames, nil
}

// fillDocumentDetailsFromMap 从批量查询结果中填充文档详细信息
func (l *GetLoanRecordDocumentsLogic) fillDocumentDetailsFromMap(loanDoc *types.LoanDocument, documentID string, moduleType int, documentDetails map[string]interface{}, categoryNames map[string]string) {
	switch moduleType {
	case 1:
		// 书籍 - 暂时不处理，因为没有找到书籍相关的mapper
		loanDoc.DocumentName = "书籍文档"
		loanDoc.DocumentNo = ""
	case 2:
		// 内部文档
		if docInterface, ok := documentDetails[documentID]; ok {
			if doc, ok := docInterface.(*mapper.InternalDocumentLibrary); ok {
				loanDoc.DocumentName = doc.Name
				loanDoc.DocumentNo = doc.No
				loanDoc.DocumentValidity = int32(doc.EffectiveDate.Unix())
				if categoryName, exists := categoryNames[doc.DocCategoryID]; exists {
					loanDoc.DocumentCategoryName = categoryName
				}
			}
		}
	case 3:
		// 外部文档
		if docInterface, ok := documentDetails[documentID]; ok {
			if doc, ok := docInterface.(*mapper.ExternalDocumentLibrary); ok {
				loanDoc.DocumentName = doc.Name
				loanDoc.DocumentNo = doc.Number
				loanDoc.DocumentValidity = int32(doc.EffectiveDate.Unix())
				if categoryName, exists := categoryNames[doc.TypeDictionaryNodeId]; exists {
					loanDoc.DocumentCategoryName = categoryName
				}
			}
		}
	}
}

// getInternalDocumentsByIDs 批量查询内部文档
// 功能: 根据文档ID列表批量查询内部文档信息
// 参数: ids - 文档ID列表
// 返回值: 内部文档列表, 错误信息
// 异常: 当数据库查询失败时返回错误
func (l *GetLoanRecordDocumentsLogic) getInternalDocumentsByIDs(ids []string) ([]mapper.InternalDocumentLibrary, error) {
	if len(ids) == 0 {
		return []mapper.InternalDocumentLibrary{}, nil
	}
	// 使用InternalDocumentLibraryClient进行批量查询
	internalDocClient := mapper.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
	return internalDocClient.GetByIDs(l.ctx, ids)
}

// getCategoryNamesByIDs 批量查询类别名称
// 功能: 根据节点ID列表批量查询业务字典节点关系信息
// 参数: nodeIDs - 节点ID列表
// 返回值: 业务字典节点关系列表, 错误信息
// 异常: 当数据库查询失败时返回错误
func (l *GetLoanRecordDocumentsLogic) getCategoryNamesByIDs(nodeIDs []string) ([]mapper.BusinessDictionaryNodeRelation, error) {
	if len(nodeIDs) == 0 {
		return []mapper.BusinessDictionaryNodeRelation{}, nil
	}
	// 使用BusinessDictionaryNodeRelationClient进行批量查询
	businessDictClient := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB)
	return businessDictClient.GetBusinessDictionaryNodeRelationByNodeIDs(l.ctx, nodeIDs)
}
