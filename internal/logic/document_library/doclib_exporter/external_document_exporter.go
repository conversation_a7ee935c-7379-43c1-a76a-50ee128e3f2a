package doclib_exporter

import (
	"context"
	"encoding/json"
	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
	"time"
)

type ExternalDocumentExporter struct {
	svcCtx *svc.ServiceContext
	ExporterAbility
}

func NewExternalDocumentExporter(svcCtx *svc.ServiceContext) ExporterAbility {
	exporter := &ExternalDocumentExporter{
		svcCtx:          svcCtx,
		ExporterAbility: NewExporterAbility(svcCtx),
	}
	return exporter
}

func (e *ExternalDocumentExporter) GetExportModelInfo(ctx context.Context) (fileName string, moduleName string, err error) {
	nickname := addons.NewQuickNameTranslatorImpl(e.svcCtx.RedisAddons).TranslateUserNickname(ctx, utils.GetContextUserID(ctx))
	return "外部文件库-" + nickname + "-" + time.Now().Format("20060102150405") + ".xlsx", "文件管理-外部文件库", nil
}

func (e *ExternalDocumentExporter) GetData(ctx context.Context, req any) ([][]string, error) {
	jsonReq, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	var r types.GetExternalDocumentsReq
	err = json.Unmarshal(jsonReq, &r)
	if err != nil {
		return nil, err
	}
	beAttachedFile := ""
	if r.BeAttachedFile == 1 {
		beAttachedFile = "1"
	} else if r.BeAttachedFile == 2 {
		beAttachedFile = "2"
	}
	orgID := utils.GetContextOrganizationID(ctx)
	page, err := docvault.NewExternalDocumentLibraryClient(e.svcCtx.DocvaultRpcConn).Page(ctx, &docvault.ExternalDocumentPageReq{
		PageInfo: &docvault.PageInfo{
			NoPage: true,
		},
		Number:                         r.Number,
		Name:                           r.Name,
		OriginalNumber:                 r.OriginalNumber,
		OriginalDocNumber:              r.OriginalDocNumber,
		PublishDocNumber:               r.PublishDocNumber,
		PublishDepartment:              r.PublishDepartment,
		TypeDictionaryNodeIds:          r.TypeDictionaryNodeIds,
		DomainDictionaryNodeId:         r.DomainDictionaryNodeId,
		AuthenticationDictionaryNodeId: r.AuthenticationDictionaryNodeId,
		BeAttachedFile:                 beAttachedFile,
		Status:                         int32(r.Status),
		OrgType:                        int32(r.OrgType),
		OrgId:                          orgID,
	})
	if err != nil {
		return nil, err
	}
	if len(page.Data) == 0 {
		return [][]string{}, nil
	}
	data := make([][]string, len(page.Data))
	for i, v := range page.Data {
		data[i] = []string{
			v.Number,
			v.Version,
			v.OriginalNumber,
			v.OriginalVersion,
			v.Name,
			v.DocType,
			v.Domain,
			v.OriginalDocNumber,
			v.PublishDocNumber,
			v.PublishDepartment,
			"",
			"",
			time.UnixMilli(v.PublishDate).Format(time.DateOnly),
			time.UnixMilli(v.EffectiveDate).Format(time.DateOnly),
			v.Authentication,
			getStatus(v.Status),
		}
	}
	return data, nil
}

func getStatus(status int32) string {
	switch status {
	case 1:
		return "即将作废"
	case 2:
		return "即将实施"
	case 3:
		return "有效"
	case 4:
		return "拟修订"
	}
	return ""
}

func (e *ExternalDocumentExporter) GetTpl(ctx context.Context) (tplPath string, err error) {
	return "data/template/external_document_export.xlsx", nil
}
