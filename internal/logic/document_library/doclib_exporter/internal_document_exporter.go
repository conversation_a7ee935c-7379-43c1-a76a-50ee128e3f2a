package doclib_exporter

import (
	"context"
	"encoding/json"
	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
	"time"
)

// InternalDocumentExporter 采用模板方法模式实现内部文件列表导出能力
// 只重写需要自定义的钩子方法，其余复用通用导出流程

type InternalDocumentExporter struct {
	req    interface{}
	svcCtx *svc.ServiceContext
	ExporterAbility
}

func NewInternalDocumentExporter(svcCtx *svc.ServiceContext) ExporterAbility {
	exporter := &InternalDocumentExporter{
		svcCtx:          svcCtx,
		ExporterAbility: NewExporterAbility(svcCtx),
	}
	return exporter
}

// 钩子方法：获取导出模块信息
func (e *InternalDocumentExporter) GetExportModelInfo(ctx context.Context) (fileName string, moduleName string, err error) {
	nickname := addons.NewQuickNameTranslatorImpl(e.svcCtx.RedisAddons).TranslateUserNickname(ctx, utils.GetContextUserID(ctx))
	return "内部文件库-" + nickname + "-" + time.Now().Format("20060102150405") + ".xlsx", "文件管理-内部文件库", nil
}

// 钩子方法：获取数据
func (e *InternalDocumentExporter) GetData(ctx context.Context, req any) ([][]string, error) {
	// 格式化req
	jsonReq, err := json.Marshal(e.req)
	if err != nil {
		return nil, err
	}
	var r types.GetInternalDocumentsReq
	err = json.Unmarshal(jsonReq, &r)
	if err != nil {
		return nil, err
	}
	rs, err := docvault.NewInternalDocumentLibraryClient(e.svcCtx.DocvaultRpcConn).Page(
		ctx,
		&docvault.InternalDocumentPageReq{
			PageInfo: &docvault.PageInfo{
				NoPage: true,
			},
			DocCategoryIds: r.DocCategoryIDs,
			DepartmentIds:  r.DepartmentIDs,
			Status:         int32(r.Status),
			HasAttachment:  int32(r.HasAttachment),
			No:             r.No,
			OriginalNo:     r.OriginalNo,
			Name:           r.Name,
		},
	)
	if err != nil {
		return nil, err
	}

	if len(rs.Data) == 0 {
		return [][]string{}, nil
	}

	docCategoryIDs := utils.ExtractSliceField(rs.Data, func(v *docvault.InternalDocumentPageItem) string {
		return v.DocCategoryId
	})
	docCategoryMap := make(map[string]string)

	if len(docCategoryIDs) > 0 {
		// 查询分类名称
		docCategories, err := mapper.NewBusinessDictionaryNodeClient(e.svcCtx.NebulaDB).GetBusinessDictionaryNodeByIDs(ctx, docCategoryIDs)
		if err != nil {
			return nil, err
		}
		docCategoryMap = utils.ExtractSliceFieldToMap(docCategories, func(v mapper.BusinessDictionaryNode) (string, string) {
			return v.ID, v.Name
		})
	}

	data := make([][]string, len(rs.Data))
	for i, v := range rs.Data {
		data[i] = []string{
			v.No,
			v.VersionNo,
			v.OriginalNo,
			v.OriginalVersionNo,
			v.Name,
			docCategoryMap[v.DocCategoryId],
			e.svcCtx.QuickNameTranslator.TranslateOrganizationName(ctx, v.DepartmentId),
			e.svcCtx.QuickNameTranslator.TranslateUserNickname(ctx, v.AuthorId),
			"",
			"",
			time.UnixMilli(v.PublishDate).Format(time.DateOnly),
			time.UnixMilli(v.EffectiveDate).Format(time.DateOnly),
			e.getStatus(v.Status),
		}
	}

	return data, nil
}

func (*InternalDocumentExporter) getStatus(status int32) string {
	switch status {
	case 1:
		return "即将作废"
	case 2:
		return "即将实施"
	case 3:
		return "有效"
	case 4:
		return "拟修订"
	}
	return ""
}

// 钩子方法：获取模板路径
func (e *InternalDocumentExporter) GetTpl(ctx context.Context) (tplPath string, err error) {
	return "data/template/internal_document_export.xlsx", nil
}
