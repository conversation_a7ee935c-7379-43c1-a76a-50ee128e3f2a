package document_library

import (
	"context"
	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
	"testing"
	"time"

	"github.com/smartystreets/goconvey/convey"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/zrpc"
)

// UpdateTestData 更新测试数据结构
type UpdateTestData struct {
	BorrowRecords           []BorrowRecord
	BorrowDocumentRelations []BorrowDocumentRelation
	Users                   []mapper.User
	InternalDocuments       []mapper.InternalDocumentLibrary
	ExternalDocuments       []mapper.ExternalDocumentLibrary
}

// setupUpdateTestEnvironment 设置更新测试环境
// 功能：创建数据库连接和服务上下文
// 返回值：服务上下文、清理函数、错误信息
func setupUpdateTestEnvironment() (*svc.ServiceContext, func(), error) {
	// 1. 直接读取配置文件
	// 2. 初始化数据库连接
	// 3. 创建服务上下文
	// 4. 返回清理函数

	// 直接读取配置文件
	var testConfig config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &testConfig)

	// 初始化数据库连接
	docvaultDB := mapper.NewDocvaultDB(testConfig)
	phoenixDB := mapper.NewPhoenixDB(testConfig)
	nebulaDB := mapper.NewNebulaDB(testConfig)

	// 创建gRPC连接
	docvaultRpcConn := zrpc.MustNewClient(testConfig.DocvaultRPC)

	// 创建服务上下文
	svcCtx := &svc.ServiceContext{
		Config:          testConfig,
		DocvaultDB:      docvaultDB,
		PhoenixDB:       phoenixDB,
		NebulaDB:        nebulaDB,
		DocvaultRpcConn: docvaultRpcConn.Conn(),
	}

	// 清理函数
	cleanup := func() {
		// 清理测试数据
		cleanupUpdateTestData(svcCtx)
		// 关闭gRPC连接
		if docvaultRpcConn != nil {
			docvaultRpcConn.Conn().Close()
		}
	}

	return svcCtx, cleanup, nil
}

// cleanupUpdateTestData 清理更新测试数据
// 功能：删除测试过程中创建的数据
// 参数：svcCtx - 服务上下文
func cleanupUpdateTestData(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 删除借阅文档关系
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_update_relation_%").Delete(&BorrowDocumentRelation{})
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("borrow_record_id LIKE ?", "test_update_borrow_%").Delete(&BorrowDocumentRelation{})

	// 删除借阅记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_update_borrow_%").Delete(&BorrowRecord{})

	// 删除内部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_update_internal_doc_%").Delete(&mapper.InternalDocumentLibrary{})

	// 删除外部文档
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_update_external_doc_%").Delete(&mapper.ExternalDocumentLibrary{})

	// 彻底删除用户（包括软删除的记录）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Unscoped().Where("id LIKE ?", "test_update_user_%").Delete(&mapper.User{})
}

// createUpdateTestData 创建更新测试数据
// 功能：在数据库中创建测试所需的数据
// 参数：svcCtx - 服务上下文
// 返回值：测试数据结构、错误信息
func createUpdateTestData(svcCtx *svc.ServiceContext) (*UpdateTestData, error) {
	// 实现步骤：
	// 1. 创建测试用户
	// 2. 创建测试文档
	// 3. 创建测试借阅记录
	// 4. 创建借阅文档关系

	ctx := context.Background()
	now := time.Now()

	// 先清理可能存在的测试数据
	cleanupUpdateTestData(svcCtx)

	// 1. 创建测试用户
	users := []mapper.User{
		{
			ID:        "test_update_user_001",
			Username:  "test_update_user_001",
			Nickname:  "更新测试用户1",
			Status:    true,
			CreatedAt: &now,
			UpdatedAt: &now,
		},
	}

	// 插入用户数据
	for _, user := range users {
		if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Create(&user).Error; err != nil {
			return nil, err
		}
	}

	// 2. 创建测试内部文档
	internalDocs := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_update_internal_doc_001",
			No:             "UPD-INT-001",
			Name:           "更新测试内部文档1",
			VersionNo:      1,
			Status:         1,
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_update_org_001",
			FileID:         "test_update_file_001",
			DocCategoryID:  "test_update_category_001",
			DepartmentID:   "test_update_dept_001",
			AuthorID:       "test_update_user_001",
			CreatedBy:      "test_update_user_001",
			UpdatedBy:      "test_update_user_001",
		},
		{
			ID:             "test_update_internal_doc_002",
			No:             "UPD-INT-002",
			Name:           "更新测试内部文档2",
			VersionNo:      1,
			Status:         1,
			CreatedAt:      now,
			UpdatedAt:      now,
			OrganizationID: "test_update_org_001",
			FileID:         "test_update_file_002",
			DocCategoryID:  "test_update_category_001",
			DepartmentID:   "test_update_dept_001",
			AuthorID:       "test_update_user_001",
			CreatedBy:      "test_update_user_001",
			UpdatedBy:      "test_update_user_001",
		},
	}

	// 插入内部文档数据
	for _, doc := range internalDocs {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error; err != nil {
			return nil, err
		}
	}

	// 3. 创建测试借阅记录
	borrowRecords := []BorrowRecord{
		{
			ID:                "test_update_borrow_001",
			UserID:            "test_update_user_001",
			BorrowTime:        now.Add(-7 * 24 * time.Hour),
			DueTime:           now.Add(7 * 24 * time.Hour),
			BorrowReason:      "更新测试需要",
			ApprovalStatus:    3,
			ApprovalInfo:      "{}",
			ApprovalApplyTime: now.Add(-6 * 24 * time.Hour),
			CreatedAt:         now.Add(-7 * 24 * time.Hour),
			UpdatedAt:         now.Add(-6 * 24 * time.Hour),
			CreatedBy:         "test_update_user_001",
			UpdatedBy:         "test_update_user_001",
		},
	}

	// 插入借阅记录数据
	for _, record := range borrowRecords {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&record).Error; err != nil {
			return nil, err
		}
	}

	// 4. 创建借阅文档关系
	borrowDocRelations := []BorrowDocumentRelation{
		{
			ID:             "test_update_relation_001",
			BorrowRecordID: "test_update_borrow_001",
			DocumentID:     "test_update_internal_doc_001",
			VersionNo:      "V1.0",
			ModuleType:     2,
			BorrowStatus:   1,
			CreatedAt:      now.Add(-7 * 24 * time.Hour),
			UpdatedAt:      now.Add(-7 * 24 * time.Hour),
		},
	}

	// 插入借阅文档关系数据
	for _, relation := range borrowDocRelations {
		if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&relation).Error; err != nil {
			return nil, err
		}
	}

	return &UpdateTestData{
		BorrowRecords:           borrowRecords,
		BorrowDocumentRelations: borrowDocRelations,
		Users:                   users,
		InternalDocuments:       internalDocs,
		ExternalDocuments:       []mapper.ExternalDocumentLibrary{},
	}, nil
}

// TestUpdateLoan 测试更新借阅记录功能
func TestUpdateLoan(t *testing.T) {
	convey.Convey("测试更新借阅记录功能", t, func() {
		convey.Convey("测试正常更新借阅记录", func() {
			// 设置测试环境
			svcCtx, cleanup, err := setupUpdateTestEnvironment()
			convey.So(err, convey.ShouldBeNil)
			defer cleanup()

			// 创建测试数据
			testData, err := createUpdateTestData(svcCtx)
			convey.So(err, convey.ShouldBeNil)
			convey.So(testData, convey.ShouldNotBeNil)

			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), utils.UserIdKey, "test_update_user_001")
			ctx = context.WithValue(ctx, "organizationId", "test_update_org_001")

			// 创建logic实例
			logic := NewUpdateLoanLogic(ctx, svcCtx)

			// 准备更新请求
			req := &types.UpdateLoanReq{
				Id:           "test_update_borrow_001",
				BorrowTime:   time.Now().Add(-5 * 24 * time.Hour).Unix(),
				DueTime:      time.Now().Add(10 * 24 * time.Hour).Unix(),
				BorrowReason: "更新后的借阅原因",
				Documents: []types.LoanDocument{
					{
						DocumentId:         "test_update_internal_doc_001",
						DocumentVersionNo:  "V1.0",
						DocumentModuleType: 2,
						BorrowStatus:       1,
					},
				},
			}

			// 执行更新操作
			resp, err := logic.UpdateLoan(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试空文档列表更新", func() {
			// 设置测试环境
			svcCtx, cleanup, err := setupUpdateTestEnvironment()
			convey.So(err, convey.ShouldBeNil)
			defer cleanup()

			// 创建测试数据
			testData, err := createUpdateTestData(svcCtx)
			convey.So(err, convey.ShouldBeNil)
			convey.So(testData, convey.ShouldNotBeNil)

			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), utils.UserIdKey, "test_update_user_001")

			// 创建logic实例
			logic := NewUpdateLoanLogic(ctx, svcCtx)

			// 准备更新请求（空文档列表）
			req := &types.UpdateLoanReq{
				Id:           "test_update_borrow_001",
				BorrowTime:   time.Now().Add(-5 * 24 * time.Hour).Unix(),
				DueTime:      time.Now().Add(10 * 24 * time.Hour).Unix(),
				BorrowReason: "更新后的借阅原因",
				Documents:    []types.LoanDocument{}, // 空文档列表
			}

			// 执行更新操作
			resp, err := logic.UpdateLoan(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})

		convey.Convey("测试多文档更新", func() {
			// 设置测试环境
			svcCtx, cleanup, err := setupUpdateTestEnvironment()
			convey.So(err, convey.ShouldBeNil)
			defer cleanup()

			// 创建测试数据
			testData, err := createUpdateTestData(svcCtx)
			convey.So(err, convey.ShouldBeNil)
			convey.So(testData, convey.ShouldNotBeNil)

			// 创建带有用户ID的上下文
			ctx := context.WithValue(context.Background(), utils.UserIdKey, "test_update_user_001")

			// 创建logic实例
			logic := NewUpdateLoanLogic(ctx, svcCtx)

			// 准备更新请求（多个文档）
			req := &types.UpdateLoanReq{
				Id:           "test_update_borrow_001",
				BorrowTime:   time.Now().Add(-5 * 24 * time.Hour).Unix(),
				DueTime:      time.Now().Add(10 * 24 * time.Hour).Unix(),
				BorrowReason: "更新后的借阅原因",
				Documents: []types.LoanDocument{
					{
						DocumentId:         "test_update_internal_doc_001",
						DocumentVersionNo:  "V1.0",
						DocumentModuleType: 2,
						BorrowStatus:       1,
					},
					{
						DocumentId:         "test_update_internal_doc_002",
						DocumentVersionNo:  "V1.0",
						DocumentModuleType: 2,
						BorrowStatus:       1,
					},
				},
			}

			// 执行更新操作
			resp, err := logic.UpdateLoan(req)
			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldNotBeNil)
		})
	})
}
