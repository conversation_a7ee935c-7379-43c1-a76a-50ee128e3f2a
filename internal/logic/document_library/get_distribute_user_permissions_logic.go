package document_library

import (
	"context"
	"fmt"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDistributeUserPermissionsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDistributeUserPermissionsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDistributeUserPermissionsLogic {
	return &GetDistributeUserPermissionsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// GetDistributeUserPermissions 根据文档ID查询发放回收用户记录
// 功能: 查询指定文档的发放权限记录，包括用户权限详情和状态信息
// 参数:
//   - req: 查询请求，包含文档ID
//
// 返回值:
//   - resp: 用户权限记录列表响应
//   - err: 错误信息，如果查询失败则返回相应错误
//
// 业务逻辑:
//  1. 根据文档ID查询发放记录文件
//  2. 根据文件记录ID查询权限记录
//  3. 组装用户权限数据并返回
func (l *GetDistributeUserPermissionsLogic) GetDistributeUserPermissions(req *types.GetDistributeUserPermissionsReq) (resp *types.GetDistributeUserPermissionsResp, err error) {
	// 参数验证
	if req.DocumentId == "" {
		l.Logger.Error("文档ID不能为空")
		return nil, fmt.Errorf("文档ID不能为空")
	}

	// 创建数据库客户端
	distributeFileClient := mapper.NewDistributeRecordFileClient(l.svcCtx.DocvaultDB)
	distributePermissionClient := mapper.NewDistributeRecordPermissionClient(l.svcCtx.DocvaultDB)

	// 根据文档ID查询发放记录文件
	distributeFiles, err := l.queryDistributeFilesByDocumentId(distributeFileClient, req.DocumentId)
	if err != nil {
		l.Logger.Errorf("查询发放记录文件失败: %v", err)
		return nil, err
	}

	// 如果没有找到发放记录，返回空列表
	if len(distributeFiles) == 0 {
		l.Logger.Infof("未找到文档ID为 %s 的发放记录", req.DocumentId)
		return &types.GetDistributeUserPermissionsResp{
			List: []types.DistributeUserPermission{},
		}, nil
	}

	// 查询权限记录
	userPermissions, err := l.queryUserPermissionsByFileRecords(distributePermissionClient, distributeFiles, req.UserID, req.Status)
	if err != nil {
		l.Logger.Errorf("查询用户权限记录失败: %v", err)
		return nil, err
	}

	return &types.GetDistributeUserPermissionsResp{
		List: userPermissions,
	}, nil
}

// queryDistributeFilesByDocumentId 根据文档ID查询发放记录文件
// 功能: 查询指定文档ID对应的所有发放记录文件
// 参数:
//   - client: 发放记录文件数据访问客户端
//   - documentId: 文档ID
//
// 返回值:
//   - files: 发放记录文件列表
//   - err: 错误信息
func (l *GetDistributeUserPermissionsLogic) queryDistributeFilesByDocumentId(client *mapper.DistributeRecordFileClient, documentId string) ([]mapper.DistributeRecordFile, error) {
	// 使用客户端方法查询数据库
	files, err := client.FindByFileID(documentId)
	if err != nil {
		l.Logger.Errorf("查询发放记录文件失败: %v", err)
		return nil, fmt.Errorf("查询发放记录文件失败: %w", err)
	}

	l.Logger.Infof("查询到 %d 条发放记录文件，文档ID: %s", len(files), documentId)
	return files, nil
}

// queryUserPermissionsByFileRecords 根据文件记录查询用户权限
// 功能: 根据发放记录文件列表查询对应的用户权限记录，支持按用户ID和状态过滤
// 参数:
//   - client: 发放记录权限数据访问客户端
//   - files: 发放记录文件列表
//   - userID: 用户ID过滤条件（可选，空字符串表示不过滤）
//   - status: 状态过滤条件（可选，0表示不过滤）
//
// 返回值:
//   - userPermissions: 用户权限记录列表
//   - err: 错误信息
func (l *GetDistributeUserPermissionsLogic) queryUserPermissionsByFileRecords(client *mapper.DistributeRecordPermissionClient, files []mapper.DistributeRecordFile, userID string, status int) ([]types.DistributeUserPermission, error) {
	if len(files) == 0 {
		return []types.DistributeUserPermission{}, nil
	}

	// 提取文件记录ID列表
	var fileRecordIDs []string
	fileRecordMap := make(map[string]mapper.DistributeRecordFile)
	for _, file := range files {
		fileRecordIDs = append(fileRecordIDs, file.ID)
		fileRecordMap[file.ID] = file
	}

	// 查询权限记录（包含发放记录状态）
	permissions, err := client.FindByFileRecordIDsWithStatus(fileRecordIDs)
	if err != nil {
		l.Logger.Errorf("查询权限记录失败: %v", err)
		return nil, fmt.Errorf("查询权限记录失败: %w", err)
	}

	// 转换为响应格式并应用过滤条件
	userPermissions := make([]types.DistributeUserPermission, 0, len(permissions))
	for _, permission := range permissions {

		apiStatus, shouldInclude := l.convertDisposeStatusToAPIStatus(permission.DisposeStatus, permission.DistributeRecordStatus)
		if !shouldInclude {
			continue // 过滤掉不符合条件的记录
		}

		// 应用用户ID过滤
		if userID != "" && permission.UserID != userID {
			continue
		}

		// 应用状态过滤
		if status != 0 && apiStatus != status {
			continue
		}

		userPermission := types.DistributeUserPermission{
			InventoryId:    permission.FileRecordID,
			FileForm:       permission.FileForm,
			FilePermission: permission.FilePermission,
			UserId:         permission.UserID,
			UserNickName:   permission.UserName,
			Status:         apiStatus,
		}
		userPermissions = append(userPermissions, userPermission)
	}

	return userPermissions, nil
}

// convertDisposeStatusToAPIStatus 将数据库中的处置状态转换为API状态
// 功能: 根据DisposeStatus和发放记录状态确定API状态，并判断是否应该包含在结果中
// 参数:
//   - disposeStatus: 数据库中的处置状态  1未回收 | 2回收审批中 | 3已回收 | 4处置审批中 ｜5已处置
//   - distributeRecordStatus: 发放记录状态	1待提交 | 2待审批 | 3已审批 | 4已拒绝
//
// 返回值:
//   - apiStatus: API 状态
//   - shouldInclude: 是否应该包含在结果中
//
// 状态映射规则:
//   - 1发放审批中 = DisposeStatus等于1且发放记录状态为2(待审批)
//   - 2已发放 = DisposeStatus等于1且发放记录状态为3(已审批)
//   - 3回收审批中 = DisposeStatus等于2
//   - 4已回收 = DisposeStatus等于3
//   - 5处置审批中 = DisposeStatus等于4
//   - 其他状态全部过滤掉
func (l *GetDistributeUserPermissionsLogic) convertDisposeStatusToAPIStatus(disposeStatus int32, distributeRecordStatus int32) (int, bool) {
	switch disposeStatus {
	case 1: // 未回收
		if distributeRecordStatus == 2 { // 待审批
			return 1, true // 发放审批中
		} else if distributeRecordStatus == 3 { // 已审批
			return 2, true // 已发放
		}
		return 0, false // 其他发放状态过滤掉
	case 2: // 回收审批中
		return 3, true // 回收审批中
	case 3: // 已回收
		return 4, true // 已回收
	case 4: // 处置审批中
		return 5, true // 处置审批中
	default: // 其他状态全部过滤掉
		return 0, false
	}
}
