package mapper

import (
	"gorm.io/gorm"
)

const (
	TableNameRecycleRecordPermission = "recycle_record_permissions"
)

// RecycleRecordPermission 对应 recycle_record_permissions 表
type RecycleRecordPermission struct {
	ID             string `gorm:"type:varchar(64);primary_key"`
	FileRecordID   string `gorm:"type:varchar(64);index;comment:'关联RecycleRecordFile的ID'"`
	UserID         string `gorm:"type:varchar(64);comment:'接收人ID'"`
	FileForm       int32  `gorm:"comment:'文件形式,1电子文件 | 2纸质文件'"`
	FilePermission int32  `gorm:"comment:'文件权限,1查阅 | 2查阅/下载 | 3一次下载'"`
}

func (RecycleRecordPermission) TableName() string {
	return TableNameRecycleRecordPermission
}

// RecycleRecordPermissionClient 是 recycle_record_permissions 表的数据访问客户端
type RecycleRecordPermissionClient struct {
	db *gorm.DB
}

// NewRecycleRecordPermissionClient 创建一个新的 RecycleRecordPermissionClient 实例
func NewRecycleRecordPermissionClient(db *DocvaultDB) *RecycleRecordPermissionClient {
	return &RecycleRecordPermissionClient{
		db: db.GetDB(),
	}
}
