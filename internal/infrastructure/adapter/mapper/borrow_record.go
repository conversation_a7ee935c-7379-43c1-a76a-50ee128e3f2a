package mapper

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// BorrowRecordClient 借阅记录查询客户端
type BorrowRecordClient struct {
	DB *DocvaultDB
}

// NewBorrowRecordClient 创建借阅记录查询客户端实例
// 参数:
//   - db: 数据库连接实例
//
// 返回值:
//   - *BorrowRecordClient: 借阅记录查询客户端实例
func NewBorrowRecordClient(db *DocvaultDB) *BorrowRecordClient {
	return &BorrowRecordClient{
		DB: db,
	}
}

// BorrowRecordView 借阅记录与文档关系的左连接视图
type BorrowRecordView struct {
	// 借阅记录字段
	ID                string    `gorm:"column:id" json:"id"`                                   // 借阅记录ID
	UserID            string    `gorm:"column:user_id" json:"user_id"`                         // 借阅用户ID
	BorrowTime        time.Time `gorm:"column:borrow_time" json:"borrow_time"`                 // 借阅时间
	DueTime           time.Time `gorm:"column:due_time" json:"due_time"`                       // 应还时间
	BorrowReason      string    `gorm:"column:borrow_reason" json:"borrow_reason"`             // 借阅原因
	BorrowApplyTime   time.Time `gorm:"column:borrow_apply_time" json:"borrow_apply_time"`     // 申请时间
	ApprovalStatus    int       `gorm:"column:approval_status" json:"approval_status"`         // 审批状态
	ApprovalInfo      string    `gorm:"column:approval_info" json:"approval_info"`             // 审批信息
	ApprovalApplyTime time.Time `gorm:"column:approval_apply_time" json:"approval_apply_time"` // 审批申请时间
	RecordCreatedAt   time.Time `gorm:"column:created_at" json:"record_created_at"`            // 借阅记录创建时间
	RecordUpdatedAt   time.Time `gorm:"column:updated_at" json:"record_updated_at"`            // 借阅记录更新时间
	RecordCreatedBy   string    `gorm:"column:created_by" json:"record_created_by"`            // 借阅记录创建人
	RecordUpdatedBy   string    `gorm:"column:updated_by" json:"record_updated_by"`            // 借阅记录更新人

	// 文档关系字段（可能为空）
	DocumentRelationID string     `gorm:"column:doc_id" json:"document_relation_id"`     // 文档关系ID
	DocumentID         *string    `gorm:"column:document_id" json:"document_id"`         // 文档ID
	VersionNo          *string    `gorm:"column:version_no" json:"version_no"`           // 文档版本号
	ModuleType         *int       `gorm:"column:module_type" json:"module_type"`         // 文档所属模块
	BorrowStatus       *int       `gorm:"column:borrow_status" json:"borrow_status"`     // 文档借阅状态
	RecoverUserID      string     `gorm:"column:recover_user_id" json:"recover_user_id"` // 回收用户ID
	RecoverTime        *time.Time `gorm:"column:recover_time" json:"recover_time"`       // 回收时间
	DocCreatedAt       *time.Time `gorm:"column:doc_created_at" json:"doc_created_at"`   // 文档关系创建时间
	DocUpdatedAt       *time.Time `gorm:"column:doc_updated_at" json:"doc_updated_at"`   // 文档关系更新时间
	DocCreatedBy       *string    `gorm:"column:doc_created_by" json:"doc_created_by"`   // 文档关系创建人
	DocUpdatedBy       *string    `gorm:"column:doc_updated_by" json:"doc_updated_by"`   // 文档关系更新人
}

// PageBorrowRecordReq 分页查询借阅记录请求参数
type PageBorrowRecordReq struct {
	UserID             string   `json:"user_id"`              // 用户ID
	Page               int      `json:"page"`                 // 页码
	PageSize           int      `json:"page_size"`            // 每页大小
	NoPage             bool     `json:"no_page"`              // 是否不分页
	ModuleType         *int     `json:"module_type"`          // 文档模块类型
	BorrowStatus       *int     `json:"borrow_status"`        // 借阅状态
	ApprovalStatus     *int     `json:"approval_status"`      // 审批状态
	Search             string   `json:"search"`               // 搜索关键词
	DocumentIDs        []string `json:"document_ids"`         // 文档ID列表
	DocumentCategoryId string   `json:"document_category_id"` // 文档类别ID
}

// GetBorrowRecordStatistics 查询借阅记录视图
// 功能: 通过左连接查询获取借阅记录及其关联的文档关系信息
// 参数:
//   - ctx: 上下文
//   - req: 分页查询请求参数
//
// 返回值:
//   - []BorrowRecordView: 借阅记录视图列表
//   - int64: 总记录数
//   - error: 错误信息
func (c *BorrowRecordClient) GetBorrowRecordStatistics(ctx context.Context, req PageBorrowRecordReq) ([]BorrowRecordView, int64, error) {
	// 1. 构建基础查询
	// 2. 应用过滤条件scope
	// 3. 执行分页查询
	// 4. 返回结果

	var list []BorrowRecordView
	var total int64

	// 构建基础查询，使用左连接
	db := c.DB.db.WithContext(ctx).Scopes(c.WithBorrowRecordViewQuery())

	// 应用过滤条件scope
	db = db.Scopes(
		c.WithUserIDFilter(req.UserID),
		c.WithModuleTypeFilter(req.ModuleType),
		c.WithBorrowStatusFilter(req.BorrowStatus),
		c.WithApprovalStatusFilter(req.ApprovalStatus),
		c.WithSearchFilter(req.Search),
		c.WithDocumentIDsFilter(req.DocumentIDs),
		c.WithDocumentCategoryFilter(req.DocumentCategoryId),
	)

	// 计算总数 - 使用DISTINCT避免LEFT JOIN导致的重复计数问题
	countDB := c.DB.db.WithContext(ctx).Scopes(c.WithBorrowRecordCountQuery()).
		Scopes(
			c.WithUserIDFilter(req.UserID),
			c.WithModuleTypeFilter(req.ModuleType),
			c.WithBorrowStatusFilter(req.BorrowStatus),
			c.WithApprovalStatusFilter(req.ApprovalStatus),
			c.WithSearchFilter(req.Search),
			c.WithDocumentIDsFilter(req.DocumentIDs),
			c.WithDocumentCategoryFilter(req.DocumentCategoryId),
		)
	if err := countDB.Select("COUNT(DISTINCT br.id)").Row().Scan(&total); err != nil {
		return nil, 0, err
	}

	// 应用分组去重、排序和分页scope
	db = db.Scopes(
		c.WithGroupBy(),
		c.WithOrderBy(),
		c.WithPagination(req.Page, req.PageSize, req.NoPage),
	)

	// 执行查询
	if err := db.Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// WithUserIDFilter 用户ID过滤scope
// 功能: 根据用户ID过滤借阅记录
// 参数:
//   - userID: 用户ID
//
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithUserIDFilter(userID string) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if userID != "" {
			return db.Where("br.user_id = ?", userID)
		}
		return db
	}
}

// WithModuleTypeFilter 文档模块类型过滤scope
// 功能: 根据文档模块类型过滤
// 参数:
//   - moduleType: 文档模块类型指针
//
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithModuleTypeFilter(moduleType *int) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if moduleType != nil {
			return db.Where("bdr.module_type = ?", *moduleType)
		}
		return db
	}
}

// WithBorrowStatusFilter 借阅状态过滤scope
// 功能: 根据借阅状态过滤
// 参数:
//   - borrowStatus: 借阅状态指针
//
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithBorrowStatusFilter(borrowStatus *int) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if borrowStatus != nil {
			return db.Where("bdr.borrow_status = ?", *borrowStatus)
		}
		return db
	}
}

// WithApprovalStatusFilter 审批状态过滤scope
// 功能: 根据审批状态过滤
// 参数:
//   - approvalStatus: 审批状态指针
//
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithApprovalStatusFilter(approvalStatus *int) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if approvalStatus != nil {
			return db.Where("br.approval_status = ?", *approvalStatus)
		}
		return db
	}
}

// WithSearchFilter 搜索关键词过滤scope
// 功能: 根据搜索关键词过滤借阅原因
// 参数:
//   - search: 搜索关键词
//
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithSearchFilter(search string) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if search != "" {
			return db.Where("br.borrow_reason LIKE ?", "%"+search+"%")
		}
		return db
	}
}

// WithDocumentIDsFilter 文档ID列表过滤scope
// 功能: 根据文档ID列表过滤
// 参数:
//   - documentIDs: 文档ID列表
//
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithDocumentIDsFilter(documentIDs []string) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(documentIDs) > 0 {
			return db.Where("bdr.document_id IN ?", documentIDs)
		}
		return db
	}
}

// WithDocumentCategoryFilter 文档类别过滤scope
// 功能: 根据文档类别ID过滤（需要通过文档表关联查询）
// 参数:
//   - categoryId: 文档类别ID
//
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithDocumentCategoryFilter(categoryId string) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if categoryId != "" {
			// 这里需要根据实际的文档表结构来实现
			// 暂时跳过，因为需要了解具体的文档表结构和关联关系
		}
		return db
	}
}

// WithGroupBy 分组scope
// 功能: 按借阅记录ID分组，避免LEFT JOIN产生重复记录
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithGroupBy() func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Group("br.id")
	}
}

// WithOrderBy 排序scope
// 功能: 按创建时间倒序排列
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithOrderBy() func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Order("br.created_at DESC")
	}
}

// WithBorrowRecordCountQuery 借阅记录计数查询scope
// 功能: 构建借阅记录与文档关系的左连接查询（仅用于计数）
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithBorrowRecordCountQuery() func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.
			Table("borrow_records br").
			Joins("LEFT JOIN borrow_document_relations bdr ON br.id = bdr.borrow_record_id")
	}
}

// WithBorrowRecordViewQuery 借阅记录视图查询scope
// 功能: 构建借阅记录与文档关系的左连接查询
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithBorrowRecordViewQuery() func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.
			Table("borrow_records br").
			Select(`
				br.id,
				br.user_id,
				br.borrow_time,
				br.due_time,
				br.borrow_reason,
				br.borrow_apply_time,
				br.approval_status,
				br.approval_info,
				br.approval_apply_time,
				br.created_at,
				br.updated_at,
				br.created_by,
				br.updated_by,
				bdr.id as doc_id,
				bdr.document_id,
				bdr.version_no,
				bdr.module_type,
				bdr.borrow_status,
				bdr.recover_user_id,
				bdr.recover_time,
				bdr.created_at as doc_created_at,
				bdr.updated_at as doc_updated_at,
				bdr.created_by as doc_created_by,
				bdr.updated_by as doc_updated_by
			`).
			Joins("LEFT JOIN borrow_document_relations bdr ON br.id = bdr.borrow_record_id")
	}
}

// WithPagination 分页scope
// 功能: 应用分页条件
// 参数:
//   - page: 页码
//   - pageSize: 每页大小
//   - noPage: 是否不分页
//
// 返回值:
//   - func(*gorm.DB) *gorm.DB: GORM scope函数
func (c *BorrowRecordClient) WithPagination(page, pageSize int, noPage bool) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if !noPage && page > 0 && pageSize > 0 {
			return db.Offset((page - 1) * pageSize).Limit(pageSize)
		}
		return db
	}
}

// BorrowRecordDocumentCount 借阅记录文档数量统计
type BorrowRecordDocumentCount struct {
	BorrowRecordID        string `gorm:"column:borrow_record_id" json:"borrow_record_id"`               // 借阅记录ID
	BorrowDocumentsCount  int    `gorm:"column:borrow_documents_count" json:"borrow_documents_count"`   // 借阅文档总数
	RecoverDocumentsCount int    `gorm:"column:recover_documents_count" json:"recover_documents_count"` // 已回收文档数量
}

// BorrowDocumentRelation 借阅文档关系结构体
type BorrowDocumentRelation struct {
	ID             string `gorm:"column:id" json:"id"`                             // 关系ID
	BorrowRecordID string `gorm:"column:borrow_record_id" json:"borrow_record_id"` // 借阅记录ID
	DocumentID     string `gorm:"column:document_id" json:"document_id"`           // 文档ID
	VersionNo      string `gorm:"column:version_no" json:"version_no"`             // 文档版本号
	ModuleType     int    `gorm:"column:module_type" json:"module_type"`           // 文档模块类型
	BorrowStatus   int    `gorm:"column:borrow_status" json:"borrow_status"`       // 借阅状态
}

// GetDocumentsByBorrowRecordID 根据借阅记录ID获取文档列表
// 功能: 查询指定借阅记录下的所有文档信息
// 参数:
//   - ctx: 上下文
//   - borrowRecordID: 借阅记录ID
//
// 返回值:
//   - []BorrowDocumentRelation: 借阅文档关系列表
//   - error: 错误信息
func (c *BorrowRecordClient) GetDocumentsByBorrowRecordID(ctx context.Context, borrowRecordID string) ([]BorrowDocumentRelation, error) {
	// 实现步骤：
	// 1. 验证输入参数
	// 2. 构建查询条件
	// 3. 执行查询获取文档关系列表
	// 4. 返回结果

	if borrowRecordID == "" {
		return nil, fmt.Errorf("借阅记录ID不能为空")
	}

	var relations []BorrowDocumentRelation

	// 查询指定借阅记录下的所有文档关系
	err := c.DB.db.WithContext(ctx).
		Table("borrow_document_relations").
		Select("id, borrow_record_id, document_id, version_no, module_type, borrow_status").
		Where("borrow_record_id = ?", borrowRecordID).
		Find(&relations).Error

	if err != nil {
		return nil, fmt.Errorf("查询借阅文档关系失败: %w", err)
	}

	return relations, nil
}

// BatchGetDocumentCounts 批量获取借阅记录的文档数量统计
// 功能: 根据借阅记录ID列表，统计每个记录的借阅文档总数和已回收文档数量
// 参数:
//   - ctx: 上下文
//   - borrowRecordIDs: 借阅记录ID列表
//
// 返回值:
//   - map[string]*BorrowRecordDocumentCount: 借阅记录ID到文档数量统计的映射
//   - error: 错误信息
func (c *BorrowRecordClient) BatchGetDocumentCounts(ctx context.Context, borrowRecordIDs []string) (map[string]*BorrowRecordDocumentCount, error) {
	// 实现步骤：
	// 1. 构建查询SQL，统计每个借阅记录的文档数量
	// 2. 执行查询获取统计结果
	// 3. 将结果转换为map返回

	if len(borrowRecordIDs) == 0 {
		return make(map[string]*BorrowRecordDocumentCount), nil
	}

	var counts []BorrowRecordDocumentCount

	// 构建统计查询SQL
	sql := `
		SELECT 
			bdr.borrow_record_id,
			COUNT(bdr.id) as borrow_documents_count,
			COUNT(CASE WHEN bdr.borrow_status = 3 THEN 1 END) as recover_documents_count
		FROM borrow_document_relations bdr
		WHERE bdr.borrow_record_id IN (?)
		GROUP BY bdr.borrow_record_id
	`

	// 执行查询
	if err := c.DB.db.WithContext(ctx).Raw(sql, borrowRecordIDs).Scan(&counts).Error; err != nil {
		return nil, err
	}

	// 转换为map
	result := make(map[string]*BorrowRecordDocumentCount)
	for i := range counts {
		result[counts[i].BorrowRecordID] = &counts[i]
	}

	return result, nil
}
