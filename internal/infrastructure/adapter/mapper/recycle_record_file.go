package mapper

import (
	"gorm.io/gorm"
)

const (
	TableNameRecycleRecordFile = "recycle_record_files"
)

// RecycleRecordFile 对应 recycle_record_files 表
type RecycleRecordFile struct {
	ID                     string `gorm:"type:varchar(64);primary_key"`
	RecordID               string `gorm:"type:varchar(64);index;comment:'关联RecycleRecord的ID'"`
	DistributeRecordFileID string `gorm:"type:varchar(64);comment:'清单文件id'"`
}

func (RecycleRecordFile) TableName() string {
	return TableNameRecycleRecordFile
}

// RecycleRecordFileClient 是 recycle_record_files 表的数据访问客户端
type RecycleRecordFileClient struct {
	db *gorm.DB
}

// NewRecycleRecordFileClient 创建一个新的 RecycleRecordFileClient 实例
func NewRecycleRecordFileClient(db *DocvaultDB) *RecycleRecordFileClient {
	return &RecycleRecordFileClient{
		db: db.GetDB(),
	}
}
