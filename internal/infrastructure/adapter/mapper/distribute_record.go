package mapper

import (
	"context"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameDistributeRecord = "distribute_records"
)

// DistributeRecord 对应 distribute_records 表
type DistributeRecord struct {
	ID                 string         `gorm:"type:varchar(64);primary_key"`
	WorkflowID         string         `gorm:"type:varchar(64);comment:'流程id'"`
	Applicant          string         `gorm:"type:varchar(64);comment:'申请人,用户id'"`
	ApplyDate          int64          `gorm:"comment:'申请日期'"`
	DistributeType     int32          `gorm:"comment:'发放类型，1内部发放 | 2外部发放'"`
	FileType           int32          `gorm:"comment:'文件类型，1内部文件 | 2外部文件'"`
	FileCategory       string         `gorm:"type:varchar(255);comment:'文件类别'"`
	TypeDictNodeId     string         `gorm:"type:varchar(64);comment:'类型字典节点id'"`
	Reason             string         `gorm:"type:text;comment:'原因'"`
	OtherReason        string         `gorm:"type:text;comment:'其他原因'"`
	WishDistributeDate int64          `gorm:"comment:'期望发放日期'"`
	DistributeCount    int            `gorm:"comment:'发放份数'"`
	SignForCount       int            `gorm:"comment:'签收份数'"`
	DisposeCount       int            `gorm:"comment:'处置份数'"`
	Status             int            `gorm:"comment:'状态,1待提交 | 2待审批 | 3已审批 | 4已驳回'"`
	ApprovalInfo       datatypes.JSON `gorm:"type:json;comment:'审批信息'"`
	CreatedAt          time.Time      `gorm:"column:created_at"` // 创建时间
	UpdatedAt          time.Time      `gorm:"column:updated_at"` // 更新时间
	CreatedBy          string         `gorm:"type:varchar(64);column:created_by"`
	UpdatedBy          string         `gorm:"type:varchar(64);column:updated_by"`
	OrganizationID     string         `gorm:"type:varchar(64);comment:'组织id'"`
	TenantID           string         `gorm:"type:varchar(64);comment:'租户id'"`
}

func (DistributeRecord) TableName() string {
	return TableNameDistributeRecord
}

// DistributeRecordClient 是 distribute_records 表的数据访问客户端
type DistributeRecordClient struct {
	db *gorm.DB
}

// NewDistributeRecordClient 创建一个新的 DistributeRecordClient 实例
func NewDistributeRecordClient(db *DocvaultDB) *DistributeRecordClient {
	return &DistributeRecordClient{
		db: db.GetDB(),
	}
}

// FindByID 根据ID查询发放记录
// 功能: 查询指定ID对应的发放记录
// 参数:
//   - id: 发放记录ID
//
// 返回值:
//   - record: 发放记录
//   - err: 错误信息
func (c *DistributeRecordClient) FindByID(ctx context.Context, id string) (DistributeRecord, error) {
	var record DistributeRecord
	err := c.db.Where("id = ?", id).First(&record).Error
	return record, err
}

func (c *DistributeRecordClient) FindByIDs(ctx context.Context, ids []string) ([]DistributeRecord, error) {
	var records []DistributeRecord
	err := c.db.Where("id in ?", ids).Find(&records).Error
	return records, err
}
