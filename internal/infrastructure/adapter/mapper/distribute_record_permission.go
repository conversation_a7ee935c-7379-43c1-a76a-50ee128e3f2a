package mapper

import "gorm.io/gorm"

const (
	TableNameDistributeRecordPermission = "distribute_record_permissions"
)

// DistributeRecordPermission 对应 distribute_record_permissions 表
type DistributeRecordPermission struct {
	ID             string `gorm:"type:varchar(64);primary_key"`
	FileRecordID   string `gorm:"type:varchar(64);index;comment:'关联DistributeRecordFile的ID'"`
	FileForm       int32  `gorm:"comment:'文件形式,1电子文件 | 2纸质文件'"`
	FilePermission int32  `gorm:"comment:'文件权限,1查阅 | 2查阅/下载 | 3一次下载'"`
	Recipient      string `gorm:"type:varchar(255);comment:'接收方'"`
	UserID         string `gorm:"type:varchar(64);comment:'接收人ID'"`
	UserName       string `gorm:"type:varchar(255);comment:'接收人姓名'"`
	SignForStatus  int32  `gorm:"comment:'签收状态,1未签收 | 2已签收'"`
	DisposeStatus  int32  `gorm:"comment:'处置状态,1未回收 | 2回收审批中 | 3已回收 | 4处置审批中 ｜5已处置'"`
	IsUsed         bool   `gorm:"comment:'是否已使用,使用后变为true'"`
}

func (DistributeRecordPermission) TableName() string {
	return TableNameDistributeRecordPermission
}

// DistributeRecordPermissionClient 是 distribute_record_permissions 表的数据访问客户端
type DistributeRecordPermissionClient struct {
	db *gorm.DB
}

// NewDistributeRecordPermissionClient 创建一个新的 DistributeRecordPermissionClient 实例
func NewDistributeRecordPermissionClient(db *DocvaultDB) *DistributeRecordPermissionClient {
	return &DistributeRecordPermissionClient{
		db: db.GetDB(),
	}
}

// DistributeRecordPermissionWithStatus 权限记录包含发放记录状态
type DistributeRecordPermissionWithStatus struct {
	DistributeRecordPermission
	DistributeRecordStatus int32 `gorm:"column:distribute_record_status"` // 发放记录状态
}

// FindByFileRecordIDs 根据文件记录ID列表查询权限记录
// 功能: 查询指定文件记录ID列表对应的所有权限记录
// 参数:
//   - fileRecordIDs: 文件记录ID列表
//
// 返回值:
//   - permissions: 权限记录列表
//   - err: 错误信息
func (c *DistributeRecordPermissionClient) FindByFileRecordIDs(fileRecordIDs []string) ([]DistributeRecordPermission, error) {
	var permissions []DistributeRecordPermission
	if len(fileRecordIDs) == 0 {
		return permissions, nil
	}
	err := c.db.Where("file_record_id IN ?", fileRecordIDs).Find(&permissions).Error
	return permissions, err
}

// FindByFileRecordIDsWithStatus 根据文件记录ID列表查询权限记录，同时获取发放记录状态
// 功能: 根据发放记录文件ID列表查询对应的用户权限记录，并关联查询发放记录状态
// 参数:
//   - fileRecordIDs: 文件记录ID列表
//
// 返回值:
//   - permissions: 权限记录列表（包含发放记录状态）
//   - err: 错误信息
func (c *DistributeRecordPermissionClient) FindByFileRecordIDsWithStatus(fileRecordIDs []string) ([]DistributeRecordPermissionWithStatus, error) {
	var permissions []DistributeRecordPermissionWithStatus
	if len(fileRecordIDs) == 0 {
		return permissions, nil
	}

	// 关联查询发放记录状态
	err := c.db.Table("distribute_record_permissions").
		Select("distribute_record_permissions.*, distribute_records.status as distribute_record_status").
		Joins("INNER JOIN distribute_record_files ON distribute_record_permissions.file_record_id = distribute_record_files.id").
		Joins("INNER JOIN distribute_records ON distribute_record_files.record_id = distribute_records.id").
		Where("distribute_record_permissions.file_record_id IN ?", fileRecordIDs).
		Find(&permissions).Error

	return permissions, err
}
