package mapper

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const (
	TableNameDisposalRecord = "disposal_records"
)

// DisposalRecord 对应 disposal_records 表
// 注意: 一条处置记录可以对应多条发放记录，通过 disposal_record_files 表关联
type DisposalRecord struct {
	ID           string         `gorm:"type:varchar(64);primary_key"`
	DisposalBy   string         `gorm:"type:varchar(64);comment:'处置人,用户id'"`
	DisposalDate time.Time      `gorm:"comment:'处置日期'"`
	Reason       string         `gorm:"type:text;comment:'处置方式'"`
	WorkflowID   string         `gorm:"type:varchar(64);comment:'流程ID'"`
	ApprovalInfo datatypes.JSON `gorm:"type:json;column:approval_info;comment:'审批信息'"`
	CreatedAt    time.Time      `gorm:"column:created_at"` // 创建时间
	UpdatedAt    time.Time      `gorm:"column:updated_at"` // 更新时间
	CreatedBy    string         `gorm:"type:varchar(64);column:created_by"`
	UpdatedBy    string         `gorm:"type:varchar(64);column:updated_by"`
}

func (DisposalRecord) TableName() string {
	return TableNameDisposalRecord
}

// DisposalRecordClient 是 disposal_records 表的数据访问客户端
type DisposalRecordClient struct {
	db *gorm.DB
}

// NewDisposalRecordClient 创建一个新的 DisposalRecordClient 实例
func NewDisposalRecordClient(db *DocvaultDB) *DisposalRecordClient {
	return &DisposalRecordClient{
		db: db.GetDB(),
	}
}
