package mapper

const (
	TableNameDisposalRecordFile = "disposal_record_files"
)

// DisposalRecordFile 对应 disposal_record_files 表
type DisposalRecordFile struct {
	ID                     string `gorm:"type:varchar(64);primary_key"`
	RecordID               string `gorm:"type:varchar(64);index;comment:'关联DisposalRecord的ID'"`
	DistributeRecordFileID string `gorm:"type:varchar(64);comment:'清单文件id'"`
}

func (DisposalRecordFile) TableName() string {
	return TableNameDisposalRecordFile
}
