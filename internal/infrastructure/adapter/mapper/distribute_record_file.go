package mapper

import (
	"gorm.io/gorm"
)

const (
	TableNameDistributeRecordFile = "distribute_record_files"
)

// DistributeRecordFile 对应 distribute_record_files 表
type DistributeRecordFile struct {
	ID       string `gorm:"type:varchar(64);primary_key"`
	RecordID string `gorm:"type:varchar(64);index;comment:'关联DistributeRecord的ID'"`
	FileID   string `gorm:"type:varchar(64);comment:'文件id'"`
	FileName string `gorm:"type:varchar(255);comment:'文件名'"`
	Number   string `gorm:"type:varchar(255);comment:'文件编号'"`
	Version  string `gorm:"type:varchar(255);comment:'版本'"`
}

func (DistributeRecordFile) TableName() string {
	return TableNameDistributeRecordFile
}

// DistributeRecordFileClient 是 distribute_record_files 表的数据访问客户端
type DistributeRecordFileClient struct {
	db *gorm.DB
}

// NewDistributeRecordFileClient 创建一个新的 DistributeRecordFileClient 实例
func NewDistributeRecordFileClient(db *DocvaultDB) *DistributeRecordFileClient {
	return &DistributeRecordFileClient{
		db: db.GetDB(),
	}
}

// FindByFileID 根据文件ID查询发放记录文件
// 功能: 查询指定文件ID对应的所有发放记录文件，只返回状态为待审批或已审批的记录
// 参数:
//   - fileID: 文件ID
//
// 返回值:
//   - files: 发放记录文件列表
//   - err: 错误信息
func (c *DistributeRecordFileClient) FindByFileID(fileID string) ([]DistributeRecordFile, error) {
	var files []DistributeRecordFile
	// 关联查询 distribute_records 表，过滤状态为 2(待审批) 或 3(已审批) 的记录
	err := c.db.Table("distribute_record_files").
		Select("distribute_record_files.*").
		Joins("INNER JOIN distribute_records ON distribute_record_files.record_id = distribute_records.id").
		Where("distribute_record_files.file_id = ? AND distribute_records.status IN (?, ?)", fileID, 2, 3).
		Find(&files).Error
	return files, err
}

// FindByID 根据ID查询发放记录文件
// 功能: 查询指定ID对应的发放记录文件
// 参数:
//   - id: 发放记录文件ID
//
// 返回值:
//   - file: 发放记录文件
//   - err: 错误信息
func (c *DistributeRecordFileClient) FindByID(id string) (DistributeRecordFile, error) {
	var file DistributeRecordFile
	err := c.db.Where("id = ?", id).First(&file).Error
	return file, err
}
