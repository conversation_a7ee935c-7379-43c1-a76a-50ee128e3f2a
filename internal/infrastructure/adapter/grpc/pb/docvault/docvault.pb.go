// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v4.25.6
// source: internal/infrastructure/adapter/grpc/protos/docvault.proto

package docvault

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EmptyResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptyResp) Reset() {
	*x = EmptyResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptyResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResp) ProtoMessage() {}

func (x *EmptyResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResp.ProtoReflect.Descriptor instead.
func (*EmptyResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{0}
}

type ApprovalInfoItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PassedDate    int64                  `protobuf:"varint,2,opt,name=passed_date,json=passedDate,proto3" json:"passed_date,omitempty"`
	Nickname      string                 `protobuf:"bytes,3,opt,name=Nickname,proto3" json:"Nickname,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApprovalInfoItem) Reset() {
	*x = ApprovalInfoItem{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApprovalInfoItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApprovalInfoItem) ProtoMessage() {}

func (x *ApprovalInfoItem) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApprovalInfoItem.ProtoReflect.Descriptor instead.
func (*ApprovalInfoItem) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{1}
}

func (x *ApprovalInfoItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ApprovalInfoItem) GetPassedDate() int64 {
	if x != nil {
		return x.PassedDate
	}
	return 0
}

func (x *ApprovalInfoItem) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

type ApprovalInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Auditors      []*ApprovalInfoItem    `protobuf:"bytes,1,rep,name=auditors,proto3" json:"auditors,omitempty"`
	Approvers     []*ApprovalInfoItem    `protobuf:"bytes,2,rep,name=approvers,proto3" json:"approvers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApprovalInfo) Reset() {
	*x = ApprovalInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApprovalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApprovalInfo) ProtoMessage() {}

func (x *ApprovalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApprovalInfo.ProtoReflect.Descriptor instead.
func (*ApprovalInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{2}
}

func (x *ApprovalInfo) GetAuditors() []*ApprovalInfoItem {
	if x != nil {
		return x.Auditors
	}
	return nil
}

func (x *ApprovalInfo) GetApprovers() []*ApprovalInfoItem {
	if x != nil {
		return x.Approvers
	}
	return nil
}

type InternalDocumentCreateReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	NoPrefix          string                 `protobuf:"bytes,1,opt,name=no_prefix,json=noPrefix,proto3" json:"no_prefix,omitempty"`
	Name              string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	FileId            string                 `protobuf:"bytes,3,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	DocCategoryId     string                 `protobuf:"bytes,4,opt,name=doc_category_id,json=docCategoryId,proto3" json:"doc_category_id,omitempty"`
	DepartmentId      string                 `protobuf:"bytes,5,opt,name=department_id,json=departmentId,proto3" json:"department_id,omitempty"`
	VersionNo         int32                  `protobuf:"varint,8,opt,name=version_no,json=versionNo,proto3" json:"version_no,omitempty"`
	AuthorId          string                 `protobuf:"bytes,9,opt,name=author_id,json=authorId,proto3" json:"author_id,omitempty"`
	PublishDate       int64                  `protobuf:"varint,10,opt,name=publish_date,json=publishDate,proto3" json:"publish_date,omitempty"`
	EffectiveDate     int64                  `protobuf:"varint,11,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
	OriginalNo        string                 `protobuf:"bytes,12,opt,name=original_no,json=originalNo,proto3" json:"original_no,omitempty"`
	OriginalVersionNo string                 `protobuf:"bytes,13,opt,name=original_version_no,json=originalVersionNo,proto3" json:"original_version_no,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *InternalDocumentCreateReq) Reset() {
	*x = InternalDocumentCreateReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDocumentCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDocumentCreateReq) ProtoMessage() {}

func (x *InternalDocumentCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDocumentCreateReq.ProtoReflect.Descriptor instead.
func (*InternalDocumentCreateReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{3}
}

func (x *InternalDocumentCreateReq) GetNoPrefix() string {
	if x != nil {
		return x.NoPrefix
	}
	return ""
}

func (x *InternalDocumentCreateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InternalDocumentCreateReq) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *InternalDocumentCreateReq) GetDocCategoryId() string {
	if x != nil {
		return x.DocCategoryId
	}
	return ""
}

func (x *InternalDocumentCreateReq) GetDepartmentId() string {
	if x != nil {
		return x.DepartmentId
	}
	return ""
}

func (x *InternalDocumentCreateReq) GetVersionNo() int32 {
	if x != nil {
		return x.VersionNo
	}
	return 0
}

func (x *InternalDocumentCreateReq) GetAuthorId() string {
	if x != nil {
		return x.AuthorId
	}
	return ""
}

func (x *InternalDocumentCreateReq) GetPublishDate() int64 {
	if x != nil {
		return x.PublishDate
	}
	return 0
}

func (x *InternalDocumentCreateReq) GetEffectiveDate() int64 {
	if x != nil {
		return x.EffectiveDate
	}
	return 0
}

func (x *InternalDocumentCreateReq) GetOriginalNo() string {
	if x != nil {
		return x.OriginalNo
	}
	return ""
}

func (x *InternalDocumentCreateReq) GetOriginalVersionNo() string {
	if x != nil {
		return x.OriginalVersionNo
	}
	return ""
}

type InternalDocumentCreateResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalDocumentCreateResp) Reset() {
	*x = InternalDocumentCreateResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDocumentCreateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDocumentCreateResp) ProtoMessage() {}

func (x *InternalDocumentCreateResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDocumentCreateResp.ProtoReflect.Descriptor instead.
func (*InternalDocumentCreateResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{4}
}

func (x *InternalDocumentCreateResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type InternalDocumentChangeReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name              string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                       // 文件名称
	FileId            string                 `protobuf:"bytes,3,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`                                     // 文件id/文件名称
	DocCategoryId     string                 `protobuf:"bytes,4,opt,name=doc_category_id,json=docCategoryId,proto3" json:"doc_category_id,omitempty"`              // 文件类别
	DepartmentId      string                 `protobuf:"bytes,5,opt,name=department_id,json=departmentId,proto3" json:"department_id,omitempty"`                   // 编制部门
	VersionNo         int32                  `protobuf:"varint,7,opt,name=version_no,json=versionNo,proto3" json:"version_no,omitempty"`                           // 版本号
	PublishDate       int64                  `protobuf:"varint,8,opt,name=publish_date,json=publishDate,proto3" json:"publish_date,omitempty"`                     // 发布日期
	EffectiveDate     int64                  `protobuf:"varint,9,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`               // 实施日期
	OriginalNo        string                 `protobuf:"bytes,10,opt,name=original_no,json=originalNo,proto3" json:"original_no,omitempty"`                        // 原文件编号
	OriginalVersionNo string                 `protobuf:"bytes,11,opt,name=original_version_no,json=originalVersionNo,proto3" json:"original_version_no,omitempty"` // 原文件版本
	AuthorId          string                 `protobuf:"bytes,12,opt,name=author_id,json=authorId,proto3" json:"author_id,omitempty"`                              // 编制人
	NoPrefix          string                 `protobuf:"bytes,13,opt,name=no_prefix,json=noPrefix,proto3" json:"no_prefix,omitempty"`                              // 编号前缀
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *InternalDocumentChangeReq) Reset() {
	*x = InternalDocumentChangeReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDocumentChangeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDocumentChangeReq) ProtoMessage() {}

func (x *InternalDocumentChangeReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDocumentChangeReq.ProtoReflect.Descriptor instead.
func (*InternalDocumentChangeReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{5}
}

func (x *InternalDocumentChangeReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InternalDocumentChangeReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InternalDocumentChangeReq) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *InternalDocumentChangeReq) GetDocCategoryId() string {
	if x != nil {
		return x.DocCategoryId
	}
	return ""
}

func (x *InternalDocumentChangeReq) GetDepartmentId() string {
	if x != nil {
		return x.DepartmentId
	}
	return ""
}

func (x *InternalDocumentChangeReq) GetVersionNo() int32 {
	if x != nil {
		return x.VersionNo
	}
	return 0
}

func (x *InternalDocumentChangeReq) GetPublishDate() int64 {
	if x != nil {
		return x.PublishDate
	}
	return 0
}

func (x *InternalDocumentChangeReq) GetEffectiveDate() int64 {
	if x != nil {
		return x.EffectiveDate
	}
	return 0
}

func (x *InternalDocumentChangeReq) GetOriginalNo() string {
	if x != nil {
		return x.OriginalNo
	}
	return ""
}

func (x *InternalDocumentChangeReq) GetOriginalVersionNo() string {
	if x != nil {
		return x.OriginalVersionNo
	}
	return ""
}

func (x *InternalDocumentChangeReq) GetAuthorId() string {
	if x != nil {
		return x.AuthorId
	}
	return ""
}

func (x *InternalDocumentChangeReq) GetNoPrefix() string {
	if x != nil {
		return x.NoPrefix
	}
	return ""
}

type InternalDocumentGetReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalDocumentGetReq) Reset() {
	*x = InternalDocumentGetReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDocumentGetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDocumentGetReq) ProtoMessage() {}

func (x *InternalDocumentGetReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDocumentGetReq.ProtoReflect.Descriptor instead.
func (*InternalDocumentGetReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{6}
}

func (x *InternalDocumentGetReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type InternalDocumentGetResp struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	No                string                 `protobuf:"bytes,2,opt,name=no,proto3" json:"no,omitempty"`
	VersionNo         string                 `protobuf:"bytes,3,opt,name=version_no,json=versionNo,proto3" json:"version_no,omitempty"`
	OriginalNo        string                 `protobuf:"bytes,4,opt,name=original_no,json=originalNo,proto3" json:"original_no,omitempty"`
	OriginalVersionNo string                 `protobuf:"bytes,5,opt,name=original_version_no,json=originalVersionNo,proto3" json:"original_version_no,omitempty"`
	Name              string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	DocCategoryId     string                 `protobuf:"bytes,7,opt,name=doc_category_id,json=docCategoryId,proto3" json:"doc_category_id,omitempty"`
	DepartmentId      string                 `protobuf:"bytes,8,opt,name=department_id,json=departmentId,proto3" json:"department_id,omitempty"`
	AuthorId          string                 `protobuf:"bytes,9,opt,name=author_id,json=authorId,proto3" json:"author_id,omitempty"`
	ApprovalInfo      *ApprovalInfo          `protobuf:"bytes,10,opt,name=approval_info,json=approvalInfo,proto3" json:"approval_info,omitempty"`
	PublishDate       int64                  `protobuf:"varint,11,opt,name=publish_date,json=publishDate,proto3" json:"publish_date,omitempty"`
	EffectiveDate     int64                  `protobuf:"varint,12,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
	Status            int32                  `protobuf:"varint,13,opt,name=status,proto3" json:"status,omitempty"` // 1- 即将作废 2- 即将实施 3- 有效 4- 拟修订
	NoPrefix          string                 `protobuf:"bytes,14,opt,name=no_prefix,json=noPrefix,proto3" json:"no_prefix,omitempty"`
	FileId            string                 `protobuf:"bytes,15,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *InternalDocumentGetResp) Reset() {
	*x = InternalDocumentGetResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDocumentGetResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDocumentGetResp) ProtoMessage() {}

func (x *InternalDocumentGetResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDocumentGetResp.ProtoReflect.Descriptor instead.
func (*InternalDocumentGetResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{7}
}

func (x *InternalDocumentGetResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InternalDocumentGetResp) GetNo() string {
	if x != nil {
		return x.No
	}
	return ""
}

func (x *InternalDocumentGetResp) GetVersionNo() string {
	if x != nil {
		return x.VersionNo
	}
	return ""
}

func (x *InternalDocumentGetResp) GetOriginalNo() string {
	if x != nil {
		return x.OriginalNo
	}
	return ""
}

func (x *InternalDocumentGetResp) GetOriginalVersionNo() string {
	if x != nil {
		return x.OriginalVersionNo
	}
	return ""
}

func (x *InternalDocumentGetResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InternalDocumentGetResp) GetDocCategoryId() string {
	if x != nil {
		return x.DocCategoryId
	}
	return ""
}

func (x *InternalDocumentGetResp) GetDepartmentId() string {
	if x != nil {
		return x.DepartmentId
	}
	return ""
}

func (x *InternalDocumentGetResp) GetAuthorId() string {
	if x != nil {
		return x.AuthorId
	}
	return ""
}

func (x *InternalDocumentGetResp) GetApprovalInfo() *ApprovalInfo {
	if x != nil {
		return x.ApprovalInfo
	}
	return nil
}

func (x *InternalDocumentGetResp) GetPublishDate() int64 {
	if x != nil {
		return x.PublishDate
	}
	return 0
}

func (x *InternalDocumentGetResp) GetEffectiveDate() int64 {
	if x != nil {
		return x.EffectiveDate
	}
	return 0
}

func (x *InternalDocumentGetResp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *InternalDocumentGetResp) GetNoPrefix() string {
	if x != nil {
		return x.NoPrefix
	}
	return ""
}

func (x *InternalDocumentGetResp) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

type PageInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	NoPage        bool                   `protobuf:"varint,3,opt,name=no_page,json=noPage,proto3" json:"no_page,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PageInfo) Reset() {
	*x = PageInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageInfo) ProtoMessage() {}

func (x *PageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageInfo.ProtoReflect.Descriptor instead.
func (*PageInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{8}
}

func (x *PageInfo) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageInfo) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageInfo) GetNoPage() bool {
	if x != nil {
		return x.NoPage
	}
	return false
}

type InternalDocumentPageReq struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	PageInfo *PageInfo              `protobuf:"bytes,1,opt,name=page_info,json=pageInfo,proto3" json:"page_info,omitempty"`
	Search   string                 `protobuf:"bytes,2,opt,name=search,proto3" json:"search,omitempty"`
	Ids      []string               `protobuf:"bytes,3,rep,name=ids,proto3" json:"ids,omitempty"`
	// 类别ids
	DocCategoryIds []string `protobuf:"bytes,4,rep,name=doc_category_ids,json=docCategoryIds,proto3" json:"doc_category_ids,omitempty"`
	// 部门ids
	DepartmentIds []string `protobuf:"bytes,5,rep,name=department_ids,json=departmentIds,proto3" json:"department_ids,omitempty"`
	// 状态 1- 即将作废 2- 即将实施 3- 有效 4- 拟修订
	Status int32 `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	// 是否有附件
	HasAttachment int32  `protobuf:"varint,7,opt,name=has_attachment,json=hasAttachment,proto3" json:"has_attachment,omitempty"` // 0-全部，1-有附件，2-无附件
	Name          string `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`                                         // 文件名称
	No            string `protobuf:"bytes,9,opt,name=no,proto3" json:"no,omitempty"`                                             // 文件编号
	OriginalNo    string `protobuf:"bytes,10,opt,name=original_no,json=originalNo,proto3" json:"original_no,omitempty"`          // 原文件编号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalDocumentPageReq) Reset() {
	*x = InternalDocumentPageReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDocumentPageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDocumentPageReq) ProtoMessage() {}

func (x *InternalDocumentPageReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDocumentPageReq.ProtoReflect.Descriptor instead.
func (*InternalDocumentPageReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{9}
}

func (x *InternalDocumentPageReq) GetPageInfo() *PageInfo {
	if x != nil {
		return x.PageInfo
	}
	return nil
}

func (x *InternalDocumentPageReq) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

func (x *InternalDocumentPageReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *InternalDocumentPageReq) GetDocCategoryIds() []string {
	if x != nil {
		return x.DocCategoryIds
	}
	return nil
}

func (x *InternalDocumentPageReq) GetDepartmentIds() []string {
	if x != nil {
		return x.DepartmentIds
	}
	return nil
}

func (x *InternalDocumentPageReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *InternalDocumentPageReq) GetHasAttachment() int32 {
	if x != nil {
		return x.HasAttachment
	}
	return 0
}

func (x *InternalDocumentPageReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InternalDocumentPageReq) GetNo() string {
	if x != nil {
		return x.No
	}
	return ""
}

func (x *InternalDocumentPageReq) GetOriginalNo() string {
	if x != nil {
		return x.OriginalNo
	}
	return ""
}

type InternalDocumentPageResp struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Data          []*InternalDocumentPageItem `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Total         int64                       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalDocumentPageResp) Reset() {
	*x = InternalDocumentPageResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDocumentPageResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDocumentPageResp) ProtoMessage() {}

func (x *InternalDocumentPageResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDocumentPageResp.ProtoReflect.Descriptor instead.
func (*InternalDocumentPageResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{10}
}

func (x *InternalDocumentPageResp) GetData() []*InternalDocumentPageItem {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *InternalDocumentPageResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type InternalDocumentPageItem struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	No                string                 `protobuf:"bytes,2,opt,name=no,proto3" json:"no,omitempty"`
	VersionNo         string                 `protobuf:"bytes,3,opt,name=version_no,json=versionNo,proto3" json:"version_no,omitempty"`
	OriginalNo        string                 `protobuf:"bytes,4,opt,name=original_no,json=originalNo,proto3" json:"original_no,omitempty"`
	OriginalVersionNo string                 `protobuf:"bytes,5,opt,name=original_version_no,json=originalVersionNo,proto3" json:"original_version_no,omitempty"`
	Name              string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	DocCategoryId     string                 `protobuf:"bytes,7,opt,name=doc_category_id,json=docCategoryId,proto3" json:"doc_category_id,omitempty"`
	DepartmentId      string                 `protobuf:"bytes,8,opt,name=department_id,json=departmentId,proto3" json:"department_id,omitempty"`
	AuthorId          string                 `protobuf:"bytes,9,opt,name=author_id,json=authorId,proto3" json:"author_id,omitempty"`
	ApprovalInfo      *ApprovalInfo          `protobuf:"bytes,10,opt,name=approval_info,json=approvalInfo,proto3" json:"approval_info,omitempty"`
	PublishDate       int64                  `protobuf:"varint,11,opt,name=publish_date,json=publishDate,proto3" json:"publish_date,omitempty"`
	EffectiveDate     int64                  `protobuf:"varint,12,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
	Status            int32                  `protobuf:"varint,13,opt,name=status,proto3" json:"status,omitempty"` // 1- 即将作废 2- 即将实施 3- 有效 4- 拟修订
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *InternalDocumentPageItem) Reset() {
	*x = InternalDocumentPageItem{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDocumentPageItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDocumentPageItem) ProtoMessage() {}

func (x *InternalDocumentPageItem) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDocumentPageItem.ProtoReflect.Descriptor instead.
func (*InternalDocumentPageItem) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{11}
}

func (x *InternalDocumentPageItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InternalDocumentPageItem) GetNo() string {
	if x != nil {
		return x.No
	}
	return ""
}

func (x *InternalDocumentPageItem) GetVersionNo() string {
	if x != nil {
		return x.VersionNo
	}
	return ""
}

func (x *InternalDocumentPageItem) GetOriginalNo() string {
	if x != nil {
		return x.OriginalNo
	}
	return ""
}

func (x *InternalDocumentPageItem) GetOriginalVersionNo() string {
	if x != nil {
		return x.OriginalVersionNo
	}
	return ""
}

func (x *InternalDocumentPageItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InternalDocumentPageItem) GetDocCategoryId() string {
	if x != nil {
		return x.DocCategoryId
	}
	return ""
}

func (x *InternalDocumentPageItem) GetDepartmentId() string {
	if x != nil {
		return x.DepartmentId
	}
	return ""
}

func (x *InternalDocumentPageItem) GetAuthorId() string {
	if x != nil {
		return x.AuthorId
	}
	return ""
}

func (x *InternalDocumentPageItem) GetApprovalInfo() *ApprovalInfo {
	if x != nil {
		return x.ApprovalInfo
	}
	return nil
}

func (x *InternalDocumentPageItem) GetPublishDate() int64 {
	if x != nil {
		return x.PublishDate
	}
	return 0
}

func (x *InternalDocumentPageItem) GetEffectiveDate() int64 {
	if x != nil {
		return x.EffectiveDate
	}
	return 0
}

func (x *InternalDocumentPageItem) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type CreateBookReq struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DictionaryNodeId string                 `protobuf:"bytes,1,opt,name=dictionary_node_id,json=dictionaryNodeId,proto3" json:"dictionary_node_id,omitempty"`
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Author           string                 `protobuf:"bytes,3,opt,name=author,proto3" json:"author,omitempty"`
	Publisher        string                 `protobuf:"bytes,4,opt,name=publisher,proto3" json:"publisher,omitempty"`
	RegisterCount    int32                  `protobuf:"varint,5,opt,name=register_count,json=registerCount,proto3" json:"register_count,omitempty"`
	BookType         string                 `protobuf:"bytes,6,opt,name=book_type,json=bookType,proto3" json:"book_type,omitempty"`
	UserId           string                 `protobuf:"bytes,7,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrganizationId   string                 `protobuf:"bytes,8,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	OrganizationCode string                 `protobuf:"bytes,9,opt,name=organization_code,json=organizationCode,proto3" json:"organization_code,omitempty"`
	BookNumber       string                 `protobuf:"bytes,10,opt,name=book_number,json=bookNumber,proto3" json:"book_number,omitempty"`
	FileId           string                 `protobuf:"bytes,11,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CreateBookReq) Reset() {
	*x = CreateBookReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateBookReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookReq) ProtoMessage() {}

func (x *CreateBookReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookReq.ProtoReflect.Descriptor instead.
func (*CreateBookReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{12}
}

func (x *CreateBookReq) GetDictionaryNodeId() string {
	if x != nil {
		return x.DictionaryNodeId
	}
	return ""
}

func (x *CreateBookReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateBookReq) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *CreateBookReq) GetPublisher() string {
	if x != nil {
		return x.Publisher
	}
	return ""
}

func (x *CreateBookReq) GetRegisterCount() int32 {
	if x != nil {
		return x.RegisterCount
	}
	return 0
}

func (x *CreateBookReq) GetBookType() string {
	if x != nil {
		return x.BookType
	}
	return ""
}

func (x *CreateBookReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreateBookReq) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *CreateBookReq) GetOrganizationCode() string {
	if x != nil {
		return x.OrganizationCode
	}
	return ""
}

func (x *CreateBookReq) GetBookNumber() string {
	if x != nil {
		return x.BookNumber
	}
	return ""
}

func (x *CreateBookReq) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

type CreateBookResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateBookResp) Reset() {
	*x = CreateBookResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateBookResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookResp) ProtoMessage() {}

func (x *CreateBookResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookResp.ProtoReflect.Descriptor instead.
func (*CreateBookResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{13}
}

func (x *CreateBookResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetBookListReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	PageInfo          *PageInfo              `protobuf:"bytes,1,opt,name=page_info,json=pageInfo,proto3" json:"page_info,omitempty"`
	Number            string                 `protobuf:"bytes,2,opt,name=number,proto3" json:"number,omitempty"`
	Name              string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Author            string                 `protobuf:"bytes,4,opt,name=author,proto3" json:"author,omitempty"`
	Publisher         string                 `protobuf:"bytes,5,opt,name=publisher,proto3" json:"publisher,omitempty"`
	DictionaryNodeIds []string               `protobuf:"bytes,6,rep,name=dictionary_node_ids,json=dictionaryNodeIds,proto3" json:"dictionary_node_ids,omitempty"`
	OnBorrow          string                 `protobuf:"bytes,7,opt,name=on_borrow,json=onBorrow,proto3" json:"on_borrow,omitempty"`
	OrganizationId    string                 `protobuf:"bytes,8,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetBookListReq) Reset() {
	*x = GetBookListReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBookListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookListReq) ProtoMessage() {}

func (x *GetBookListReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookListReq.ProtoReflect.Descriptor instead.
func (*GetBookListReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{14}
}

func (x *GetBookListReq) GetPageInfo() *PageInfo {
	if x != nil {
		return x.PageInfo
	}
	return nil
}

func (x *GetBookListReq) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *GetBookListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetBookListReq) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *GetBookListReq) GetPublisher() string {
	if x != nil {
		return x.Publisher
	}
	return ""
}

func (x *GetBookListReq) GetDictionaryNodeIds() []string {
	if x != nil {
		return x.DictionaryNodeIds
	}
	return nil
}

func (x *GetBookListReq) GetOnBorrow() string {
	if x != nil {
		return x.OnBorrow
	}
	return ""
}

func (x *GetBookListReq) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type BookInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                        // 主键id
	Status           int32                  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`                                               // 状态（0未启用 | 1启用）
	IsDelete         int32                  `protobuf:"varint,3,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`                           // 是否删除（0未删除 | 1已删除）
	Number           string                 `protobuf:"bytes,4,opt,name=number,proto3" json:"number,omitempty"`                                                // 书籍编号
	Name             string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                                                    // 书籍名称
	Author           string                 `protobuf:"bytes,6,opt,name=author,proto3" json:"author,omitempty"`                                                // 作者/编者
	Publisher        string                 `protobuf:"bytes,7,opt,name=publisher,proto3" json:"publisher,omitempty"`                                          // 出版社
	BookType         string                 `protobuf:"bytes,8,opt,name=book_type,json=bookType,proto3" json:"book_type,omitempty"`                            // 书籍类型
	RegisterCount    int32                  `protobuf:"varint,9,opt,name=register_count,json=registerCount,proto3" json:"register_count,omitempty"`            // 总在册数
	ReceiveCount     int32                  `protobuf:"varint,10,opt,name=receive_count,json=receiveCount,proto3" json:"receive_count,omitempty"`              // 领用数
	BorrowCount      int32                  `protobuf:"varint,11,opt,name=borrow_count,json=borrowCount,proto3" json:"borrow_count,omitempty"`                 // 借用数
	OnBorrow         bool                   `protobuf:"varint,12,opt,name=on_borrow,json=onBorrow,proto3" json:"on_borrow,omitempty"`                          // 借用状态(false未借用 | true借用中)
	SurplusCount     int32                  `protobuf:"varint,13,opt,name=surplus_count,json=surplusCount,proto3" json:"surplus_count,omitempty"`              // 剩余数
	CreatedTime      int64                  `protobuf:"varint,14,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`                 // 创建时间（字符串格式，或使用 Timestamp）
	UpdatedTime      int64                  `protobuf:"varint,15,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`                 // 更新时间（字符串格式，或使用 Timestamp）
	CreatedBy        string                 `protobuf:"bytes,16,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                        // 创建人
	UpdatedBy        string                 `protobuf:"bytes,17,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                        // 修改人
	DictionaryNodeId string                 `protobuf:"bytes,18,opt,name=dictionary_node_id,json=dictionaryNodeId,proto3" json:"dictionary_node_id,omitempty"` // 类型id
	FileId           string                 `protobuf:"bytes,19,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`                                 // 书籍文件id
	OrganizationId   string                 `protobuf:"bytes,20,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`         // 所属组织
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BookInfo) Reset() {
	*x = BookInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BookInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookInfo) ProtoMessage() {}

func (x *BookInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookInfo.ProtoReflect.Descriptor instead.
func (*BookInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{15}
}

func (x *BookInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BookInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *BookInfo) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *BookInfo) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *BookInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BookInfo) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *BookInfo) GetPublisher() string {
	if x != nil {
		return x.Publisher
	}
	return ""
}

func (x *BookInfo) GetBookType() string {
	if x != nil {
		return x.BookType
	}
	return ""
}

func (x *BookInfo) GetRegisterCount() int32 {
	if x != nil {
		return x.RegisterCount
	}
	return 0
}

func (x *BookInfo) GetReceiveCount() int32 {
	if x != nil {
		return x.ReceiveCount
	}
	return 0
}

func (x *BookInfo) GetBorrowCount() int32 {
	if x != nil {
		return x.BorrowCount
	}
	return 0
}

func (x *BookInfo) GetOnBorrow() bool {
	if x != nil {
		return x.OnBorrow
	}
	return false
}

func (x *BookInfo) GetSurplusCount() int32 {
	if x != nil {
		return x.SurplusCount
	}
	return 0
}

func (x *BookInfo) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *BookInfo) GetUpdatedTime() int64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *BookInfo) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *BookInfo) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *BookInfo) GetDictionaryNodeId() string {
	if x != nil {
		return x.DictionaryNodeId
	}
	return ""
}

func (x *BookInfo) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *BookInfo) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type GetBookListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int32                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Data          []*BookInfo            `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBookListResp) Reset() {
	*x = GetBookListResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBookListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookListResp) ProtoMessage() {}

func (x *GetBookListResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookListResp.ProtoReflect.Descriptor instead.
func (*GetBookListResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{16}
}

func (x *GetBookListResp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetBookListResp) GetData() []*BookInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateBookResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateBookResp) Reset() {
	*x = UpdateBookResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBookResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookResp) ProtoMessage() {}

func (x *UpdateBookResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookResp.ProtoReflect.Descriptor instead.
func (*UpdateBookResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateBookResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateBookResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type DeleteBookReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteBookReq) Reset() {
	*x = DeleteBookReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteBookReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBookReq) ProtoMessage() {}

func (x *DeleteBookReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBookReq.ProtoReflect.Descriptor instead.
func (*DeleteBookReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteBookReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteBookResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteBookResp) Reset() {
	*x = DeleteBookResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteBookResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBookResp) ProtoMessage() {}

func (x *DeleteBookResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBookResp.ProtoReflect.Descriptor instead.
func (*DeleteBookResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteBookResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteBookResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type ImportBookReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ImportBookInfo []*ImportBookInfo      `protobuf:"bytes,1,rep,name=import_book_info,json=importBookInfo,proto3" json:"import_book_info,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ImportBookReq) Reset() {
	*x = ImportBookReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportBookReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportBookReq) ProtoMessage() {}

func (x *ImportBookReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportBookReq.ProtoReflect.Descriptor instead.
func (*ImportBookReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{20}
}

func (x *ImportBookReq) GetImportBookInfo() []*ImportBookInfo {
	if x != nil {
		return x.ImportBookInfo
	}
	return nil
}

type ImportBookInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DictionaryNodeId string                 `protobuf:"bytes,1,opt,name=dictionary_node_id,json=dictionaryNodeId,proto3" json:"dictionary_node_id,omitempty"`
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Author           string                 `protobuf:"bytes,3,opt,name=author,proto3" json:"author,omitempty"`
	Publisher        string                 `protobuf:"bytes,4,opt,name=publisher,proto3" json:"publisher,omitempty"`
	RegisterCount    int32                  `protobuf:"varint,5,opt,name=register_count,json=registerCount,proto3" json:"register_count,omitempty"`
	BookType         string                 `protobuf:"bytes,6,opt,name=book_type,json=bookType,proto3" json:"book_type,omitempty"`
	Number           string                 `protobuf:"bytes,7,opt,name=number,proto3" json:"number,omitempty"`
	FileId           string                 `protobuf:"bytes,8,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ImportBookInfo) Reset() {
	*x = ImportBookInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportBookInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportBookInfo) ProtoMessage() {}

func (x *ImportBookInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportBookInfo.ProtoReflect.Descriptor instead.
func (*ImportBookInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{21}
}

func (x *ImportBookInfo) GetDictionaryNodeId() string {
	if x != nil {
		return x.DictionaryNodeId
	}
	return ""
}

func (x *ImportBookInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ImportBookInfo) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *ImportBookInfo) GetPublisher() string {
	if x != nil {
		return x.Publisher
	}
	return ""
}

func (x *ImportBookInfo) GetRegisterCount() int32 {
	if x != nil {
		return x.RegisterCount
	}
	return 0
}

func (x *ImportBookInfo) GetBookType() string {
	if x != nil {
		return x.BookType
	}
	return ""
}

func (x *ImportBookInfo) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *ImportBookInfo) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

type ImportBookResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*ImportRespInfo      `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportBookResp) Reset() {
	*x = ImportBookResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportBookResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportBookResp) ProtoMessage() {}

func (x *ImportBookResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportBookResp.ProtoReflect.Descriptor instead.
func (*ImportBookResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{22}
}

func (x *ImportBookResp) GetData() []*ImportRespInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type ImportRespInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DictionaryNodeId string                 `protobuf:"bytes,1,opt,name=dictionary_node_id,json=dictionaryNodeId,proto3" json:"dictionary_node_id,omitempty"`
	BookId           string                 `protobuf:"bytes,2,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ImportRespInfo) Reset() {
	*x = ImportRespInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportRespInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportRespInfo) ProtoMessage() {}

func (x *ImportRespInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportRespInfo.ProtoReflect.Descriptor instead.
func (*ImportRespInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{23}
}

func (x *ImportRespInfo) GetDictionaryNodeId() string {
	if x != nil {
		return x.DictionaryNodeId
	}
	return ""
}

func (x *ImportRespInfo) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

type InternalDocumentBatchCreateReq struct {
	state                      protoimpl.MessageState       `protogen:"open.v1"`
	InternalDocumentCreateReqs []*InternalDocumentCreateReq `protobuf:"bytes,1,rep,name=internal_document_create_reqs,json=internalDocumentCreateReqs,proto3" json:"internal_document_create_reqs,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *InternalDocumentBatchCreateReq) Reset() {
	*x = InternalDocumentBatchCreateReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDocumentBatchCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDocumentBatchCreateReq) ProtoMessage() {}

func (x *InternalDocumentBatchCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDocumentBatchCreateReq.ProtoReflect.Descriptor instead.
func (*InternalDocumentBatchCreateReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{24}
}

func (x *InternalDocumentBatchCreateReq) GetInternalDocumentCreateReqs() []*InternalDocumentCreateReq {
	if x != nil {
		return x.InternalDocumentCreateReqs
	}
	return nil
}

type InternalDocumentBatchCreateResp struct {
	state                 protoimpl.MessageState     `protogen:"open.v1"`
	InternalDocumentInfos []*InternalDocumentGetResp `protobuf:"bytes,1,rep,name=internal_document_infos,json=internalDocumentInfos,proto3" json:"internal_document_infos,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *InternalDocumentBatchCreateResp) Reset() {
	*x = InternalDocumentBatchCreateResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalDocumentBatchCreateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalDocumentBatchCreateResp) ProtoMessage() {}

func (x *InternalDocumentBatchCreateResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalDocumentBatchCreateResp.ProtoReflect.Descriptor instead.
func (*InternalDocumentBatchCreateResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{25}
}

func (x *InternalDocumentBatchCreateResp) GetInternalDocumentInfos() []*InternalDocumentGetResp {
	if x != nil {
		return x.InternalDocumentInfos
	}
	return nil
}

type ExternalDocumentCreateReq struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Data          []*ExternalDocumentCreateInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	OrgType       int32                         `protobuf:"varint,2,opt,name=org_type,json=orgType,proto3" json:"org_type,omitempty"`
	OrgId         string                        `protobuf:"bytes,3,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExternalDocumentCreateReq) Reset() {
	*x = ExternalDocumentCreateReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocumentCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocumentCreateReq) ProtoMessage() {}

func (x *ExternalDocumentCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocumentCreateReq.ProtoReflect.Descriptor instead.
func (*ExternalDocumentCreateReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{26}
}

func (x *ExternalDocumentCreateReq) GetData() []*ExternalDocumentCreateInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ExternalDocumentCreateReq) GetOrgType() int32 {
	if x != nil {
		return x.OrgType
	}
	return 0
}

func (x *ExternalDocumentCreateReq) GetOrgId() string {
	if x != nil {
		return x.OrgId
	}
	return ""
}

type ExternalDocumentCreateInfo struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	TypeDictionaryNodeId            string                 `protobuf:"bytes,1,opt,name=type_dictionary_node_id,json=typeDictionaryNodeId,proto3" json:"type_dictionary_node_id,omitempty"`
	DomainDictionaryNodeId          string                 `protobuf:"bytes,2,opt,name=domain_dictionary_node_id,json=domainDictionaryNodeId,proto3" json:"domain_dictionary_node_id,omitempty"`
	AuthenticationDictionaryNodeIds []string               `protobuf:"bytes,3,rep,name=authentication_dictionary_node_ids,json=authenticationDictionaryNodeIds,proto3" json:"authentication_dictionary_node_ids,omitempty"`
	NumberPrefix                    string                 `protobuf:"bytes,4,opt,name=number_prefix,json=numberPrefix,proto3" json:"number_prefix,omitempty"`
	DocType                         string                 `protobuf:"bytes,5,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	Domain                          string                 `protobuf:"bytes,6,opt,name=domain,proto3" json:"domain,omitempty"`
	Authentications                 []string               `protobuf:"bytes,7,rep,name=authentications,proto3" json:"authentications,omitempty"`
	Name                            string                 `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	OriginalDocNumber               string                 `protobuf:"bytes,9,opt,name=original_doc_number,json=originalDocNumber,proto3" json:"original_doc_number,omitempty"`
	PublishDocNumber                string                 `protobuf:"bytes,10,opt,name=publish_doc_number,json=publishDocNumber,proto3" json:"publish_doc_number,omitempty"`
	PublishDepartment               string                 `protobuf:"bytes,11,opt,name=publish_department,json=publishDepartment,proto3" json:"publish_department,omitempty"`
	FileId                          string                 `protobuf:"bytes,12,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	PublishDate                     int64                  `protobuf:"varint,13,opt,name=publish_date,json=publishDate,proto3" json:"publish_date,omitempty"`
	EffectiveDate                   int64                  `protobuf:"varint,14,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
	OriginalNumber                  string                 `protobuf:"bytes,15,opt,name=Original_number,json=OriginalNumber,proto3" json:"Original_number,omitempty"`
	OriginalVersion                 string                 `protobuf:"bytes,16,opt,name=Original_version,json=OriginalVersion,proto3" json:"Original_version,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *ExternalDocumentCreateInfo) Reset() {
	*x = ExternalDocumentCreateInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocumentCreateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocumentCreateInfo) ProtoMessage() {}

func (x *ExternalDocumentCreateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocumentCreateInfo.ProtoReflect.Descriptor instead.
func (*ExternalDocumentCreateInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{27}
}

func (x *ExternalDocumentCreateInfo) GetTypeDictionaryNodeId() string {
	if x != nil {
		return x.TypeDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentCreateInfo) GetDomainDictionaryNodeId() string {
	if x != nil {
		return x.DomainDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentCreateInfo) GetAuthenticationDictionaryNodeIds() []string {
	if x != nil {
		return x.AuthenticationDictionaryNodeIds
	}
	return nil
}

func (x *ExternalDocumentCreateInfo) GetNumberPrefix() string {
	if x != nil {
		return x.NumberPrefix
	}
	return ""
}

func (x *ExternalDocumentCreateInfo) GetDocType() string {
	if x != nil {
		return x.DocType
	}
	return ""
}

func (x *ExternalDocumentCreateInfo) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ExternalDocumentCreateInfo) GetAuthentications() []string {
	if x != nil {
		return x.Authentications
	}
	return nil
}

func (x *ExternalDocumentCreateInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExternalDocumentCreateInfo) GetOriginalDocNumber() string {
	if x != nil {
		return x.OriginalDocNumber
	}
	return ""
}

func (x *ExternalDocumentCreateInfo) GetPublishDocNumber() string {
	if x != nil {
		return x.PublishDocNumber
	}
	return ""
}

func (x *ExternalDocumentCreateInfo) GetPublishDepartment() string {
	if x != nil {
		return x.PublishDepartment
	}
	return ""
}

func (x *ExternalDocumentCreateInfo) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *ExternalDocumentCreateInfo) GetPublishDate() int64 {
	if x != nil {
		return x.PublishDate
	}
	return 0
}

func (x *ExternalDocumentCreateInfo) GetEffectiveDate() int64 {
	if x != nil {
		return x.EffectiveDate
	}
	return 0
}

func (x *ExternalDocumentCreateInfo) GetOriginalNumber() string {
	if x != nil {
		return x.OriginalNumber
	}
	return ""
}

func (x *ExternalDocumentCreateInfo) GetOriginalVersion() string {
	if x != nil {
		return x.OriginalVersion
	}
	return ""
}

type ExternalDocumentCreateResp struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	Data          []*ExternalDocumentCreateRespList `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExternalDocumentCreateResp) Reset() {
	*x = ExternalDocumentCreateResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocumentCreateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocumentCreateResp) ProtoMessage() {}

func (x *ExternalDocumentCreateResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocumentCreateResp.ProtoReflect.Descriptor instead.
func (*ExternalDocumentCreateResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{28}
}

func (x *ExternalDocumentCreateResp) GetData() []*ExternalDocumentCreateRespList {
	if x != nil {
		return x.Data
	}
	return nil
}

type ExternalDocumentCreateRespList struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	Id                              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TypeDictionaryNodeId            string                 `protobuf:"bytes,2,opt,name=type_dictionary_node_id,json=typeDictionaryNodeId,proto3" json:"type_dictionary_node_id,omitempty"`
	DomainDictionaryNodeId          string                 `protobuf:"bytes,3,opt,name=domain_dictionary_node_id,json=domainDictionaryNodeId,proto3" json:"domain_dictionary_node_id,omitempty"`
	AuthenticationDictionaryNodeIds []string               `protobuf:"bytes,4,rep,name=authentication_dictionary_node_ids,json=authenticationDictionaryNodeIds,proto3" json:"authentication_dictionary_node_ids,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *ExternalDocumentCreateRespList) Reset() {
	*x = ExternalDocumentCreateRespList{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocumentCreateRespList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocumentCreateRespList) ProtoMessage() {}

func (x *ExternalDocumentCreateRespList) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocumentCreateRespList.ProtoReflect.Descriptor instead.
func (*ExternalDocumentCreateRespList) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{29}
}

func (x *ExternalDocumentCreateRespList) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExternalDocumentCreateRespList) GetTypeDictionaryNodeId() string {
	if x != nil {
		return x.TypeDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentCreateRespList) GetDomainDictionaryNodeId() string {
	if x != nil {
		return x.DomainDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentCreateRespList) GetAuthenticationDictionaryNodeIds() []string {
	if x != nil {
		return x.AuthenticationDictionaryNodeIds
	}
	return nil
}

type ExternalDocumentGetReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExternalDocumentGetReq) Reset() {
	*x = ExternalDocumentGetReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocumentGetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocumentGetReq) ProtoMessage() {}

func (x *ExternalDocumentGetReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocumentGetReq.ProtoReflect.Descriptor instead.
func (*ExternalDocumentGetReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{30}
}

func (x *ExternalDocumentGetReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ExternalDocumentGetResp struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	Id                              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Number                          string                 `protobuf:"bytes,2,opt,name=number,proto3" json:"number,omitempty"`
	Version                         string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	OriginalNumber                  string                 `protobuf:"bytes,4,opt,name=original_number,json=originalNumber,proto3" json:"original_number,omitempty"`
	OriginalVersion                 string                 `protobuf:"bytes,5,opt,name=original_version,json=originalVersion,proto3" json:"original_version,omitempty"`
	Name                            string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	DocType                         string                 `protobuf:"bytes,7,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	Domain                          string                 `protobuf:"bytes,8,opt,name=domain,proto3" json:"domain,omitempty"`
	OriginalDocNumber               string                 `protobuf:"bytes,9,opt,name=original_doc_number,json=originalDocNumber,proto3" json:"original_doc_number,omitempty"`
	PublishDocNumber                string                 `protobuf:"bytes,10,opt,name=publish_doc_number,json=publishDocNumber,proto3" json:"publish_doc_number,omitempty"`
	PublishDepartment               string                 `protobuf:"bytes,11,opt,name=publish_department,json=publishDepartment,proto3" json:"publish_department,omitempty"`
	PublishDate                     int64                  `protobuf:"varint,12,opt,name=publish_date,json=publishDate,proto3" json:"publish_date,omitempty"`
	EffectiveDate                   int64                  `protobuf:"varint,13,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
	Authentication                  string                 `protobuf:"bytes,14,opt,name=Authentication,proto3" json:"Authentication,omitempty"`
	Status                          int32                  `protobuf:"varint,15,opt,name=status,proto3" json:"status,omitempty"`
	TypeDictionaryNodeId            string                 `protobuf:"bytes,16,opt,name=type_dictionary_node_id,json=typeDictionaryNodeId,proto3" json:"type_dictionary_node_id,omitempty"`
	DomainDictionaryNodeId          string                 `protobuf:"bytes,17,opt,name=domain_dictionary_node_id,json=domainDictionaryNodeId,proto3" json:"domain_dictionary_node_id,omitempty"`
	AuthenticationDictionaryNodeIds []string               `protobuf:"bytes,18,rep,name=authentication_dictionary_node_ids,json=authenticationDictionaryNodeIds,proto3" json:"authentication_dictionary_node_ids,omitempty"`
	FileId                          string                 `protobuf:"bytes,19,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *ExternalDocumentGetResp) Reset() {
	*x = ExternalDocumentGetResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocumentGetResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocumentGetResp) ProtoMessage() {}

func (x *ExternalDocumentGetResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocumentGetResp.ProtoReflect.Descriptor instead.
func (*ExternalDocumentGetResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{31}
}

func (x *ExternalDocumentGetResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetOriginalNumber() string {
	if x != nil {
		return x.OriginalNumber
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetOriginalVersion() string {
	if x != nil {
		return x.OriginalVersion
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetDocType() string {
	if x != nil {
		return x.DocType
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetOriginalDocNumber() string {
	if x != nil {
		return x.OriginalDocNumber
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetPublishDocNumber() string {
	if x != nil {
		return x.PublishDocNumber
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetPublishDepartment() string {
	if x != nil {
		return x.PublishDepartment
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetPublishDate() int64 {
	if x != nil {
		return x.PublishDate
	}
	return 0
}

func (x *ExternalDocumentGetResp) GetEffectiveDate() int64 {
	if x != nil {
		return x.EffectiveDate
	}
	return 0
}

func (x *ExternalDocumentGetResp) GetAuthentication() string {
	if x != nil {
		return x.Authentication
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ExternalDocumentGetResp) GetTypeDictionaryNodeId() string {
	if x != nil {
		return x.TypeDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetDomainDictionaryNodeId() string {
	if x != nil {
		return x.DomainDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentGetResp) GetAuthenticationDictionaryNodeIds() []string {
	if x != nil {
		return x.AuthenticationDictionaryNodeIds
	}
	return nil
}

func (x *ExternalDocumentGetResp) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

type ExternalDocumentPageReq struct {
	state                          protoimpl.MessageState `protogen:"open.v1"`
	PageInfo                       *PageInfo              `protobuf:"bytes,1,opt,name=page_info,json=pageInfo,proto3" json:"page_info,omitempty"`
	Number                         string                 `protobuf:"bytes,2,opt,name=number,proto3" json:"number,omitempty"`
	Name                           string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	OriginalNumber                 string                 `protobuf:"bytes,4,opt,name=original_number,json=originalNumber,proto3" json:"original_number,omitempty"`
	OriginalDocNumber              string                 `protobuf:"bytes,5,opt,name=original_docNumber,json=originalDocNumber,proto3" json:"original_docNumber,omitempty"`
	PublishDocNumber               string                 `protobuf:"bytes,6,opt,name=publish_doc_number,json=publishDocNumber,proto3" json:"publish_doc_number,omitempty"`
	PublishDepartment              string                 `protobuf:"bytes,7,opt,name=publish_department,json=publishDepartment,proto3" json:"publish_department,omitempty"`
	TypeDictionaryNodeIds          []string               `protobuf:"bytes,8,rep,name=type_dictionary_node_ids,json=typeDictionaryNodeIds,proto3" json:"type_dictionary_node_ids,omitempty"`
	DomainDictionaryNodeId         string                 `protobuf:"bytes,9,opt,name=domain_dictionary_node_id,json=domainDictionaryNodeId,proto3" json:"domain_dictionary_node_id,omitempty"`
	AuthenticationDictionaryNodeId string                 `protobuf:"bytes,10,opt,name=authentication_dictionary_node_id,json=authenticationDictionaryNodeId,proto3" json:"authentication_dictionary_node_id,omitempty"`
	BeAttachedFile                 string                 `protobuf:"bytes,11,opt,name=be_attached_file,json=beAttachedFile,proto3" json:"be_attached_file,omitempty"`
	Status                         int32                  `protobuf:"varint,12,opt,name=status,proto3" json:"status,omitempty"`
	OrgType                        int32                  `protobuf:"varint,13,opt,name=org_type,json=orgType,proto3" json:"org_type,omitempty"`
	OrgId                          string                 `protobuf:"bytes,14,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	unknownFields                  protoimpl.UnknownFields
	sizeCache                      protoimpl.SizeCache
}

func (x *ExternalDocumentPageReq) Reset() {
	*x = ExternalDocumentPageReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocumentPageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocumentPageReq) ProtoMessage() {}

func (x *ExternalDocumentPageReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocumentPageReq.ProtoReflect.Descriptor instead.
func (*ExternalDocumentPageReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{32}
}

func (x *ExternalDocumentPageReq) GetPageInfo() *PageInfo {
	if x != nil {
		return x.PageInfo
	}
	return nil
}

func (x *ExternalDocumentPageReq) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *ExternalDocumentPageReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExternalDocumentPageReq) GetOriginalNumber() string {
	if x != nil {
		return x.OriginalNumber
	}
	return ""
}

func (x *ExternalDocumentPageReq) GetOriginalDocNumber() string {
	if x != nil {
		return x.OriginalDocNumber
	}
	return ""
}

func (x *ExternalDocumentPageReq) GetPublishDocNumber() string {
	if x != nil {
		return x.PublishDocNumber
	}
	return ""
}

func (x *ExternalDocumentPageReq) GetPublishDepartment() string {
	if x != nil {
		return x.PublishDepartment
	}
	return ""
}

func (x *ExternalDocumentPageReq) GetTypeDictionaryNodeIds() []string {
	if x != nil {
		return x.TypeDictionaryNodeIds
	}
	return nil
}

func (x *ExternalDocumentPageReq) GetDomainDictionaryNodeId() string {
	if x != nil {
		return x.DomainDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentPageReq) GetAuthenticationDictionaryNodeId() string {
	if x != nil {
		return x.AuthenticationDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentPageReq) GetBeAttachedFile() string {
	if x != nil {
		return x.BeAttachedFile
	}
	return ""
}

func (x *ExternalDocumentPageReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ExternalDocumentPageReq) GetOrgType() int32 {
	if x != nil {
		return x.OrgType
	}
	return 0
}

func (x *ExternalDocumentPageReq) GetOrgId() string {
	if x != nil {
		return x.OrgId
	}
	return ""
}

type ExternalDocumentPageResp struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Data          []*ExternalDocumentPageInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Total         int64                       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExternalDocumentPageResp) Reset() {
	*x = ExternalDocumentPageResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocumentPageResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocumentPageResp) ProtoMessage() {}

func (x *ExternalDocumentPageResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocumentPageResp.ProtoReflect.Descriptor instead.
func (*ExternalDocumentPageResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{33}
}

func (x *ExternalDocumentPageResp) GetData() []*ExternalDocumentPageInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ExternalDocumentPageResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ExternalDocumentPageInfo struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	Id                              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Number                          string                 `protobuf:"bytes,2,opt,name=number,proto3" json:"number,omitempty"`
	Version                         string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	OriginalNumber                  string                 `protobuf:"bytes,4,opt,name=original_number,json=originalNumber,proto3" json:"original_number,omitempty"`
	OriginalVersion                 string                 `protobuf:"bytes,5,opt,name=original_version,json=originalVersion,proto3" json:"original_version,omitempty"`
	Name                            string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	DocType                         string                 `protobuf:"bytes,7,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	Domain                          string                 `protobuf:"bytes,8,opt,name=domain,proto3" json:"domain,omitempty"`
	OriginalDocNumber               string                 `protobuf:"bytes,9,opt,name=original_doc_number,json=originalDocNumber,proto3" json:"original_doc_number,omitempty"`
	PublishDocNumber                string                 `protobuf:"bytes,10,opt,name=publish_doc_number,json=publishDocNumber,proto3" json:"publish_doc_number,omitempty"`
	PublishDepartment               string                 `protobuf:"bytes,11,opt,name=publish_department,json=publishDepartment,proto3" json:"publish_department,omitempty"`
	ApprovalInfo                    *ApprovalInfo          `protobuf:"bytes,12,opt,name=approval_info,json=approvalInfo,proto3" json:"approval_info,omitempty"`
	PublishDate                     int64                  `protobuf:"varint,13,opt,name=publish_date,json=publishDate,proto3" json:"publish_date,omitempty"`
	EffectiveDate                   int64                  `protobuf:"varint,14,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
	Authentication                  string                 `protobuf:"bytes,15,opt,name=Authentication,proto3" json:"Authentication,omitempty"`
	Status                          int32                  `protobuf:"varint,16,opt,name=status,proto3" json:"status,omitempty"`
	TypeDictionaryNodeId            string                 `protobuf:"bytes,17,opt,name=type_dictionary_node_id,json=typeDictionaryNodeId,proto3" json:"type_dictionary_node_id,omitempty"`
	DomainDictionaryNodeId          string                 `protobuf:"bytes,18,opt,name=domain_dictionary_node_id,json=domainDictionaryNodeId,proto3" json:"domain_dictionary_node_id,omitempty"`
	AuthenticationDictionaryNodeIds []string               `protobuf:"bytes,19,rep,name=authentication_dictionary_node_ids,json=authenticationDictionaryNodeIds,proto3" json:"authentication_dictionary_node_ids,omitempty"`
	FileId                          string                 `protobuf:"bytes,20,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *ExternalDocumentPageInfo) Reset() {
	*x = ExternalDocumentPageInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocumentPageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocumentPageInfo) ProtoMessage() {}

func (x *ExternalDocumentPageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocumentPageInfo.ProtoReflect.Descriptor instead.
func (*ExternalDocumentPageInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{34}
}

func (x *ExternalDocumentPageInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetOriginalNumber() string {
	if x != nil {
		return x.OriginalNumber
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetOriginalVersion() string {
	if x != nil {
		return x.OriginalVersion
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetDocType() string {
	if x != nil {
		return x.DocType
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetOriginalDocNumber() string {
	if x != nil {
		return x.OriginalDocNumber
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetPublishDocNumber() string {
	if x != nil {
		return x.PublishDocNumber
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetPublishDepartment() string {
	if x != nil {
		return x.PublishDepartment
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetApprovalInfo() *ApprovalInfo {
	if x != nil {
		return x.ApprovalInfo
	}
	return nil
}

func (x *ExternalDocumentPageInfo) GetPublishDate() int64 {
	if x != nil {
		return x.PublishDate
	}
	return 0
}

func (x *ExternalDocumentPageInfo) GetEffectiveDate() int64 {
	if x != nil {
		return x.EffectiveDate
	}
	return 0
}

func (x *ExternalDocumentPageInfo) GetAuthentication() string {
	if x != nil {
		return x.Authentication
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ExternalDocumentPageInfo) GetTypeDictionaryNodeId() string {
	if x != nil {
		return x.TypeDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetDomainDictionaryNodeId() string {
	if x != nil {
		return x.DomainDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentPageInfo) GetAuthenticationDictionaryNodeIds() []string {
	if x != nil {
		return x.AuthenticationDictionaryNodeIds
	}
	return nil
}

func (x *ExternalDocumentPageInfo) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

type ExternalDocumentChangeReq struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	Id                              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                            string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	OriginalDocNumber               string                 `protobuf:"bytes,3,opt,name=original_doc_number,json=originalDocNumber,proto3" json:"original_doc_number,omitempty"`
	PublishDocNumber                string                 `protobuf:"bytes,4,opt,name=publish_doc_number,json=publishDocNumber,proto3" json:"publish_doc_number,omitempty"`
	PublishDepartment               string                 `protobuf:"bytes,5,opt,name=publish_department,json=publishDepartment,proto3" json:"publish_department,omitempty"`
	TypeDictionaryNodeId            string                 `protobuf:"bytes,6,opt,name=type_dictionary_node_id,json=typeDictionaryNodeId,proto3" json:"type_dictionary_node_id,omitempty"`
	DomainDictionaryNodeId          string                 `protobuf:"bytes,7,opt,name=domain_dictionary_node_id,json=domainDictionaryNodeId,proto3" json:"domain_dictionary_node_id,omitempty"`
	AuthenticationDictionaryNodeIds []string               `protobuf:"bytes,8,rep,name=authentication_dictionary_node_ids,json=authenticationDictionaryNodeIds,proto3" json:"authentication_dictionary_node_ids,omitempty"`
	PublishDate                     int64                  `protobuf:"varint,9,opt,name=publish_date,json=publishDate,proto3" json:"publish_date,omitempty"`
	EffectiveDate                   int64                  `protobuf:"varint,10,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
	OriginalNumber                  string                 `protobuf:"bytes,11,opt,name=original_number,json=originalNumber,proto3" json:"original_number,omitempty"`
	OriginalVersion                 string                 `protobuf:"bytes,12,opt,name=original_version,json=originalVersion,proto3" json:"original_version,omitempty"`
	FileId                          string                 `protobuf:"bytes,13,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	NumberPrefix                    string                 `protobuf:"bytes,14,opt,name=number_prefix,json=numberPrefix,proto3" json:"number_prefix,omitempty"`
	DocType                         string                 `protobuf:"bytes,15,opt,name=doc_type,json=docType,proto3" json:"doc_type,omitempty"`
	Authentications                 []string               `protobuf:"bytes,16,rep,name=authentications,proto3" json:"authentications,omitempty"`
	Domain                          string                 `protobuf:"bytes,17,opt,name=domain,proto3" json:"domain,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *ExternalDocumentChangeReq) Reset() {
	*x = ExternalDocumentChangeReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalDocumentChangeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalDocumentChangeReq) ProtoMessage() {}

func (x *ExternalDocumentChangeReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalDocumentChangeReq.ProtoReflect.Descriptor instead.
func (*ExternalDocumentChangeReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{35}
}

func (x *ExternalDocumentChangeReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetOriginalDocNumber() string {
	if x != nil {
		return x.OriginalDocNumber
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetPublishDocNumber() string {
	if x != nil {
		return x.PublishDocNumber
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetPublishDepartment() string {
	if x != nil {
		return x.PublishDepartment
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetTypeDictionaryNodeId() string {
	if x != nil {
		return x.TypeDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetDomainDictionaryNodeId() string {
	if x != nil {
		return x.DomainDictionaryNodeId
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetAuthenticationDictionaryNodeIds() []string {
	if x != nil {
		return x.AuthenticationDictionaryNodeIds
	}
	return nil
}

func (x *ExternalDocumentChangeReq) GetPublishDate() int64 {
	if x != nil {
		return x.PublishDate
	}
	return 0
}

func (x *ExternalDocumentChangeReq) GetEffectiveDate() int64 {
	if x != nil {
		return x.EffectiveDate
	}
	return 0
}

func (x *ExternalDocumentChangeReq) GetOriginalNumber() string {
	if x != nil {
		return x.OriginalNumber
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetOriginalVersion() string {
	if x != nil {
		return x.OriginalVersion
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetNumberPrefix() string {
	if x != nil {
		return x.NumberPrefix
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetDocType() string {
	if x != nil {
		return x.DocType
	}
	return ""
}

func (x *ExternalDocumentChangeReq) GetAuthentications() []string {
	if x != nil {
		return x.Authentications
	}
	return nil
}

func (x *ExternalDocumentChangeReq) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type ImportGroupDocsToCompanyReq struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	Data          []*ImportGroupDocsToCompanyInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	OrgCode       string                          `protobuf:"bytes,2,opt,name=org_code,json=orgCode,proto3" json:"org_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportGroupDocsToCompanyReq) Reset() {
	*x = ImportGroupDocsToCompanyReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportGroupDocsToCompanyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportGroupDocsToCompanyReq) ProtoMessage() {}

func (x *ImportGroupDocsToCompanyReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportGroupDocsToCompanyReq.ProtoReflect.Descriptor instead.
func (*ImportGroupDocsToCompanyReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{36}
}

func (x *ImportGroupDocsToCompanyReq) GetData() []*ImportGroupDocsToCompanyInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ImportGroupDocsToCompanyReq) GetOrgCode() string {
	if x != nil {
		return x.OrgCode
	}
	return ""
}

type ImportGroupDocsToCompanyInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OriginalNumber  string                 `protobuf:"bytes,2,opt,name=original_number,json=originalNumber,proto3" json:"original_number,omitempty"`
	OriginalVersion string                 `protobuf:"bytes,3,opt,name=original_version,json=originalVersion,proto3" json:"original_version,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ImportGroupDocsToCompanyInfo) Reset() {
	*x = ImportGroupDocsToCompanyInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportGroupDocsToCompanyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportGroupDocsToCompanyInfo) ProtoMessage() {}

func (x *ImportGroupDocsToCompanyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportGroupDocsToCompanyInfo.ProtoReflect.Descriptor instead.
func (*ImportGroupDocsToCompanyInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{37}
}

func (x *ImportGroupDocsToCompanyInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ImportGroupDocsToCompanyInfo) GetOriginalNumber() string {
	if x != nil {
		return x.OriginalNumber
	}
	return ""
}

func (x *ImportGroupDocsToCompanyInfo) GetOriginalVersion() string {
	if x != nil {
		return x.OriginalVersion
	}
	return ""
}

type ImportGroupDocsToCompanyResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportGroupDocsToCompanyResp) Reset() {
	*x = ImportGroupDocsToCompanyResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportGroupDocsToCompanyResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportGroupDocsToCompanyResp) ProtoMessage() {}

func (x *ImportGroupDocsToCompanyResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportGroupDocsToCompanyResp.ProtoReflect.Descriptor instead.
func (*ImportGroupDocsToCompanyResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{38}
}

type PlagiarismCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlagiarismCheckReq) Reset() {
	*x = PlagiarismCheckReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlagiarismCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlagiarismCheckReq) ProtoMessage() {}

func (x *PlagiarismCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlagiarismCheckReq.ProtoReflect.Descriptor instead.
func (*PlagiarismCheckReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{39}
}

func (x *PlagiarismCheckReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type DocumentDistributeReq struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                              // 文档id
	Applicant          string                 `protobuf:"bytes,2,opt,name=applicant,proto3" json:"applicant,omitempty"`                                                // 申请人
	ApplyDate          int64                  `protobuf:"varint,3,opt,name=apply_date,json=applyDate,proto3" json:"apply_date,omitempty"`                              // 申请日期(时间戳)
	DistributeType     int32                  `protobuf:"varint,4,opt,name=distribute_type,json=distributeType,proto3" json:"distribute_type,omitempty"`               // 分发类型
	FileType           int32                  `protobuf:"varint,5,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`                                 // 文件类型
	TypeDictNodeId     string                 `protobuf:"bytes,6,opt,name=type_dict_node_id,json=typeDictNodeId,proto3" json:"type_dict_node_id,omitempty"`            // 类型字典节点ID
	Reason             string                 `protobuf:"bytes,7,opt,name=reason,proto3" json:"reason,omitempty"`                                                      // 原因
	OtherReason        string                 `protobuf:"bytes,8,opt,name=other_reason,json=otherReason,proto3" json:"other_reason,omitempty"`                         // 其他原因(当reason_dict_node_id为"其他"时使用)
	WishDistributeDate int64                  `protobuf:"varint,9,opt,name=wish_distribute_date,json=wishDistributeDate,proto3" json:"wish_distribute_date,omitempty"` // 希望分发日期(时间戳)
	DistributeList     []*DistributeList      `protobuf:"bytes,10,rep,name=distribute_list,json=distributeList,proto3" json:"distribute_list,omitempty"`               // 发放清单
	FileCategory       string                 `protobuf:"bytes,11,opt,name=fileCategory,proto3" json:"fileCategory,omitempty"`                                         // 文件类别
	WorkflowId         string                 `protobuf:"bytes,12,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`                           // 流程id
	SaveMethod         int32                  `protobuf:"varint,13,opt,name=save_method,json=saveMethod,proto3" json:"save_method,omitempty"`                          // 保存方式
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *DocumentDistributeReq) Reset() {
	*x = DocumentDistributeReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocumentDistributeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentDistributeReq) ProtoMessage() {}

func (x *DocumentDistributeReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentDistributeReq.ProtoReflect.Descriptor instead.
func (*DocumentDistributeReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{40}
}

func (x *DocumentDistributeReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DocumentDistributeReq) GetApplicant() string {
	if x != nil {
		return x.Applicant
	}
	return ""
}

func (x *DocumentDistributeReq) GetApplyDate() int64 {
	if x != nil {
		return x.ApplyDate
	}
	return 0
}

func (x *DocumentDistributeReq) GetDistributeType() int32 {
	if x != nil {
		return x.DistributeType
	}
	return 0
}

func (x *DocumentDistributeReq) GetFileType() int32 {
	if x != nil {
		return x.FileType
	}
	return 0
}

func (x *DocumentDistributeReq) GetTypeDictNodeId() string {
	if x != nil {
		return x.TypeDictNodeId
	}
	return ""
}

func (x *DocumentDistributeReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *DocumentDistributeReq) GetOtherReason() string {
	if x != nil {
		return x.OtherReason
	}
	return ""
}

func (x *DocumentDistributeReq) GetWishDistributeDate() int64 {
	if x != nil {
		return x.WishDistributeDate
	}
	return 0
}

func (x *DocumentDistributeReq) GetDistributeList() []*DistributeList {
	if x != nil {
		return x.DistributeList
	}
	return nil
}

func (x *DocumentDistributeReq) GetFileCategory() string {
	if x != nil {
		return x.FileCategory
	}
	return ""
}

func (x *DocumentDistributeReq) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *DocumentDistributeReq) GetSaveMethod() int32 {
	if x != nil {
		return x.SaveMethod
	}
	return 0
}

type DistributeList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileId        string                 `protobuf:"bytes,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`       // 文件id
	FileName      string                 `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"` // 文件名
	Number        string                 `protobuf:"bytes,3,opt,name=number,proto3" json:"number,omitempty"`                     // 文件编号
	Version       string                 `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`                   // 文件版本
	Permissions   []*Permission          `protobuf:"bytes,5,rep,name=permissions,proto3" json:"permissions,omitempty"`           // 权限列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DistributeList) Reset() {
	*x = DistributeList{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DistributeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributeList) ProtoMessage() {}

func (x *DistributeList) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributeList.ProtoReflect.Descriptor instead.
func (*DistributeList) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{41}
}

func (x *DistributeList) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *DistributeList) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *DistributeList) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *DistributeList) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DistributeList) GetPermissions() []*Permission {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type Permission struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FileForm       int32                  `protobuf:"varint,1,opt,name=file_form,json=fileForm,proto3" json:"file_form,omitempty"`                   // 文件形式
	FilePermission int32                  `protobuf:"varint,2,opt,name=file_permission,json=filePermission,proto3" json:"file_permission,omitempty"` // 文件权限
	Recipient      string                 `protobuf:"bytes,3,opt,name=recipient,proto3" json:"recipient,omitempty"`                                  // 接收方
	ReceivedBy     []*Recipient           `protobuf:"bytes,4,rep,name=received_by,json=receivedBy,proto3" json:"received_by,omitempty"`              // 接收人
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Permission) Reset() {
	*x = Permission{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Permission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Permission) ProtoMessage() {}

func (x *Permission) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Permission.ProtoReflect.Descriptor instead.
func (*Permission) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{42}
}

func (x *Permission) GetFileForm() int32 {
	if x != nil {
		return x.FileForm
	}
	return 0
}

func (x *Permission) GetFilePermission() int32 {
	if x != nil {
		return x.FilePermission
	}
	return 0
}

func (x *Permission) GetRecipient() string {
	if x != nil {
		return x.Recipient
	}
	return ""
}

func (x *Permission) GetReceivedBy() []*Recipient {
	if x != nil {
		return x.ReceivedBy
	}
	return nil
}

type Recipient struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`       // 用户ID
	UserName      string                 `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"` // 用户名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Recipient) Reset() {
	*x = Recipient{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Recipient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Recipient) ProtoMessage() {}

func (x *Recipient) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Recipient.ProtoReflect.Descriptor instead.
func (*Recipient) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{43}
}

func (x *Recipient) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Recipient) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

type GetDocPermissionUsersReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FileId         string                 `protobuf:"bytes,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	FileFrom       int32                  `protobuf:"varint,2,opt,name=file_from,json=fileFrom,proto3" json:"file_from,omitempty"`
	FilePermission int32                  `protobuf:"varint,3,opt,name=file_permission,json=filePermission,proto3" json:"file_permission,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetDocPermissionUsersReq) Reset() {
	*x = GetDocPermissionUsersReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDocPermissionUsersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDocPermissionUsersReq) ProtoMessage() {}

func (x *GetDocPermissionUsersReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDocPermissionUsersReq.ProtoReflect.Descriptor instead.
func (*GetDocPermissionUsersReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{44}
}

func (x *GetDocPermissionUsersReq) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *GetDocPermissionUsersReq) GetFileFrom() int32 {
	if x != nil {
		return x.FileFrom
	}
	return 0
}

func (x *GetDocPermissionUsersReq) GetFilePermission() int32 {
	if x != nil {
		return x.FilePermission
	}
	return 0
}

type GetDocPermissionUsersResp struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	WaitForApprovalUsers []string               `protobuf:"bytes,1,rep,name=wait_for_approval_users,json=waitForApprovalUsers,proto3" json:"wait_for_approval_users,omitempty"` // 待审批用户
	NotRecycledUsers     []string               `protobuf:"bytes,2,rep,name=not_recycled_users,json=notRecycledUsers,proto3" json:"not_recycled_users,omitempty"`               // 未回收用户
	RecycleFlowUsers     []string               `protobuf:"bytes,3,rep,name=recycle_flow_users,json=recycleFlowUsers,proto3" json:"recycle_flow_users,omitempty"`               // 回收流程中用户
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *GetDocPermissionUsersResp) Reset() {
	*x = GetDocPermissionUsersResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDocPermissionUsersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDocPermissionUsersResp) ProtoMessage() {}

func (x *GetDocPermissionUsersResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDocPermissionUsersResp.ProtoReflect.Descriptor instead.
func (*GetDocPermissionUsersResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{45}
}

func (x *GetDocPermissionUsersResp) GetWaitForApprovalUsers() []string {
	if x != nil {
		return x.WaitForApprovalUsers
	}
	return nil
}

func (x *GetDocPermissionUsersResp) GetNotRecycledUsers() []string {
	if x != nil {
		return x.NotRecycledUsers
	}
	return nil
}

func (x *GetDocPermissionUsersResp) GetRecycleFlowUsers() []string {
	if x != nil {
		return x.RecycleFlowUsers
	}
	return nil
}

type UpdateDistributeStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WorkflowId    string                 `protobuf:"bytes,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	Status        int32                  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDistributeStatusReq) Reset() {
	*x = UpdateDistributeStatusReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDistributeStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDistributeStatusReq) ProtoMessage() {}

func (x *UpdateDistributeStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDistributeStatusReq.ProtoReflect.Descriptor instead.
func (*UpdateDistributeStatusReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{46}
}

func (x *UpdateDistributeStatusReq) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *UpdateDistributeStatusReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type RecycleApprovalInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DistributeId  string                 `protobuf:"bytes,1,opt,name=distribute_id,json=distributeId,proto3" json:"distribute_id,omitempty"`    // 发放信息id
	RecycleDate   int64                  `protobuf:"varint,2,opt,name=recycle_date,json=recycleDate,proto3" json:"recycle_date,omitempty"`      // 回收日期
	RecycleReason string                 `protobuf:"bytes,3,opt,name=recycle_reason,json=recycleReason,proto3" json:"recycle_reason,omitempty"` // 回收原因
	OtherReason   string                 `protobuf:"bytes,4,opt,name=other_reason,json=otherReason,proto3" json:"other_reason,omitempty"`       // 其他原因
	RecycleList   []*RecycleList         `protobuf:"bytes,5,rep,name=recycle_list,json=recycleList,proto3" json:"recycle_list,omitempty"`       // 回收清单
	WorkflowId    string                 `protobuf:"bytes,6,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`          // 流程id
	ApprovalInfo  *ApprovalInfo          `protobuf:"bytes,7,opt,name=approval_info,json=approvalInfo,proto3" json:"approval_info,omitempty"`    // 审批信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecycleApprovalInfo) Reset() {
	*x = RecycleApprovalInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecycleApprovalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecycleApprovalInfo) ProtoMessage() {}

func (x *RecycleApprovalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecycleApprovalInfo.ProtoReflect.Descriptor instead.
func (*RecycleApprovalInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{47}
}

func (x *RecycleApprovalInfo) GetDistributeId() string {
	if x != nil {
		return x.DistributeId
	}
	return ""
}

func (x *RecycleApprovalInfo) GetRecycleDate() int64 {
	if x != nil {
		return x.RecycleDate
	}
	return 0
}

func (x *RecycleApprovalInfo) GetRecycleReason() string {
	if x != nil {
		return x.RecycleReason
	}
	return ""
}

func (x *RecycleApprovalInfo) GetOtherReason() string {
	if x != nil {
		return x.OtherReason
	}
	return ""
}

func (x *RecycleApprovalInfo) GetRecycleList() []*RecycleList {
	if x != nil {
		return x.RecycleList
	}
	return nil
}

func (x *RecycleApprovalInfo) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *RecycleApprovalInfo) GetApprovalInfo() *ApprovalInfo {
	if x != nil {
		return x.ApprovalInfo
	}
	return nil
}

type RecycleList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InventoryId   string                 `protobuf:"bytes,1,opt,name=inventory_id,json=inventoryId,proto3" json:"inventory_id,omitempty"` // 清单列表ID
	Permissions   []*FilePermission      `protobuf:"bytes,2,rep,name=permissions,proto3" json:"permissions,omitempty"`                    // 文件权限
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecycleList) Reset() {
	*x = RecycleList{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecycleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecycleList) ProtoMessage() {}

func (x *RecycleList) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecycleList.ProtoReflect.Descriptor instead.
func (*RecycleList) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{48}
}

func (x *RecycleList) GetInventoryId() string {
	if x != nil {
		return x.InventoryId
	}
	return ""
}

func (x *RecycleList) GetPermissions() []*FilePermission {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type FilePermission struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FileForm       int32                  `protobuf:"varint,1,opt,name=file_form,json=fileForm,proto3" json:"file_form,omitempty"`                   // 文件形式
	FilePermission int32                  `protobuf:"varint,2,opt,name=file_permission,json=filePermission,proto3" json:"file_permission,omitempty"` // 文件权限
	ReceivedBy     []string               `protobuf:"bytes,3,rep,name=received_by,json=receivedBy,proto3" json:"received_by,omitempty"`              // 接收人
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *FilePermission) Reset() {
	*x = FilePermission{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilePermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilePermission) ProtoMessage() {}

func (x *FilePermission) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilePermission.ProtoReflect.Descriptor instead.
func (*FilePermission) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{49}
}

func (x *FilePermission) GetFileForm() int32 {
	if x != nil {
		return x.FileForm
	}
	return 0
}

func (x *FilePermission) GetFilePermission() int32 {
	if x != nil {
		return x.FilePermission
	}
	return 0
}

func (x *FilePermission) GetReceivedBy() []string {
	if x != nil {
		return x.ReceivedBy
	}
	return nil
}

type DisposalApprovalInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	DistributeId   string                 `protobuf:"bytes,1,opt,name=distribute_id,json=distributeId,proto3" json:"distribute_id,omitempty"`       // 发放信息id
	DisposalDate   int64                  `protobuf:"varint,2,opt,name=disposal_date,json=disposalDate,proto3" json:"disposal_date,omitempty"`      // 处置日期
	DisposalReason string                 `protobuf:"bytes,3,opt,name=disposal_reason,json=disposalReason,proto3" json:"disposal_reason,omitempty"` // 处置方式
	DisposalList   []*DisposalList        `protobuf:"bytes,4,rep,name=disposal_list,json=disposalList,proto3" json:"disposal_list,omitempty"`       // 处置清单
	WorkflowId     string                 `protobuf:"bytes,5,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`             // 流程id
	ApprovalInfo   *ApprovalInfo          `protobuf:"bytes,6,opt,name=approval_info,json=approvalInfo,proto3" json:"approval_info,omitempty"`       // 审批信息
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DisposalApprovalInfo) Reset() {
	*x = DisposalApprovalInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisposalApprovalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisposalApprovalInfo) ProtoMessage() {}

func (x *DisposalApprovalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisposalApprovalInfo.ProtoReflect.Descriptor instead.
func (*DisposalApprovalInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{50}
}

func (x *DisposalApprovalInfo) GetDistributeId() string {
	if x != nil {
		return x.DistributeId
	}
	return ""
}

func (x *DisposalApprovalInfo) GetDisposalDate() int64 {
	if x != nil {
		return x.DisposalDate
	}
	return 0
}

func (x *DisposalApprovalInfo) GetDisposalReason() string {
	if x != nil {
		return x.DisposalReason
	}
	return ""
}

func (x *DisposalApprovalInfo) GetDisposalList() []*DisposalList {
	if x != nil {
		return x.DisposalList
	}
	return nil
}

func (x *DisposalApprovalInfo) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *DisposalApprovalInfo) GetApprovalInfo() *ApprovalInfo {
	if x != nil {
		return x.ApprovalInfo
	}
	return nil
}

type DisposalList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InventoryId   string                 `protobuf:"bytes,1,opt,name=inventory_id,json=inventoryId,proto3" json:"inventory_id,omitempty"` // 清单列表ID
	Permissions   []*FilePermission      `protobuf:"bytes,2,rep,name=permissions,proto3" json:"permissions,omitempty"`                    // 文件权限
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DisposalList) Reset() {
	*x = DisposalList{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisposalList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisposalList) ProtoMessage() {}

func (x *DisposalList) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisposalList.ProtoReflect.Descriptor instead.
func (*DisposalList) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{51}
}

func (x *DisposalList) GetInventoryId() string {
	if x != nil {
		return x.InventoryId
	}
	return ""
}

func (x *DisposalList) GetPermissions() []*FilePermission {
	if x != nil {
		return x.Permissions
	}
	return nil
}

// 获取分发列表请求
type GetDistributeListReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	PageInfo       *PageInfo              `protobuf:"bytes,1,opt,name=page_info,json=pageInfo,proto3" json:"page_info,omitempty"`                    // 分页信息
	FileNumber     string                 `protobuf:"bytes,2,opt,name=file_number,json=fileNumber,proto3" json:"file_number,omitempty"`              // 文件编号
	FileName       string                 `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`                    // 文件名称
	FileType       int32                  `protobuf:"varint,4,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`                   // 文件类型
	FileCategory   []string               `protobuf:"bytes,5,rep,name=file_category,json=fileCategory,proto3" json:"file_category,omitempty"`        // 文件类别
	DistributeType int32                  `protobuf:"varint,6,opt,name=distribute_type,json=distributeType,proto3" json:"distribute_type,omitempty"` // 发放类型
	Status         int32                  `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`                                       // 状态（1待提交 | 2待审批 | 3已审批 | 4已驳回）
	Applicant      []string               `protobuf:"bytes,8,rep,name=applicant,proto3" json:"applicant,omitempty"`                                  // 申请人
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetDistributeListReq) Reset() {
	*x = GetDistributeListReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDistributeListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDistributeListReq) ProtoMessage() {}

func (x *GetDistributeListReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDistributeListReq.ProtoReflect.Descriptor instead.
func (*GetDistributeListReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{52}
}

func (x *GetDistributeListReq) GetPageInfo() *PageInfo {
	if x != nil {
		return x.PageInfo
	}
	return nil
}

func (x *GetDistributeListReq) GetFileNumber() string {
	if x != nil {
		return x.FileNumber
	}
	return ""
}

func (x *GetDistributeListReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *GetDistributeListReq) GetFileType() int32 {
	if x != nil {
		return x.FileType
	}
	return 0
}

func (x *GetDistributeListReq) GetFileCategory() []string {
	if x != nil {
		return x.FileCategory
	}
	return nil
}

func (x *GetDistributeListReq) GetDistributeType() int32 {
	if x != nil {
		return x.DistributeType
	}
	return 0
}

func (x *GetDistributeListReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GetDistributeListReq) GetApplicant() []string {
	if x != nil {
		return x.Applicant
	}
	return nil
}

type GetDistributeListResp struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Total         int64                    `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"` // 总记录数
	Data          []*GetDistributeListInfo `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`    // 数据列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDistributeListResp) Reset() {
	*x = GetDistributeListResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDistributeListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDistributeListResp) ProtoMessage() {}

func (x *GetDistributeListResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDistributeListResp.ProtoReflect.Descriptor instead.
func (*GetDistributeListResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{53}
}

func (x *GetDistributeListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetDistributeListResp) GetData() []*GetDistributeListInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetDistributeListInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                              // 主键id
	Applicant          string                 `protobuf:"bytes,2,opt,name=applicant,proto3" json:"applicant,omitempty"`                                                // 申请人
	ApplyDate          int64                  `protobuf:"varint,3,opt,name=apply_date,json=applyDate,proto3" json:"apply_date,omitempty"`                              // 申请时间
	DistributeType     int32                  `protobuf:"varint,4,opt,name=distribute_type,json=distributeType,proto3" json:"distribute_type,omitempty"`               // 发放类型（1内部发放 | 2外部发放）
	FileType           int32                  `protobuf:"varint,5,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`                                 // 文件类型（1内部文件 | 2外部文件）
	FileCategory       string                 `protobuf:"bytes,6,opt,name=file_category,json=fileCategory,proto3" json:"file_category,omitempty"`                      // 文件类别
	Reason             string                 `protobuf:"bytes,7,opt,name=reason,proto3" json:"reason,omitempty"`                                                      // 发放原因
	OtherReason        string                 `protobuf:"bytes,8,opt,name=other_reason,json=otherReason,proto3" json:"other_reason,omitempty"`                         // 其他原因
	WishDistributeDate int64                  `protobuf:"varint,9,opt,name=wish_distribute_date,json=wishDistributeDate,proto3" json:"wish_distribute_date,omitempty"` // 期望发放时间
	Status             int32                  `protobuf:"varint,10,opt,name=status,proto3" json:"status,omitempty"`                                                    // 状态（1待提交 | 2待审批 | 3已审批 | 4已驳回）
	ApprovalInfo       *ApprovalInfo          `protobuf:"bytes,11,opt,name=approval_info,json=approvalInfo,proto3" json:"approval_info,omitempty"`                     // 审批信息
	WorkflowId         string                 `protobuf:"bytes,12,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`                           // 流程id
	DistributeCount    int32                  `protobuf:"varint,13,opt,name=distribute_count,json=distributeCount,proto3" json:"distribute_count,omitempty"`           // 发放份数
	Received           []*DistributeUser      `protobuf:"bytes,14,rep,name=received,proto3" json:"received,omitempty"`                                                 // 已接收人
	NotReceived        []*DistributeUser      `protobuf:"bytes,15,rep,name=not_received,json=notReceived,proto3" json:"not_received,omitempty"`                        // 未接收人
	Recycle            []*DistributeUser      `protobuf:"bytes,16,rep,name=recycle,proto3" json:"recycle,omitempty"`                                                   // 已回收人
	DisposalBy         []*DistributeUser      `protobuf:"bytes,17,rep,name=disposal_by,json=disposalBy,proto3" json:"disposal_by,omitempty"`                           // 已处置人
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetDistributeListInfo) Reset() {
	*x = GetDistributeListInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDistributeListInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDistributeListInfo) ProtoMessage() {}

func (x *GetDistributeListInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDistributeListInfo.ProtoReflect.Descriptor instead.
func (*GetDistributeListInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{54}
}

func (x *GetDistributeListInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetDistributeListInfo) GetApplicant() string {
	if x != nil {
		return x.Applicant
	}
	return ""
}

func (x *GetDistributeListInfo) GetApplyDate() int64 {
	if x != nil {
		return x.ApplyDate
	}
	return 0
}

func (x *GetDistributeListInfo) GetDistributeType() int32 {
	if x != nil {
		return x.DistributeType
	}
	return 0
}

func (x *GetDistributeListInfo) GetFileType() int32 {
	if x != nil {
		return x.FileType
	}
	return 0
}

func (x *GetDistributeListInfo) GetFileCategory() string {
	if x != nil {
		return x.FileCategory
	}
	return ""
}

func (x *GetDistributeListInfo) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *GetDistributeListInfo) GetOtherReason() string {
	if x != nil {
		return x.OtherReason
	}
	return ""
}

func (x *GetDistributeListInfo) GetWishDistributeDate() int64 {
	if x != nil {
		return x.WishDistributeDate
	}
	return 0
}

func (x *GetDistributeListInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GetDistributeListInfo) GetApprovalInfo() *ApprovalInfo {
	if x != nil {
		return x.ApprovalInfo
	}
	return nil
}

func (x *GetDistributeListInfo) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *GetDistributeListInfo) GetDistributeCount() int32 {
	if x != nil {
		return x.DistributeCount
	}
	return 0
}

func (x *GetDistributeListInfo) GetReceived() []*DistributeUser {
	if x != nil {
		return x.Received
	}
	return nil
}

func (x *GetDistributeListInfo) GetNotReceived() []*DistributeUser {
	if x != nil {
		return x.NotReceived
	}
	return nil
}

func (x *GetDistributeListInfo) GetRecycle() []*DistributeUser {
	if x != nil {
		return x.Recycle
	}
	return nil
}

func (x *GetDistributeListInfo) GetDisposalBy() []*DistributeUser {
	if x != nil {
		return x.DisposalBy
	}
	return nil
}

type DistributeUser struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`        // 用户id
	FileForm      int32                  `protobuf:"varint,2,opt,name=file_form,json=fileForm,proto3" json:"file_form,omitempty"` // 文件形式（1电子文件 | 2纸质文件）
	Nickname      string                 `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`                  // 用户昵称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DistributeUser) Reset() {
	*x = DistributeUser{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DistributeUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributeUser) ProtoMessage() {}

func (x *DistributeUser) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributeUser.ProtoReflect.Descriptor instead.
func (*DistributeUser) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{55}
}

func (x *DistributeUser) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DistributeUser) GetFileForm() int32 {
	if x != nil {
		return x.FileForm
	}
	return 0
}

func (x *DistributeUser) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

type DistributeApprovalReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WorkflowId    string                 `protobuf:"bytes,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"` // 流程id
	Status        int32                  `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`                          // 审批状态
	SignForStatus int32                  `protobuf:"varint,3,opt,name=sign_for_status,json=signForStatus,proto3" json:"sign_for_status,omitempty"`
	DisposeStatus int32                  `protobuf:"varint,4,opt,name=dispose_status,json=disposeStatus,proto3" json:"dispose_status,omitempty"`
	ApprovalInfo  *ApprovalInfo          `protobuf:"bytes,5,opt,name=approval_info,json=approvalInfo,proto3" json:"approval_info,omitempty"` // 审批信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DistributeApprovalReq) Reset() {
	*x = DistributeApprovalReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DistributeApprovalReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributeApprovalReq) ProtoMessage() {}

func (x *DistributeApprovalReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributeApprovalReq.ProtoReflect.Descriptor instead.
func (*DistributeApprovalReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{56}
}

func (x *DistributeApprovalReq) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *DistributeApprovalReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DistributeApprovalReq) GetSignForStatus() int32 {
	if x != nil {
		return x.SignForStatus
	}
	return 0
}

func (x *DistributeApprovalReq) GetDisposeStatus() int32 {
	if x != nil {
		return x.DisposeStatus
	}
	return 0
}

func (x *DistributeApprovalReq) GetApprovalInfo() *ApprovalInfo {
	if x != nil {
		return x.ApprovalInfo
	}
	return nil
}

// 借阅文档项
type BorrowDocumentItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DocumentId    string                 `protobuf:"bytes,1,opt,name=document_id,json=documentId,proto3" json:"document_id,omitempty"`            // 文档ID
	VersionNo     string                 `protobuf:"bytes,2,opt,name=version_no,json=versionNo,proto3" json:"version_no,omitempty"`               // 文档版本号
	ModuleType    int32                  `protobuf:"varint,3,opt,name=module_type,json=moduleType,proto3" json:"module_type,omitempty"`           // 文档所属模块，1书籍 | 2内部文档 | 3外部文档
	BorrowStatus  int32                  `protobuf:"varint,4,opt,name=borrow_status,json=borrowStatus,proto3" json:"borrow_status,omitempty"`     // 文档借阅状态，1借阅中 | 2回收中 | 3已回收
	RecoverUserId string                 `protobuf:"bytes,5,opt,name=recover_user_id,json=recoverUserId,proto3" json:"recover_user_id,omitempty"` // 回收人ID
	RecoverTime   int64                  `protobuf:"varint,6,opt,name=recover_time,json=recoverTime,proto3" json:"recover_time,omitempty"`        // 回收时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BorrowDocumentItem) Reset() {
	*x = BorrowDocumentItem{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BorrowDocumentItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BorrowDocumentItem) ProtoMessage() {}

func (x *BorrowDocumentItem) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BorrowDocumentItem.ProtoReflect.Descriptor instead.
func (*BorrowDocumentItem) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{57}
}

func (x *BorrowDocumentItem) GetDocumentId() string {
	if x != nil {
		return x.DocumentId
	}
	return ""
}

func (x *BorrowDocumentItem) GetVersionNo() string {
	if x != nil {
		return x.VersionNo
	}
	return ""
}

func (x *BorrowDocumentItem) GetModuleType() int32 {
	if x != nil {
		return x.ModuleType
	}
	return 0
}

func (x *BorrowDocumentItem) GetBorrowStatus() int32 {
	if x != nil {
		return x.BorrowStatus
	}
	return 0
}

func (x *BorrowDocumentItem) GetRecoverUserId() string {
	if x != nil {
		return x.RecoverUserId
	}
	return ""
}

func (x *BorrowDocumentItem) GetRecoverTime() int64 {
	if x != nil {
		return x.RecoverTime
	}
	return 0
}

// 创建借阅记录请求
type BorrowRecordCreateReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	UserId            string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                      // 借阅用户ID（必传）
	Documents         []*BorrowDocumentItem  `protobuf:"bytes,2,rep,name=documents,proto3" json:"documents,omitempty"`                                              // 借阅的文档列表（必传）
	DueTime           int64                  `protobuf:"varint,3,opt,name=due_time,json=dueTime,proto3" json:"due_time,omitempty"`                                  // 应还时间（必传）
	BorrowReasonType  int32                  `protobuf:"varint,4,opt,name=borrow_reason_type,json=borrowReasonType,proto3" json:"borrow_reason_type,omitempty"`     // 借阅原因类型（必传）1项目参考/研究 | 2问题调查/分析 | 3审计/检查准备 | 4培训/学习需要 | 5其他
	BorrowOtherReason string                 `protobuf:"bytes,5,opt,name=borrow_other_reason,json=borrowOtherReason,proto3" json:"borrow_other_reason,omitempty"`   // 借阅原因描述（选择"其他"时必传，其他情况可选）
	BorrowTime        int64                  `protobuf:"varint,6,opt,name=borrow_time,json=borrowTime,proto3" json:"borrow_time,omitempty"`                         // 借阅时间（可选，系统自动生成）
	BorrowApplyTime   int64                  `protobuf:"varint,7,opt,name=borrow_apply_time,json=borrowApplyTime,proto3" json:"borrow_apply_time,omitempty"`        // 申请时间（可选，系统自动生成）
	ApprovalStatus    int32                  `protobuf:"varint,8,opt,name=approval_status,json=approvalStatus,proto3" json:"approval_status,omitempty"`             // 审批状态（可选，系统自动设置）
	ApprovalInfo      *ApprovalInfo          `protobuf:"bytes,9,opt,name=approval_info,json=approvalInfo,proto3" json:"approval_info,omitempty"`                    // 审批信息（可选）
	ApprovalApplyTime int64                  `protobuf:"varint,10,opt,name=approval_apply_time,json=approvalApplyTime,proto3" json:"approval_apply_time,omitempty"` // 审批申请时间（可选，系统自动生成）
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BorrowRecordCreateReq) Reset() {
	*x = BorrowRecordCreateReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BorrowRecordCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BorrowRecordCreateReq) ProtoMessage() {}

func (x *BorrowRecordCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BorrowRecordCreateReq.ProtoReflect.Descriptor instead.
func (*BorrowRecordCreateReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{58}
}

func (x *BorrowRecordCreateReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BorrowRecordCreateReq) GetDocuments() []*BorrowDocumentItem {
	if x != nil {
		return x.Documents
	}
	return nil
}

func (x *BorrowRecordCreateReq) GetDueTime() int64 {
	if x != nil {
		return x.DueTime
	}
	return 0
}

func (x *BorrowRecordCreateReq) GetBorrowReasonType() int32 {
	if x != nil {
		return x.BorrowReasonType
	}
	return 0
}

func (x *BorrowRecordCreateReq) GetBorrowOtherReason() string {
	if x != nil {
		return x.BorrowOtherReason
	}
	return ""
}

func (x *BorrowRecordCreateReq) GetBorrowTime() int64 {
	if x != nil {
		return x.BorrowTime
	}
	return 0
}

func (x *BorrowRecordCreateReq) GetBorrowApplyTime() int64 {
	if x != nil {
		return x.BorrowApplyTime
	}
	return 0
}

func (x *BorrowRecordCreateReq) GetApprovalStatus() int32 {
	if x != nil {
		return x.ApprovalStatus
	}
	return 0
}

func (x *BorrowRecordCreateReq) GetApprovalInfo() *ApprovalInfo {
	if x != nil {
		return x.ApprovalInfo
	}
	return nil
}

func (x *BorrowRecordCreateReq) GetApprovalApplyTime() int64 {
	if x != nil {
		return x.ApprovalApplyTime
	}
	return 0
}

// 创建借阅记录响应
type BorrowRecordCreateResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // 借阅记录ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BorrowRecordCreateResp) Reset() {
	*x = BorrowRecordCreateResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BorrowRecordCreateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BorrowRecordCreateResp) ProtoMessage() {}

func (x *BorrowRecordCreateResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BorrowRecordCreateResp.ProtoReflect.Descriptor instead.
func (*BorrowRecordCreateResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{59}
}

func (x *BorrowRecordCreateResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// 修改借阅记录请求
type BorrowRecordModifyReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	BorrowRecordId    string                 `protobuf:"bytes,1,opt,name=borrow_record_id,json=borrowRecordId,proto3" json:"borrow_record_id,omitempty"`            // 借阅记录ID（必传）
	UserId            string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                      // 借阅用户ID（可选）
	Documents         []*BorrowDocumentItem  `protobuf:"bytes,3,rep,name=documents,proto3" json:"documents,omitempty"`                                              // 借阅的文档列表（可选）
	BorrowTime        int64                  `protobuf:"varint,4,opt,name=borrow_time,json=borrowTime,proto3" json:"borrow_time,omitempty"`                         // 借阅时间（可选）
	DueTime           int64                  `protobuf:"varint,5,opt,name=due_time,json=dueTime,proto3" json:"due_time,omitempty"`                                  // 应还时间（可选）
	BorrowReasonType  int32                  `protobuf:"varint,6,opt,name=borrow_reason_type,json=borrowReasonType,proto3" json:"borrow_reason_type,omitempty"`     // 借阅原因类型（可选）1项目参考/研究 | 2问题调查/分析 | 3审计/检查准备 | 4培训/学习需要 | 5其他
	BorrowOtherReason string                 `protobuf:"bytes,7,opt,name=borrow_other_reason,json=borrowOtherReason,proto3" json:"borrow_other_reason,omitempty"`   // 借阅原因描述（选择"其他"时必传，其他情况可选）
	BorrowApplyTime   int64                  `protobuf:"varint,8,opt,name=borrow_apply_time,json=borrowApplyTime,proto3" json:"borrow_apply_time,omitempty"`        // 申请时间（可选）
	ApprovalStatus    int32                  `protobuf:"varint,9,opt,name=approval_status,json=approvalStatus,proto3" json:"approval_status,omitempty"`             // 审批状态（可选）
	ApprovalInfo      *ApprovalInfo          `protobuf:"bytes,10,opt,name=approval_info,json=approvalInfo,proto3" json:"approval_info,omitempty"`                   // 审批信息（可选）
	ApprovalApplyTime int64                  `protobuf:"varint,11,opt,name=approval_apply_time,json=approvalApplyTime,proto3" json:"approval_apply_time,omitempty"` // 审批申请时间（可选）
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BorrowRecordModifyReq) Reset() {
	*x = BorrowRecordModifyReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BorrowRecordModifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BorrowRecordModifyReq) ProtoMessage() {}

func (x *BorrowRecordModifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BorrowRecordModifyReq.ProtoReflect.Descriptor instead.
func (*BorrowRecordModifyReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{60}
}

func (x *BorrowRecordModifyReq) GetBorrowRecordId() string {
	if x != nil {
		return x.BorrowRecordId
	}
	return ""
}

func (x *BorrowRecordModifyReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BorrowRecordModifyReq) GetDocuments() []*BorrowDocumentItem {
	if x != nil {
		return x.Documents
	}
	return nil
}

func (x *BorrowRecordModifyReq) GetBorrowTime() int64 {
	if x != nil {
		return x.BorrowTime
	}
	return 0
}

func (x *BorrowRecordModifyReq) GetDueTime() int64 {
	if x != nil {
		return x.DueTime
	}
	return 0
}

func (x *BorrowRecordModifyReq) GetBorrowReasonType() int32 {
	if x != nil {
		return x.BorrowReasonType
	}
	return 0
}

func (x *BorrowRecordModifyReq) GetBorrowOtherReason() string {
	if x != nil {
		return x.BorrowOtherReason
	}
	return ""
}

func (x *BorrowRecordModifyReq) GetBorrowApplyTime() int64 {
	if x != nil {
		return x.BorrowApplyTime
	}
	return 0
}

func (x *BorrowRecordModifyReq) GetApprovalStatus() int32 {
	if x != nil {
		return x.ApprovalStatus
	}
	return 0
}

func (x *BorrowRecordModifyReq) GetApprovalInfo() *ApprovalInfo {
	if x != nil {
		return x.ApprovalInfo
	}
	return nil
}

func (x *BorrowRecordModifyReq) GetApprovalApplyTime() int64 {
	if x != nil {
		return x.ApprovalApplyTime
	}
	return 0
}

// 修改借阅记录状态请求
type BorrowRecordStatusModifyReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BorrowRecordId string                 `protobuf:"bytes,1,opt,name=borrow_record_id,json=borrowRecordId,proto3" json:"borrow_record_id,omitempty"` // 借阅记录ID（必传）
	ApprovalStatus int32                  `protobuf:"varint,2,opt,name=approval_status,json=approvalStatus,proto3" json:"approval_status,omitempty"`  // 新的审批状态（必传）1待审批 | 2已审批 | 3已驳回
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BorrowRecordStatusModifyReq) Reset() {
	*x = BorrowRecordStatusModifyReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BorrowRecordStatusModifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BorrowRecordStatusModifyReq) ProtoMessage() {}

func (x *BorrowRecordStatusModifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BorrowRecordStatusModifyReq.ProtoReflect.Descriptor instead.
func (*BorrowRecordStatusModifyReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{61}
}

func (x *BorrowRecordStatusModifyReq) GetBorrowRecordId() string {
	if x != nil {
		return x.BorrowRecordId
	}
	return ""
}

func (x *BorrowRecordStatusModifyReq) GetApprovalStatus() int32 {
	if x != nil {
		return x.ApprovalStatus
	}
	return 0
}

// 修改借阅文档状态请求
type BorrowDocumentStatusModifyReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BorrowRecordId string                 `protobuf:"bytes,1,opt,name=borrow_record_id,json=borrowRecordId,proto3" json:"borrow_record_id,omitempty"` // 借阅记录ID（必传）
	DocumentIds    []string               `protobuf:"bytes,2,rep,name=document_ids,json=documentIds,proto3" json:"document_ids,omitempty"`            // 文档ID列表（必传）
	BorrowStatus   int32                  `protobuf:"varint,3,opt,name=borrow_status,json=borrowStatus,proto3" json:"borrow_status,omitempty"`        // 新的借阅状态（必传）1借阅中 | 2回收中 | 3已回收
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BorrowDocumentStatusModifyReq) Reset() {
	*x = BorrowDocumentStatusModifyReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BorrowDocumentStatusModifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BorrowDocumentStatusModifyReq) ProtoMessage() {}

func (x *BorrowDocumentStatusModifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BorrowDocumentStatusModifyReq.ProtoReflect.Descriptor instead.
func (*BorrowDocumentStatusModifyReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{62}
}

func (x *BorrowDocumentStatusModifyReq) GetBorrowRecordId() string {
	if x != nil {
		return x.BorrowRecordId
	}
	return ""
}

func (x *BorrowDocumentStatusModifyReq) GetDocumentIds() []string {
	if x != nil {
		return x.DocumentIds
	}
	return nil
}

func (x *BorrowDocumentStatusModifyReq) GetBorrowStatus() int32 {
	if x != nil {
		return x.BorrowStatus
	}
	return 0
}

// 删除借阅记录请求
type BorrowRecordDeleteReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BorrowRecordId string                 `protobuf:"bytes,1,opt,name=borrow_record_id,json=borrowRecordId,proto3" json:"borrow_record_id,omitempty"` // 借阅记录ID（必传）
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BorrowRecordDeleteReq) Reset() {
	*x = BorrowRecordDeleteReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BorrowRecordDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BorrowRecordDeleteReq) ProtoMessage() {}

func (x *BorrowRecordDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BorrowRecordDeleteReq.ProtoReflect.Descriptor instead.
func (*BorrowRecordDeleteReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{63}
}

func (x *BorrowRecordDeleteReq) GetBorrowRecordId() string {
	if x != nil {
		return x.BorrowRecordId
	}
	return ""
}

type GetDistributeApplicationReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // 发放列表id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDistributeApplicationReq) Reset() {
	*x = GetDistributeApplicationReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDistributeApplicationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDistributeApplicationReq) ProtoMessage() {}

func (x *GetDistributeApplicationReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDistributeApplicationReq.ProtoReflect.Descriptor instead.
func (*GetDistributeApplicationReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{64}
}

func (x *GetDistributeApplicationReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// 获取发放清单响应
type GetDistributeApplicationResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*DistributeInventory `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"` // 发放清单
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDistributeApplicationResp) Reset() {
	*x = GetDistributeApplicationResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDistributeApplicationResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDistributeApplicationResp) ProtoMessage() {}

func (x *GetDistributeApplicationResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDistributeApplicationResp.ProtoReflect.Descriptor instead.
func (*GetDistributeApplicationResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{65}
}

func (x *GetDistributeApplicationResp) GetData() []*DistributeInventory {
	if x != nil {
		return x.Data
	}
	return nil
}

// 发放清单项
type DistributeInventory struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                             // 发放清单id
	FileId        string                 `protobuf:"bytes,2,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`       // 文档id
	FileName      string                 `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"` // 文档名称
	Number        string                 `protobuf:"bytes,4,opt,name=number,proto3" json:"number,omitempty"`                     // 文档编号
	Version       string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`                   // 文档版本
	Permissions   []*PermissionResp      `protobuf:"bytes,6,rep,name=permissions,proto3" json:"permissions,omitempty"`           // 权限列表
	Recipient     string                 `protobuf:"bytes,7,opt,name=recipient,proto3" json:"recipient,omitempty"`               // 接收方
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DistributeInventory) Reset() {
	*x = DistributeInventory{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DistributeInventory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributeInventory) ProtoMessage() {}

func (x *DistributeInventory) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributeInventory.ProtoReflect.Descriptor instead.
func (*DistributeInventory) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{66}
}

func (x *DistributeInventory) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DistributeInventory) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *DistributeInventory) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *DistributeInventory) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *DistributeInventory) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DistributeInventory) GetPermissions() []*PermissionResp {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *DistributeInventory) GetRecipient() string {
	if x != nil {
		return x.Recipient
	}
	return ""
}

// 权限响应
type PermissionResp struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FileForm       int32                  `protobuf:"varint,1,opt,name=file_form,json=fileForm,proto3" json:"file_form,omitempty"`                   // 文件形式（1电子文件 | 2纸质文件）
	FilePermission int32                  `protobuf:"varint,2,opt,name=file_permission,json=filePermission,proto3" json:"file_permission,omitempty"` // 文件权限（1查阅 | 2查阅/下载 | 3一次下载）
	ReceivedBy     []*ReceivedBy          `protobuf:"bytes,3,rep,name=received_by,json=receivedBy,proto3" json:"received_by,omitempty"`              // 接收人
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PermissionResp) Reset() {
	*x = PermissionResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermissionResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionResp) ProtoMessage() {}

func (x *PermissionResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionResp.ProtoReflect.Descriptor instead.
func (*PermissionResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{67}
}

func (x *PermissionResp) GetFileForm() int32 {
	if x != nil {
		return x.FileForm
	}
	return 0
}

func (x *PermissionResp) GetFilePermission() int32 {
	if x != nil {
		return x.FilePermission
	}
	return 0
}

func (x *PermissionResp) GetReceivedBy() []*ReceivedBy {
	if x != nil {
		return x.ReceivedBy
	}
	return nil
}

// 接收人信息
type ReceivedBy struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                 // 用户id
	Nickname      string                 `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`                           // 用户昵称
	Status        int32                  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`                              // 状态（1未回收 | 2回收审批中 | 3已回收）\
	RecycleDate   int64                  `protobuf:"varint,4,opt,name=recycle_date,json=recycleDate,proto3" json:"recycle_date,omitempty"` // 回收日期
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceivedBy) Reset() {
	*x = ReceivedBy{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceivedBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceivedBy) ProtoMessage() {}

func (x *ReceivedBy) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceivedBy.ProtoReflect.Descriptor instead.
func (*ReceivedBy) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{68}
}

func (x *ReceivedBy) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ReceivedBy) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *ReceivedBy) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ReceivedBy) GetRecycleDate() int64 {
	if x != nil {
		return x.RecycleDate
	}
	return 0
}

type DeleteDistributeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // 发放列表id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDistributeReq) Reset() {
	*x = DeleteDistributeReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDistributeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDistributeReq) ProtoMessage() {}

func (x *DeleteDistributeReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDistributeReq.ProtoReflect.Descriptor instead.
func (*DeleteDistributeReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{69}
}

func (x *DeleteDistributeReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// 获取发放详情请求
type GetDistributeDetailReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // 发放记录ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDistributeDetailReq) Reset() {
	*x = GetDistributeDetailReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDistributeDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDistributeDetailReq) ProtoMessage() {}

func (x *GetDistributeDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDistributeDetailReq.ProtoReflect.Descriptor instead.
func (*GetDistributeDetailReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{70}
}

func (x *GetDistributeDetailReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// 获取发放详情响应
type GetDistributeDetailResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Detail        *DistributeDetailInfo  `protobuf:"bytes,1,opt,name=detail,proto3" json:"detail,omitempty"` // 发放详情信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDistributeDetailResp) Reset() {
	*x = GetDistributeDetailResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDistributeDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDistributeDetailResp) ProtoMessage() {}

func (x *GetDistributeDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDistributeDetailResp.ProtoReflect.Descriptor instead.
func (*GetDistributeDetailResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{71}
}

func (x *GetDistributeDetailResp) GetDetail() *DistributeDetailInfo {
	if x != nil {
		return x.Detail
	}
	return nil
}

// 发放详情信息
type DistributeDetailInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 基本信息
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                            // 发放记录ID
	WorkflowId    string `protobuf:"bytes,2,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`          // 流程ID
	Applicant     string `protobuf:"bytes,3,opt,name=applicant,proto3" json:"applicant,omitempty"`                              // 申请人ID
	ApplicantName string `protobuf:"bytes,4,opt,name=applicant_name,json=applicantName,proto3" json:"applicant_name,omitempty"` // 申请人姓名
	ApplyDate     int64  `protobuf:"varint,5,opt,name=apply_date,json=applyDate,proto3" json:"apply_date,omitempty"`            // 申请日期
	// 类型信息
	DistributeType int32  `protobuf:"varint,6,opt,name=distribute_type,json=distributeType,proto3" json:"distribute_type,omitempty"`    // 发放类型（1内部发放 | 2外部发放）
	FileType       int32  `protobuf:"varint,7,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`                      // 文件类型（1内部文件 | 2外部文件）
	FileCategory   string `protobuf:"bytes,8,opt,name=file_category,json=fileCategory,proto3" json:"file_category,omitempty"`           // 文件类别
	TypeDictNodeId string `protobuf:"bytes,9,opt,name=type_dict_node_id,json=typeDictNodeId,proto3" json:"type_dict_node_id,omitempty"` // 类型字典节点ID
	// 原因信息
	Reason      string `protobuf:"bytes,10,opt,name=reason,proto3" json:"reason,omitempty"`                              // 发放原因
	OtherReason string `protobuf:"bytes,11,opt,name=other_reason,json=otherReason,proto3" json:"other_reason,omitempty"` // 其他原因
	// 日期和状态
	WishDistributeDate int64 `protobuf:"varint,12,opt,name=wish_distribute_date,json=wishDistributeDate,proto3" json:"wish_distribute_date,omitempty"` // 期望发放日期
	Status             int32 `protobuf:"varint,13,opt,name=status,proto3" json:"status,omitempty"`                                                     // 状态（1待提交 | 2待审批 | 3已审批 | 4已驳回）
	// 发放清单
	DistributeList []*DistributeInventoryDetail `protobuf:"bytes,14,rep,name=distribute_list,json=distributeList,proto3" json:"distribute_list,omitempty"` // 发放清单
	// 时间戳
	CreatedAt     int64 `protobuf:"varint,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间
	UpdatedAt     int64 `protobuf:"varint,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DistributeDetailInfo) Reset() {
	*x = DistributeDetailInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DistributeDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributeDetailInfo) ProtoMessage() {}

func (x *DistributeDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributeDetailInfo.ProtoReflect.Descriptor instead.
func (*DistributeDetailInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{72}
}

func (x *DistributeDetailInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DistributeDetailInfo) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *DistributeDetailInfo) GetApplicant() string {
	if x != nil {
		return x.Applicant
	}
	return ""
}

func (x *DistributeDetailInfo) GetApplicantName() string {
	if x != nil {
		return x.ApplicantName
	}
	return ""
}

func (x *DistributeDetailInfo) GetApplyDate() int64 {
	if x != nil {
		return x.ApplyDate
	}
	return 0
}

func (x *DistributeDetailInfo) GetDistributeType() int32 {
	if x != nil {
		return x.DistributeType
	}
	return 0
}

func (x *DistributeDetailInfo) GetFileType() int32 {
	if x != nil {
		return x.FileType
	}
	return 0
}

func (x *DistributeDetailInfo) GetFileCategory() string {
	if x != nil {
		return x.FileCategory
	}
	return ""
}

func (x *DistributeDetailInfo) GetTypeDictNodeId() string {
	if x != nil {
		return x.TypeDictNodeId
	}
	return ""
}

func (x *DistributeDetailInfo) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *DistributeDetailInfo) GetOtherReason() string {
	if x != nil {
		return x.OtherReason
	}
	return ""
}

func (x *DistributeDetailInfo) GetWishDistributeDate() int64 {
	if x != nil {
		return x.WishDistributeDate
	}
	return 0
}

func (x *DistributeDetailInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DistributeDetailInfo) GetDistributeList() []*DistributeInventoryDetail {
	if x != nil {
		return x.DistributeList
	}
	return nil
}

func (x *DistributeDetailInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *DistributeDetailInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// 发放清单详情
type DistributeInventoryDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                             // 发放清单ID
	FileId        string                 `protobuf:"bytes,2,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`       // 文件ID
	FileName      string                 `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"` // 文件名称
	Number        string                 `protobuf:"bytes,4,opt,name=number,proto3" json:"number,omitempty"`                     // 文件编号
	Version       string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`                   // 文件版本
	Permissions   []*PermissionDetail    `protobuf:"bytes,6,rep,name=permissions,proto3" json:"permissions,omitempty"`           // 权限详情列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DistributeInventoryDetail) Reset() {
	*x = DistributeInventoryDetail{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DistributeInventoryDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributeInventoryDetail) ProtoMessage() {}

func (x *DistributeInventoryDetail) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributeInventoryDetail.ProtoReflect.Descriptor instead.
func (*DistributeInventoryDetail) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{73}
}

func (x *DistributeInventoryDetail) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DistributeInventoryDetail) GetFileId() string {
	if x != nil {
		return x.FileId
	}
	return ""
}

func (x *DistributeInventoryDetail) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *DistributeInventoryDetail) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *DistributeInventoryDetail) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *DistributeInventoryDetail) GetPermissions() []*PermissionDetail {
	if x != nil {
		return x.Permissions
	}
	return nil
}

// 权限详情信息
type PermissionDetail struct {
	state          protoimpl.MessageState  `protogen:"open.v1"`
	FileForm       int32                   `protobuf:"varint,1,opt,name=file_form,json=fileForm,proto3" json:"file_form,omitempty"`                   // 文件形式（1电子文件 | 2纸质文件）
	FilePermission int32                   `protobuf:"varint,2,opt,name=file_permission,json=filePermission,proto3" json:"file_permission,omitempty"` // 文件权限（1查询 | 2查询/下载 | 3一次下载）
	Recipient      string                  `protobuf:"bytes,3,opt,name=recipient,proto3" json:"recipient,omitempty"`                                  // 接收方
	ReceivedBy     []*DistributeUserDetail `protobuf:"bytes,4,rep,name=received_by,json=receivedBy,proto3" json:"received_by,omitempty"`              // 接收人详情列表
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PermissionDetail) Reset() {
	*x = PermissionDetail{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermissionDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionDetail) ProtoMessage() {}

func (x *PermissionDetail) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionDetail.ProtoReflect.Descriptor instead.
func (*PermissionDetail) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{74}
}

func (x *PermissionDetail) GetFileForm() int32 {
	if x != nil {
		return x.FileForm
	}
	return 0
}

func (x *PermissionDetail) GetFilePermission() int32 {
	if x != nil {
		return x.FilePermission
	}
	return 0
}

func (x *PermissionDetail) GetRecipient() string {
	if x != nil {
		return x.Recipient
	}
	return ""
}

func (x *PermissionDetail) GetReceivedBy() []*DistributeUserDetail {
	if x != nil {
		return x.ReceivedBy
	}
	return nil
}

// 用户详情信息
type DistributeUserDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                       // 用户ID
	UserNickname  string                 `protobuf:"bytes,2,opt,name=user_nickname,json=userNickname,proto3" json:"user_nickname,omitempty"`     // 用户姓名
	RecycleStatus int32                  `protobuf:"varint,3,opt,name=recycle_status,json=recycleStatus,proto3" json:"recycle_status,omitempty"` // 回收状态（0未回收 | 1已回收 | 2待审批）
	RecycleTime   int64                  `protobuf:"varint,4,opt,name=recycle_time,json=recycleTime,proto3" json:"recycle_time,omitempty"`       // 回收时间（Unix时间戳）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DistributeUserDetail) Reset() {
	*x = DistributeUserDetail{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DistributeUserDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DistributeUserDetail) ProtoMessage() {}

func (x *DistributeUserDetail) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DistributeUserDetail.ProtoReflect.Descriptor instead.
func (*DistributeUserDetail) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{75}
}

func (x *DistributeUserDetail) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *DistributeUserDetail) GetUserNickname() string {
	if x != nil {
		return x.UserNickname
	}
	return ""
}

func (x *DistributeUserDetail) GetRecycleStatus() int32 {
	if x != nil {
		return x.RecycleStatus
	}
	return 0
}

func (x *DistributeUserDetail) GetRecycleTime() int64 {
	if x != nil {
		return x.RecycleTime
	}
	return 0
}

type UpdateUserDisposalStatusReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	DistributeId   string                 `protobuf:"bytes,1,opt,name=distribute_id,json=distributeId,proto3" json:"distribute_id,omitempty"`        // 发放记录ID
	DisposalStatus int32                  `protobuf:"varint,2,opt,name=disposal_status,json=disposalStatus,proto3" json:"disposal_status,omitempty"` // 处置状态
	Recycles       []*RecycleList         `protobuf:"bytes,3,rep,name=recycles,proto3" json:"recycles,omitempty"`                                    // 回收列表
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdateUserDisposalStatusReq) Reset() {
	*x = UpdateUserDisposalStatusReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserDisposalStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserDisposalStatusReq) ProtoMessage() {}

func (x *UpdateUserDisposalStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserDisposalStatusReq.ProtoReflect.Descriptor instead.
func (*UpdateUserDisposalStatusReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{76}
}

func (x *UpdateUserDisposalStatusReq) GetDistributeId() string {
	if x != nil {
		return x.DistributeId
	}
	return ""
}

func (x *UpdateUserDisposalStatusReq) GetDisposalStatus() int32 {
	if x != nil {
		return x.DisposalStatus
	}
	return 0
}

func (x *UpdateUserDisposalStatusReq) GetRecycles() []*RecycleList {
	if x != nil {
		return x.Recycles
	}
	return nil
}

// 根据发放清单ID查询回收信息请求
type GetRecycleInfoReq struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	DistributeRecordFileId string                 `protobuf:"bytes,1,opt,name=distribute_record_file_id,json=distributeRecordFileId,proto3" json:"distribute_record_file_id,omitempty"` // 发放记录文件ID
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *GetRecycleInfoReq) Reset() {
	*x = GetRecycleInfoReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRecycleInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecycleInfoReq) ProtoMessage() {}

func (x *GetRecycleInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecycleInfoReq.ProtoReflect.Descriptor instead.
func (*GetRecycleInfoReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{77}
}

func (x *GetRecycleInfoReq) GetDistributeRecordFileId() string {
	if x != nil {
		return x.DistributeRecordFileId
	}
	return ""
}

// 根据发放清单ID查询回收信息响应
type GetRecycleInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RecycleInfo   *RecycleInfo           `protobuf:"bytes,1,opt,name=recycle_info,json=recycleInfo,proto3" json:"recycle_info,omitempty"` // 回收信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRecycleInfoResp) Reset() {
	*x = GetRecycleInfoResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRecycleInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecycleInfoResp) ProtoMessage() {}

func (x *GetRecycleInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecycleInfoResp.ProtoReflect.Descriptor instead.
func (*GetRecycleInfoResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{78}
}

func (x *GetRecycleInfoResp) GetRecycleInfo() *RecycleInfo {
	if x != nil {
		return x.RecycleInfo
	}
	return nil
}

// 回收信息
type RecycleInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FileName       string                 `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`                   // 文件名称
	FileNumber     string                 `protobuf:"bytes,2,opt,name=file_number,json=fileNumber,proto3" json:"file_number,omitempty"`             // 文件编号
	RecycleRecords []*RecycleRecord       `protobuf:"bytes,3,rep,name=recycle_records,json=recycleRecords,proto3" json:"recycle_records,omitempty"` // 回收记录列表
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RecycleInfo) Reset() {
	*x = RecycleInfo{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecycleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecycleInfo) ProtoMessage() {}

func (x *RecycleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecycleInfo.ProtoReflect.Descriptor instead.
func (*RecycleInfo) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{79}
}

func (x *RecycleInfo) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *RecycleInfo) GetFileNumber() string {
	if x != nil {
		return x.FileNumber
	}
	return ""
}

func (x *RecycleInfo) GetRecycleRecords() []*RecycleRecord {
	if x != nil {
		return x.RecycleRecords
	}
	return nil
}

// 回收记录
type RecycleRecord struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	RecycleInitiator string                 `protobuf:"bytes,1,opt,name=recycle_initiator,json=recycleInitiator,proto3" json:"recycle_initiator,omitempty"` // 回收发起人
	RecycleReason    string                 `protobuf:"bytes,2,opt,name=recycle_reason,json=recycleReason,proto3" json:"recycle_reason,omitempty"`          // 回收原因
	HandoverPersons  []*HandoverPerson      `protobuf:"bytes,3,rep,name=handover_persons,json=handoverPersons,proto3" json:"handover_persons,omitempty"`    // 交还人信息列表
	Auditors         []string               `protobuf:"bytes,4,rep,name=auditors,proto3" json:"auditors,omitempty"`                                         // 审批人列表
	Approvers        []string               `protobuf:"bytes,5,rep,name=approvers,proto3" json:"approvers,omitempty"`                                       // 批准人列表
	RecycleDate      int64                  `protobuf:"varint,6,opt,name=recycle_date,json=recycleDate,proto3" json:"recycle_date,omitempty"`               // 回收日期（毫秒级时间戳）
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RecycleRecord) Reset() {
	*x = RecycleRecord{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecycleRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecycleRecord) ProtoMessage() {}

func (x *RecycleRecord) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecycleRecord.ProtoReflect.Descriptor instead.
func (*RecycleRecord) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{80}
}

func (x *RecycleRecord) GetRecycleInitiator() string {
	if x != nil {
		return x.RecycleInitiator
	}
	return ""
}

func (x *RecycleRecord) GetRecycleReason() string {
	if x != nil {
		return x.RecycleReason
	}
	return ""
}

func (x *RecycleRecord) GetHandoverPersons() []*HandoverPerson {
	if x != nil {
		return x.HandoverPersons
	}
	return nil
}

func (x *RecycleRecord) GetAuditors() []string {
	if x != nil {
		return x.Auditors
	}
	return nil
}

func (x *RecycleRecord) GetApprovers() []string {
	if x != nil {
		return x.Approvers
	}
	return nil
}

func (x *RecycleRecord) GetRecycleDate() int64 {
	if x != nil {
		return x.RecycleDate
	}
	return 0
}

// 交还人信息
type HandoverPerson struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	HandoverId     string                 `protobuf:"bytes,1,opt,name=handover_id,json=handoverId,proto3" json:"handover_id,omitempty"`              // 交还人ID
	HandoverName   string                 `protobuf:"bytes,2,opt,name=handover_name,json=handoverName,proto3" json:"handover_name,omitempty"`        // 交还人名字
	FileForm       int32                  `protobuf:"varint,3,opt,name=file_form,json=fileForm,proto3" json:"file_form,omitempty"`                   // 文件形式（1电子文件 | 2纸质文件）
	FilePermission int32                  `protobuf:"varint,4,opt,name=file_permission,json=filePermission,proto3" json:"file_permission,omitempty"` // 文件权限（1查询 | 2查询/下载 | 3一次下载）
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *HandoverPerson) Reset() {
	*x = HandoverPerson{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandoverPerson) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandoverPerson) ProtoMessage() {}

func (x *HandoverPerson) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandoverPerson.ProtoReflect.Descriptor instead.
func (*HandoverPerson) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{81}
}

func (x *HandoverPerson) GetHandoverId() string {
	if x != nil {
		return x.HandoverId
	}
	return ""
}

func (x *HandoverPerson) GetHandoverName() string {
	if x != nil {
		return x.HandoverName
	}
	return ""
}

func (x *HandoverPerson) GetFileForm() int32 {
	if x != nil {
		return x.FileForm
	}
	return 0
}

func (x *HandoverPerson) GetFilePermission() int32 {
	if x != nil {
		return x.FilePermission
	}
	return 0
}

// 根据发放清单ID查询处置详情请求
type GetDisposalDetailReq struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	DistributeRecordFileId string                 `protobuf:"bytes,1,opt,name=distribute_record_file_id,json=distributeRecordFileId,proto3" json:"distribute_record_file_id,omitempty"` // 发放记录文件ID
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *GetDisposalDetailReq) Reset() {
	*x = GetDisposalDetailReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDisposalDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDisposalDetailReq) ProtoMessage() {}

func (x *GetDisposalDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDisposalDetailReq.ProtoReflect.Descriptor instead.
func (*GetDisposalDetailReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{82}
}

func (x *GetDisposalDetailReq) GetDistributeRecordFileId() string {
	if x != nil {
		return x.DistributeRecordFileId
	}
	return ""
}

// 查询处置详情响应
type GetDisposalDetailResp struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	FileName        string                 `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`                      // 文件名称
	FileNumber      string                 `protobuf:"bytes,2,opt,name=file_number,json=fileNumber,proto3" json:"file_number,omitempty"`                // 文件编号
	DisposalRecords []*DisposalRecord      `protobuf:"bytes,3,rep,name=disposal_records,json=disposalRecords,proto3" json:"disposal_records,omitempty"` // 处置记录列表
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetDisposalDetailResp) Reset() {
	*x = GetDisposalDetailResp{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[83]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDisposalDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDisposalDetailResp) ProtoMessage() {}

func (x *GetDisposalDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[83]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDisposalDetailResp.ProtoReflect.Descriptor instead.
func (*GetDisposalDetailResp) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{83}
}

func (x *GetDisposalDetailResp) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *GetDisposalDetailResp) GetFileNumber() string {
	if x != nil {
		return x.FileNumber
	}
	return ""
}

func (x *GetDisposalDetailResp) GetDisposalRecords() []*DisposalRecord {
	if x != nil {
		return x.DisposalRecords
	}
	return nil
}

// 处置记录
type DisposalRecord struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	HandoverPerson string                 `protobuf:"bytes,1,opt,name=handover_person,json=handoverPerson,proto3" json:"handover_person,omitempty"` // 交还人
	HandoverDate   int64                  `protobuf:"varint,2,opt,name=handover_date,json=handoverDate,proto3" json:"handover_date,omitempty"`      // 交还日期
	RecyclePerson  string                 `protobuf:"bytes,3,opt,name=recycle_person,json=recyclePerson,proto3" json:"recycle_person,omitempty"`    // 回收人
	RecycleDate    int64                  `protobuf:"varint,4,opt,name=recycle_date,json=recycleDate,proto3" json:"recycle_date,omitempty"`         // 回收日期
	DisposalPerson string                 `protobuf:"bytes,5,opt,name=disposal_person,json=disposalPerson,proto3" json:"disposal_person,omitempty"` // 处置人
	DisposalDate   int64                  `protobuf:"varint,6,opt,name=disposal_date,json=disposalDate,proto3" json:"disposal_date,omitempty"`      // 处置日期
	DisposalMethod string                 `protobuf:"bytes,7,opt,name=disposal_method,json=disposalMethod,proto3" json:"disposal_method,omitempty"` // 处置方式
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DisposalRecord) Reset() {
	*x = DisposalRecord{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[84]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisposalRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisposalRecord) ProtoMessage() {}

func (x *DisposalRecord) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[84]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisposalRecord.ProtoReflect.Descriptor instead.
func (*DisposalRecord) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{84}
}

func (x *DisposalRecord) GetHandoverPerson() string {
	if x != nil {
		return x.HandoverPerson
	}
	return ""
}

func (x *DisposalRecord) GetHandoverDate() int64 {
	if x != nil {
		return x.HandoverDate
	}
	return 0
}

func (x *DisposalRecord) GetRecyclePerson() string {
	if x != nil {
		return x.RecyclePerson
	}
	return ""
}

func (x *DisposalRecord) GetRecycleDate() int64 {
	if x != nil {
		return x.RecycleDate
	}
	return 0
}

func (x *DisposalRecord) GetDisposalPerson() string {
	if x != nil {
		return x.DisposalPerson
	}
	return ""
}

func (x *DisposalRecord) GetDisposalDate() int64 {
	if x != nil {
		return x.DisposalDate
	}
	return 0
}

func (x *DisposalRecord) GetDisposalMethod() string {
	if x != nil {
		return x.DisposalMethod
	}
	return ""
}

// 更新权限使用状态请求
type UpdatePermissionUsedStatusReq struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	DistributeRecordFileId string                 `protobuf:"bytes,1,opt,name=distribute_record_file_id,json=distributeRecordFileId,proto3" json:"distribute_record_file_id,omitempty"` // 发放记录文件ID（清单ID）
	FileForm               int32                  `protobuf:"varint,2,opt,name=file_form,json=fileForm,proto3" json:"file_form,omitempty"`                                              // 文件形式（1电子文件 | 2纸质文件）
	FilePermission         int32                  `protobuf:"varint,3,opt,name=file_permission,json=filePermission,proto3" json:"file_permission,omitempty"`                            // 文件权限（1查阅 | 2查阅/下载 | 3一次下载）
	UserId                 string                 `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                                     // 用户ID
	IsUsed                 bool                   `protobuf:"varint,5,opt,name=is_used,json=isUsed,proto3" json:"is_used,omitempty"`                                                    // 是否已使用（true已使用 | false未使用）
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *UpdatePermissionUsedStatusReq) Reset() {
	*x = UpdatePermissionUsedStatusReq{}
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[85]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePermissionUsedStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePermissionUsedStatusReq) ProtoMessage() {}

func (x *UpdatePermissionUsedStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes[85]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePermissionUsedStatusReq.ProtoReflect.Descriptor instead.
func (*UpdatePermissionUsedStatusReq) Descriptor() ([]byte, []int) {
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP(), []int{85}
}

func (x *UpdatePermissionUsedStatusReq) GetDistributeRecordFileId() string {
	if x != nil {
		return x.DistributeRecordFileId
	}
	return ""
}

func (x *UpdatePermissionUsedStatusReq) GetFileForm() int32 {
	if x != nil {
		return x.FileForm
	}
	return 0
}

func (x *UpdatePermissionUsedStatusReq) GetFilePermission() int32 {
	if x != nil {
		return x.FilePermission
	}
	return 0
}

func (x *UpdatePermissionUsedStatusReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdatePermissionUsedStatusReq) GetIsUsed() bool {
	if x != nil {
		return x.IsUsed
	}
	return false
}

var File_internal_infrastructure_adapter_grpc_protos_docvault_proto protoreflect.FileDescriptor

var file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDesc = string([]byte{
	0x0a, 0x3a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x69, 0x6e, 0x66, 0x72, 0x61,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65,
	0x72, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x64, 0x6f,
	0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x64, 0x6f,
	0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x22, 0x0b, 0x0a, 0x09, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x68, 0x0a, 0x10, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x80, 0x01,
	0x0a, 0x0c, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36,
	0x0a, 0x08, 0x61, 0x75, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x61, 0x75,
	0x64, 0x69, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x6f, 0x63, 0x76,
	0x61, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x72, 0x73,
	0x22, 0x89, 0x03, 0x0a, 0x19, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b,
	0x0a, 0x09, 0x6e, 0x6f, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6e, 0x6f, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x6f, 0x63, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x64, 0x6f, 0x63, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x2e, 0x0a, 0x13,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x22, 0x2c, 0x0a, 0x1a,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x99, 0x03, 0x0a, 0x19, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x6f, 0x63, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x64, 0x6f, 0x63, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x6f,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4e,
	0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x2e, 0x0a, 0x13,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x1b, 0x0a, 0x09,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x5f,
	0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f,
	0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x22, 0x28, 0x0a, 0x16, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x22, 0xfc, 0x03, 0x0a, 0x17, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6e, 0x6f, 0x12, 0x1d, 0x0a, 0x0a,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x2e, 0x0a, 0x13,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x64, 0x6f, 0x63, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x6f, 0x63, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x61,
	0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0d, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x5f,
	0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f,
	0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22,
	0x54, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x6e, 0x6f, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6e,
	0x6f, 0x50, 0x61, 0x67, 0x65, 0x22, 0xc9, 0x02, 0x0a, 0x17, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x2f, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x10,
	0x64, 0x6f, 0x63, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x6f, 0x63, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x49, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x68, 0x61, 0x73, 0x5f, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x68,
	0x61, 0x73, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x6e, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6e, 0x6f,
	0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4e,
	0x6f, 0x22, 0x68, 0x0a, 0x18, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x64, 0x6f,
	0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xc7, 0x03, 0x0a, 0x18,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x6e, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6e, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x2e, 0x0a, 0x13, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x6f, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f,
	0x64, 0x6f, 0x63, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x6f, 0x63, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x70,
	0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xf4, 0x02, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12,
	0x25, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x6f, 0x6f, 0x6b, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x20, 0x0a, 0x0e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x99,
	0x02, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x2f, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x11, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x6e, 0x5f, 0x62, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x6e, 0x42, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xf3, 0x04, 0x0a, 0x08, 0x42,
	0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x72, 0x12, 0x1b,
	0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x62,
	0x6f, 0x72, 0x72, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x6e,
	0x5f, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6f,
	0x6e, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x72, 0x70, 0x6c,
	0x75, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x73, 0x75, 0x72, 0x70, 0x6c, 0x75, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x2c, 0x0a, 0x12, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e,
	0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x69,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x22, 0x4f, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75,
	0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x36, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x1f, 0x0a, 0x0d, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x36, 0x0a, 0x0e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x22, 0x53, 0x0a, 0x0d, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x6f, 0x6f, 0x6b,
	0x52, 0x65, 0x71, 0x12, 0x42, 0x0a, 0x10, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xfd, 0x01, 0x0a, 0x0e, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x69,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x6f, 0x6f,
	0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x6f,
	0x6f, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x17,
	0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x3e, 0x0a, 0x0e, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2c, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75,
	0x6c, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x57, 0x0a, 0x0e, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72,
	0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x6b, 0x49, 0x64,
	0x22, 0x88, 0x01, 0x0a, 0x1e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x66, 0x0a, 0x1d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x72, 0x65, 0x71, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x6f, 0x63,
	0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x52,
	0x1a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x73, 0x22, 0x7c, 0x0a, 0x1f, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x59,
	0x0a, 0x17, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x52, 0x15, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x19, 0x45, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x38, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74,
	0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x6f, 0x72, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06,
	0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72,
	0x67, 0x49, 0x64, 0x22, 0xb5, 0x05, 0x0a, 0x1a, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x35, 0x0a, 0x17, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x74, 0x79, 0x70, 0x65, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e,
	0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x22, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72,
	0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x1f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66,
	0x69, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x75, 0x74,
	0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x6f,
	0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x44, 0x6f, 0x63, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x12, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x5f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x4f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x29, 0x0a, 0x10, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x4f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x5a, 0x0a, 0x1a, 0x45,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75,
	0x6c, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xef, 0x01, 0x0a, 0x1e, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f,
	0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x74, 0x79, 0x70,
	0x65, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x12, 0x39, 0x0a, 0x19, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x64, 0x69, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x69, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x22,
	0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e,
	0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x73, 0x22, 0x28, 0x0a, 0x16, 0x45, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x22, 0xe5, 0x05, 0x0a, 0x17, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x6f, 0x63,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x2e, 0x0a, 0x13,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x44, 0x6f, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x12, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x44,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x41, 0x75, 0x74,
	0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x69, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x74, 0x79, 0x70, 0x65, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f,
	0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x22, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x1f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0xde, 0x04, 0x0a, 0x17,
	0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x64, 0x6f, 0x63,
	0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d, 0x0a,
	0x12, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x44, 0x6f, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x12, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x44,
	0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x18, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x74, 0x79, 0x70,
	0x65, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x49, 0x0a,
	0x21, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e,
	0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x65, 0x5f, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x62, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x65, 0x64, 0x46, 0x69,
	0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6f, 0x72,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x22, 0x68, 0x0a, 0x18,
	0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c,
	0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xa3, 0x06, 0x0a, 0x18, 0x45, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x29,
	0x0a, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x2e, 0x0a, 0x13, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x2c, 0x0a, 0x12, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x6f, 0x63, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x44, 0x6f, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d,
	0x0a, 0x12, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3b, 0x0a,
	0x0d, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e,
	0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x41, 0x75,
	0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x74, 0x79, 0x70, 0x65, 0x44, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79,
	0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79,
	0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x22, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e,
	0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x13, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x1f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65,
	0x49, 0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0xc4, 0x05, 0x0a,
	0x19, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x0a, 0x13, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c,
	0x0a, 0x12, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x44, 0x6f, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x12,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x44, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x17, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e,
	0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x74, 0x79,
	0x70, 0x65, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65,
	0x49, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x64, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x4b, 0x0a,
	0x22, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1f, 0x61, 0x75, 0x74, 0x68, 0x65,
	0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x29, 0x0a,
	0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61,
	0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66,
	0x69, 0x78, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6f, 0x63, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x68,
	0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x22, 0x74, 0x0a, 0x1b, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x44, 0x6f, 0x63, 0x73, 0x54, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x6f, 0x63, 0x73, 0x54, 0x6f, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x67, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x72, 0x67, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x1c, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x6f, 0x63, 0x73, 0x54, 0x6f, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x1e,
	0x0a, 0x1c, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x6f, 0x63,
	0x73, 0x54, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70, 0x22, 0x26,
	0x0a, 0x12, 0x50, 0x6c, 0x61, 0x67, 0x69, 0x61, 0x72, 0x69, 0x73, 0x6d, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0xeb, 0x03, 0x0a, 0x15, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x11, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x69, 0x63, 0x74,
	0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x74, 0x79, 0x70, 0x65, 0x44, 0x69, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x74,
	0x68, 0x65, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x69, 0x73,
	0x68, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x77, 0x69, 0x73, 0x68, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x41, 0x0a, 0x0f, 0x64,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0a,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0e,
	0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69,
	0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x61, 0x76, 0x65, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x22, 0xb0, 0x01, 0x0a, 0x0e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x36, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa6, 0x01, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x66,
	0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x46,
	0x6f, 0x72, 0x6d, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x66, 0x69,
	0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09,
	0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x52, 0x65, 0x63, 0x69, 0x70,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x42, 0x79,
	0x22, 0x41, 0x0a, 0x09, 0x52, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x79, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x66, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xae,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x17,
	0x77, 0x61, 0x69, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x77,
	0x61, 0x69, 0x74, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x6e, 0x6f, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x79, 0x63,
	0x6c, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x10, 0x6e, 0x6f, 0x74, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x72,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x73, 0x22,
	0x54, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xbf, 0x02, 0x0a, 0x13, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c,
	0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a,
	0x0d, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c,
	0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x38, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74,
	0x2e, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0b, 0x72, 0x65,
	0x63, 0x79, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0d, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6c, 0x0a, 0x0b, 0x52, 0x65, 0x63, 0x79, 0x63,
	0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e,
	0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0b, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x77, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x46, 0x6f, 0x72, 0x6d, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x66,
	0x69, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x42, 0x79, 0x22, 0xa4,
	0x02, 0x0a, 0x14, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x41, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70,
	0x6f, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0d, 0x64, 0x69,
	0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x69, 0x73,
	0x70, 0x6f, 0x73, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6f,
	0x73, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6d, 0x0a, 0x0c, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61,
	0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f,
	0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x76,
	0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa6, 0x02, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f,
	0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x27,
	0x0a, 0x0f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1c, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x22, 0x62, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x33, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x64, 0x6f, 0x63,
	0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0xbf, 0x05, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x30,
	0x0a, 0x14, 0x77, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x77, 0x69,
	0x73, 0x68, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x34, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x18, 0x0e, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x08, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x0c, 0x6e, 0x6f, 0x74, 0x5f, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0b, 0x6e, 0x6f, 0x74, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x64, 0x12, 0x32, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18,
	0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74,
	0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x07, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6f, 0x73, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0a, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61,
	0x6c, 0x42, 0x79, 0x22, 0x62, 0x0a, 0x0e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x6e,
	0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e,
	0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xdc, 0x01, 0x0a, 0x15, 0x44, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x52, 0x65,
	0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x69,
	0x67, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70,
	0x6f, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0d, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xe5, 0x01, 0x0a, 0x12, 0x42, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1f, 0x0a,
	0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xc8,
	0x03, 0x0a, 0x15, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x3a, 0x0a, 0x09, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e,
	0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x64, 0x75, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x64, 0x75, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f, 0x72, 0x72,
	0x6f, 0x77, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77,
	0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x4f, 0x74, 0x68, 0x65, 0x72,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x6f, 0x72,
	0x72, 0x6f, 0x77, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0f, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0d,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x41,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x28, 0x0a, 0x16, 0x42, 0x6f, 0x72,
	0x72, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x22, 0xf2, 0x03, 0x0a, 0x15, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x12, 0x28, 0x0a,
	0x10, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x3a, 0x0a, 0x09, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x42,
	0x6f, 0x72, 0x72, 0x6f, 0x77, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x64, 0x75, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x64, 0x75, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f, 0x72, 0x72,
	0x6f, 0x77, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77,
	0x5f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x4f, 0x74, 0x68, 0x65, 0x72,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77,
	0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0f, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0d, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x70, 0x0a, 0x1b, 0x42, 0x6f, 0x72, 0x72,
	0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x91, 0x01, 0x0a, 0x1d, 0x42,
	0x6f, 0x72, 0x72, 0x6f, 0x77, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x10,
	0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x6f, 0x72,
	0x72, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x41,
	0x0a, 0x15, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x6f, 0x72, 0x72, 0x6f,
	0x77, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x62, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49,
	0x64, 0x22, 0x2d, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x51, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0xe7, 0x01, 0x0a, 0x13, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61,
	0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x22, 0x8d, 0x01,
	0x0a, 0x0e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x27, 0x0a,
	0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x64, 0x6f,
	0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x42,
	0x79, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x42, 0x79, 0x22, 0x7c, 0x0a,
	0x0a, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x42, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x79,
	0x63, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x25, 0x0a, 0x13, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x28, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x51, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75,
	0x6c, 0x74, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22,
	0xd2, 0x04, 0x0a, 0x14, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x29, 0x0a, 0x11, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x79, 0x70, 0x65, 0x44, 0x69, 0x63, 0x74, 0x4e, 0x6f, 0x64,
	0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x30,
	0x0a, 0x14, 0x77, 0x69, 0x73, 0x68, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x77, 0x69,
	0x73, 0x68, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4c, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0e, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0xd1, 0x01, 0x0a, 0x19, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0b, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0b, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xb7, 0x01, 0x0a, 0x10, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e,
	0x74, 0x12, 0x3f, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c,
	0x74, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x64,
	0x42, 0x79, 0x22, 0x9e, 0x01, 0x0a, 0x14, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x69, 0x63,
	0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x73, 0x65,
	0x72, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x70,
	0x6f, 0x73, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x31, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x52,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x08, 0x72, 0x65, 0x63, 0x79,
	0x63, 0x6c, 0x65, 0x73, 0x22, 0x4e, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x79, 0x63,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x39, 0x0a, 0x19, 0x64, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x64, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x46, 0x69,
	0x6c, 0x65, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x79, 0x63,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x38, 0x0a, 0x0c, 0x72, 0x65,
	0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x52, 0x65, 0x63, 0x79,
	0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x8d, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x40, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x64, 0x6f,
	0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x22, 0x85, 0x02, 0x0a, 0x0d, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c,
	0x65, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x10, 0x68, 0x61,
	0x6e, 0x64, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e,
	0x48, 0x61, 0x6e, 0x64, 0x6f, 0x76, 0x65, 0x72, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x52, 0x0f,
	0x68, 0x61, 0x6e, 0x64, 0x6f, 0x76, 0x65, 0x72, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x61, 0x75, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x75, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x9c, 0x01, 0x0a,
	0x0e, 0x48, 0x61, 0x6e, 0x64, 0x6f, 0x76, 0x65, 0x72, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12,
	0x1f, 0x0a, 0x0b, 0x68, 0x61, 0x6e, 0x64, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x68, 0x61, 0x6e, 0x64, 0x6f, 0x76, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x68, 0x61, 0x6e, 0x64, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x61, 0x6e, 0x64, 0x6f, 0x76, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x66, 0x6f,
	0x72, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x6f,
	0x72, 0x6d, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x66, 0x69, 0x6c,
	0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x51, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x12, 0x39, 0x0a, 0x19, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x9a,
	0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73,
	0x61, 0x6c, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x69, 0x73, 0x70,
	0x6f, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x70,
	0x6f, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x9f, 0x02, 0x0a, 0x0e,
	0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x27,
	0x0a, 0x0f, 0x68, 0x61, 0x6e, 0x64, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x68, 0x61, 0x6e, 0x64, 0x6f, 0x76, 0x65,
	0x72, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x61, 0x6e, 0x64, 0x6f,
	0x76, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x68, 0x61, 0x6e, 0x64, 0x6f, 0x76, 0x65, 0x72, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x79, 0x63,
	0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73,
	0x61, 0x6c, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12,
	0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c,
	0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64,
	0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0xd2, 0x01,
	0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x55, 0x73, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x39, 0x0a, 0x19, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x16, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f,
	0x75, 0x73, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x55, 0x73,
	0x65, 0x64, 0x32, 0xb1, 0x03, 0x0a, 0x17, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x12, 0x53,
	0x0a, 0x06, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x23, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61,
	0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e,
	0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x03, 0x47, 0x65, 0x74, 0x12, 0x20, 0x2e, 0x64, 0x6f, 0x63,
	0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x64,
	0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x4d, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x12, 0x21, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75,
	0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x64, 0x6f, 0x63,
	0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x42,
	0x0a, 0x06, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x23, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61,
	0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e,
	0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x62, 0x0a, 0x0b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x12, 0x28, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x64, 0x6f,
	0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x32, 0xc9, 0x02, 0x0a, 0x04, 0x42, 0x6f, 0x6f, 0x6b, 0x12,
	0x3f, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x17, 0x2e,
	0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c,
	0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x42, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f,
	0x6f, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x64, 0x6f, 0x63, 0x76,
	0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x12, 0x12, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f,
	0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c,
	0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x3f, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x17,
	0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75,
	0x6c, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x3f, 0x0a, 0x0a, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x12,
	0x17, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61,
	0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x32, 0x8b, 0x04, 0x0a, 0x17, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x12, 0x53,
	0x0a, 0x06, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x23, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61,
	0x75, 0x6c, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e,
	0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x03, 0x47, 0x65, 0x74, 0x12, 0x20, 0x2e, 0x64, 0x6f, 0x63,
	0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x64,
	0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x4d, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x12, 0x21, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75,
	0x6c, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x64, 0x6f, 0x63,
	0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x42,
	0x0a, 0x06, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x23, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61,
	0x75, 0x6c, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e,
	0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x51, 0x0a, 0x1c, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x50, 0x6c, 0x61, 0x67, 0x69, 0x61, 0x72, 0x69, 0x73, 0x6d, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x12, 0x1c, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x6c,
	0x61, 0x67, 0x69, 0x61, 0x72, 0x69, 0x73, 0x6d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71,
	0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x69, 0x0a, 0x18, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x44, 0x6f, 0x63, 0x73, 0x54, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x12, 0x25, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x6f, 0x63, 0x73, 0x54, 0x6f, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61,
	0x75, 0x6c, 0x74, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44,
	0x6f, 0x63, 0x73, 0x54, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x32, 0xf6, 0x0d, 0x0a, 0x0f, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x12, 0x4f, 0x0a, 0x17, 0x50, 0x72, 0x65, 0x53, 0x61, 0x76, 0x65, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x1f, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x60, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x22,
	0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x6f, 0x63,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x23, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x44, 0x6f, 0x63, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5e, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x79, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x23, 0x2e,
	0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x16, 0x53, 0x61, 0x76, 0x65, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x12, 0x1f, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x52,
	0x65, 0x71, 0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x17, 0x53, 0x61, 0x76, 0x65, 0x52,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x52, 0x65,
	0x63, 0x79, 0x63, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4f, 0x0a, 0x18, 0x53, 0x61, 0x76, 0x65, 0x44, 0x69,
	0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x69,
	0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x58, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x1e, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x1f, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x6d, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x49,
	0x64, 0x12, 0x25, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61,
	0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x4c, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x2e, 0x64, 0x6f, 0x63,
	0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63, 0x76,
	0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5a,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x20, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75,
	0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x63, 0x0a, 0x26, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x46, 0x69,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x1a, 0x1c, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x59, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x79, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12,
	0x1b, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x63, 0x79, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x64,
	0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x79, 0x63,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x57, 0x0a, 0x12, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x1f, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x72, 0x72,
	0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x20, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x72,
	0x72, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x42, 0x6f, 0x72,
	0x72, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1f, 0x2e, 0x64, 0x6f, 0x63, 0x76,
	0x61, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63,
	0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x56, 0x0a, 0x18, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x2e, 0x64, 0x6f,
	0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52,
	0x65, 0x71, 0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5a, 0x0a, 0x1a, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74,
	0x2e, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x13,
	0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x6f, 0x72,
	0x72, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1f, 0x2e, 0x64, 0x6f, 0x63, 0x76,
	0x61, 0x75, 0x6c, 0x74, 0x2e, 0x42, 0x6f, 0x72, 0x72, 0x6f, 0x77, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63,
	0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x56, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x73,
	0x70, 0x6f, 0x73, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x2e, 0x64, 0x6f,
	0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x44, 0x69,
	0x73, 0x70, 0x6f, 0x73, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1e, 0x2e, 0x64,
	0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6f,
	0x73, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x64,
	0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6f,
	0x73, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5a, 0x0a,
	0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x55, 0x73, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x2e, 0x64, 0x6f,
	0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x64, 0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x42, 0x0c, 0x5a, 0x0a, 0x2e, 0x2f, 0x64,
	0x6f, 0x63, 0x76, 0x61, 0x75, 0x6c, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescOnce sync.Once
	file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescData []byte
)

func file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescGZIP() []byte {
	file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescOnce.Do(func() {
		file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDesc), len(file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDesc)))
	})
	return file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDescData
}

var file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes = make([]protoimpl.MessageInfo, 86)
var file_internal_infrastructure_adapter_grpc_protos_docvault_proto_goTypes = []any{
	(*EmptyResp)(nil),                       // 0: docvault.EmptyResp
	(*ApprovalInfoItem)(nil),                // 1: docvault.ApprovalInfoItem
	(*ApprovalInfo)(nil),                    // 2: docvault.ApprovalInfo
	(*InternalDocumentCreateReq)(nil),       // 3: docvault.InternalDocumentCreateReq
	(*InternalDocumentCreateResp)(nil),      // 4: docvault.InternalDocumentCreateResp
	(*InternalDocumentChangeReq)(nil),       // 5: docvault.InternalDocumentChangeReq
	(*InternalDocumentGetReq)(nil),          // 6: docvault.InternalDocumentGetReq
	(*InternalDocumentGetResp)(nil),         // 7: docvault.InternalDocumentGetResp
	(*PageInfo)(nil),                        // 8: docvault.PageInfo
	(*InternalDocumentPageReq)(nil),         // 9: docvault.InternalDocumentPageReq
	(*InternalDocumentPageResp)(nil),        // 10: docvault.InternalDocumentPageResp
	(*InternalDocumentPageItem)(nil),        // 11: docvault.InternalDocumentPageItem
	(*CreateBookReq)(nil),                   // 12: docvault.CreateBookReq
	(*CreateBookResp)(nil),                  // 13: docvault.CreateBookResp
	(*GetBookListReq)(nil),                  // 14: docvault.GetBookListReq
	(*BookInfo)(nil),                        // 15: docvault.BookInfo
	(*GetBookListResp)(nil),                 // 16: docvault.GetBookListResp
	(*UpdateBookResp)(nil),                  // 17: docvault.UpdateBookResp
	(*DeleteBookReq)(nil),                   // 18: docvault.DeleteBookReq
	(*DeleteBookResp)(nil),                  // 19: docvault.DeleteBookResp
	(*ImportBookReq)(nil),                   // 20: docvault.ImportBookReq
	(*ImportBookInfo)(nil),                  // 21: docvault.ImportBookInfo
	(*ImportBookResp)(nil),                  // 22: docvault.ImportBookResp
	(*ImportRespInfo)(nil),                  // 23: docvault.ImportRespInfo
	(*InternalDocumentBatchCreateReq)(nil),  // 24: docvault.InternalDocumentBatchCreateReq
	(*InternalDocumentBatchCreateResp)(nil), // 25: docvault.InternalDocumentBatchCreateResp
	(*ExternalDocumentCreateReq)(nil),       // 26: docvault.ExternalDocumentCreateReq
	(*ExternalDocumentCreateInfo)(nil),      // 27: docvault.ExternalDocumentCreateInfo
	(*ExternalDocumentCreateResp)(nil),      // 28: docvault.ExternalDocumentCreateResp
	(*ExternalDocumentCreateRespList)(nil),  // 29: docvault.ExternalDocumentCreateRespList
	(*ExternalDocumentGetReq)(nil),          // 30: docvault.ExternalDocumentGetReq
	(*ExternalDocumentGetResp)(nil),         // 31: docvault.ExternalDocumentGetResp
	(*ExternalDocumentPageReq)(nil),         // 32: docvault.ExternalDocumentPageReq
	(*ExternalDocumentPageResp)(nil),        // 33: docvault.ExternalDocumentPageResp
	(*ExternalDocumentPageInfo)(nil),        // 34: docvault.ExternalDocumentPageInfo
	(*ExternalDocumentChangeReq)(nil),       // 35: docvault.ExternalDocumentChangeReq
	(*ImportGroupDocsToCompanyReq)(nil),     // 36: docvault.ImportGroupDocsToCompanyReq
	(*ImportGroupDocsToCompanyInfo)(nil),    // 37: docvault.ImportGroupDocsToCompanyInfo
	(*ImportGroupDocsToCompanyResp)(nil),    // 38: docvault.ImportGroupDocsToCompanyResp
	(*PlagiarismCheckReq)(nil),              // 39: docvault.PlagiarismCheckReq
	(*DocumentDistributeReq)(nil),           // 40: docvault.DocumentDistributeReq
	(*DistributeList)(nil),                  // 41: docvault.DistributeList
	(*Permission)(nil),                      // 42: docvault.Permission
	(*Recipient)(nil),                       // 43: docvault.Recipient
	(*GetDocPermissionUsersReq)(nil),        // 44: docvault.GetDocPermissionUsersReq
	(*GetDocPermissionUsersResp)(nil),       // 45: docvault.GetDocPermissionUsersResp
	(*UpdateDistributeStatusReq)(nil),       // 46: docvault.UpdateDistributeStatusReq
	(*RecycleApprovalInfo)(nil),             // 47: docvault.RecycleApprovalInfo
	(*RecycleList)(nil),                     // 48: docvault.RecycleList
	(*FilePermission)(nil),                  // 49: docvault.FilePermission
	(*DisposalApprovalInfo)(nil),            // 50: docvault.DisposalApprovalInfo
	(*DisposalList)(nil),                    // 51: docvault.DisposalList
	(*GetDistributeListReq)(nil),            // 52: docvault.GetDistributeListReq
	(*GetDistributeListResp)(nil),           // 53: docvault.GetDistributeListResp
	(*GetDistributeListInfo)(nil),           // 54: docvault.GetDistributeListInfo
	(*DistributeUser)(nil),                  // 55: docvault.DistributeUser
	(*DistributeApprovalReq)(nil),           // 56: docvault.DistributeApprovalReq
	(*BorrowDocumentItem)(nil),              // 57: docvault.BorrowDocumentItem
	(*BorrowRecordCreateReq)(nil),           // 58: docvault.BorrowRecordCreateReq
	(*BorrowRecordCreateResp)(nil),          // 59: docvault.BorrowRecordCreateResp
	(*BorrowRecordModifyReq)(nil),           // 60: docvault.BorrowRecordModifyReq
	(*BorrowRecordStatusModifyReq)(nil),     // 61: docvault.BorrowRecordStatusModifyReq
	(*BorrowDocumentStatusModifyReq)(nil),   // 62: docvault.BorrowDocumentStatusModifyReq
	(*BorrowRecordDeleteReq)(nil),           // 63: docvault.BorrowRecordDeleteReq
	(*GetDistributeApplicationReq)(nil),     // 64: docvault.GetDistributeApplicationReq
	(*GetDistributeApplicationResp)(nil),    // 65: docvault.GetDistributeApplicationResp
	(*DistributeInventory)(nil),             // 66: docvault.DistributeInventory
	(*PermissionResp)(nil),                  // 67: docvault.PermissionResp
	(*ReceivedBy)(nil),                      // 68: docvault.ReceivedBy
	(*DeleteDistributeReq)(nil),             // 69: docvault.DeleteDistributeReq
	(*GetDistributeDetailReq)(nil),          // 70: docvault.GetDistributeDetailReq
	(*GetDistributeDetailResp)(nil),         // 71: docvault.GetDistributeDetailResp
	(*DistributeDetailInfo)(nil),            // 72: docvault.DistributeDetailInfo
	(*DistributeInventoryDetail)(nil),       // 73: docvault.DistributeInventoryDetail
	(*PermissionDetail)(nil),                // 74: docvault.PermissionDetail
	(*DistributeUserDetail)(nil),            // 75: docvault.DistributeUserDetail
	(*UpdateUserDisposalStatusReq)(nil),     // 76: docvault.UpdateUserDisposalStatusReq
	(*GetRecycleInfoReq)(nil),               // 77: docvault.GetRecycleInfoReq
	(*GetRecycleInfoResp)(nil),              // 78: docvault.GetRecycleInfoResp
	(*RecycleInfo)(nil),                     // 79: docvault.RecycleInfo
	(*RecycleRecord)(nil),                   // 80: docvault.RecycleRecord
	(*HandoverPerson)(nil),                  // 81: docvault.HandoverPerson
	(*GetDisposalDetailReq)(nil),            // 82: docvault.GetDisposalDetailReq
	(*GetDisposalDetailResp)(nil),           // 83: docvault.GetDisposalDetailResp
	(*DisposalRecord)(nil),                  // 84: docvault.DisposalRecord
	(*UpdatePermissionUsedStatusReq)(nil),   // 85: docvault.UpdatePermissionUsedStatusReq
}
var file_internal_infrastructure_adapter_grpc_protos_docvault_proto_depIdxs = []int32{
	1,  // 0: docvault.ApprovalInfo.auditors:type_name -> docvault.ApprovalInfoItem
	1,  // 1: docvault.ApprovalInfo.approvers:type_name -> docvault.ApprovalInfoItem
	2,  // 2: docvault.InternalDocumentGetResp.approval_info:type_name -> docvault.ApprovalInfo
	8,  // 3: docvault.InternalDocumentPageReq.page_info:type_name -> docvault.PageInfo
	11, // 4: docvault.InternalDocumentPageResp.data:type_name -> docvault.InternalDocumentPageItem
	2,  // 5: docvault.InternalDocumentPageItem.approval_info:type_name -> docvault.ApprovalInfo
	8,  // 6: docvault.GetBookListReq.page_info:type_name -> docvault.PageInfo
	15, // 7: docvault.GetBookListResp.data:type_name -> docvault.BookInfo
	21, // 8: docvault.ImportBookReq.import_book_info:type_name -> docvault.ImportBookInfo
	23, // 9: docvault.ImportBookResp.data:type_name -> docvault.ImportRespInfo
	3,  // 10: docvault.InternalDocumentBatchCreateReq.internal_document_create_reqs:type_name -> docvault.InternalDocumentCreateReq
	7,  // 11: docvault.InternalDocumentBatchCreateResp.internal_document_infos:type_name -> docvault.InternalDocumentGetResp
	27, // 12: docvault.ExternalDocumentCreateReq.data:type_name -> docvault.ExternalDocumentCreateInfo
	29, // 13: docvault.ExternalDocumentCreateResp.data:type_name -> docvault.ExternalDocumentCreateRespList
	8,  // 14: docvault.ExternalDocumentPageReq.page_info:type_name -> docvault.PageInfo
	34, // 15: docvault.ExternalDocumentPageResp.data:type_name -> docvault.ExternalDocumentPageInfo
	2,  // 16: docvault.ExternalDocumentPageInfo.approval_info:type_name -> docvault.ApprovalInfo
	37, // 17: docvault.ImportGroupDocsToCompanyReq.data:type_name -> docvault.ImportGroupDocsToCompanyInfo
	41, // 18: docvault.DocumentDistributeReq.distribute_list:type_name -> docvault.DistributeList
	42, // 19: docvault.DistributeList.permissions:type_name -> docvault.Permission
	43, // 20: docvault.Permission.received_by:type_name -> docvault.Recipient
	48, // 21: docvault.RecycleApprovalInfo.recycle_list:type_name -> docvault.RecycleList
	2,  // 22: docvault.RecycleApprovalInfo.approval_info:type_name -> docvault.ApprovalInfo
	49, // 23: docvault.RecycleList.permissions:type_name -> docvault.FilePermission
	51, // 24: docvault.DisposalApprovalInfo.disposal_list:type_name -> docvault.DisposalList
	2,  // 25: docvault.DisposalApprovalInfo.approval_info:type_name -> docvault.ApprovalInfo
	49, // 26: docvault.DisposalList.permissions:type_name -> docvault.FilePermission
	8,  // 27: docvault.GetDistributeListReq.page_info:type_name -> docvault.PageInfo
	54, // 28: docvault.GetDistributeListResp.data:type_name -> docvault.GetDistributeListInfo
	2,  // 29: docvault.GetDistributeListInfo.approval_info:type_name -> docvault.ApprovalInfo
	55, // 30: docvault.GetDistributeListInfo.received:type_name -> docvault.DistributeUser
	55, // 31: docvault.GetDistributeListInfo.not_received:type_name -> docvault.DistributeUser
	55, // 32: docvault.GetDistributeListInfo.recycle:type_name -> docvault.DistributeUser
	55, // 33: docvault.GetDistributeListInfo.disposal_by:type_name -> docvault.DistributeUser
	2,  // 34: docvault.DistributeApprovalReq.approval_info:type_name -> docvault.ApprovalInfo
	57, // 35: docvault.BorrowRecordCreateReq.documents:type_name -> docvault.BorrowDocumentItem
	2,  // 36: docvault.BorrowRecordCreateReq.approval_info:type_name -> docvault.ApprovalInfo
	57, // 37: docvault.BorrowRecordModifyReq.documents:type_name -> docvault.BorrowDocumentItem
	2,  // 38: docvault.BorrowRecordModifyReq.approval_info:type_name -> docvault.ApprovalInfo
	66, // 39: docvault.GetDistributeApplicationResp.data:type_name -> docvault.DistributeInventory
	67, // 40: docvault.DistributeInventory.permissions:type_name -> docvault.PermissionResp
	68, // 41: docvault.PermissionResp.received_by:type_name -> docvault.ReceivedBy
	72, // 42: docvault.GetDistributeDetailResp.detail:type_name -> docvault.DistributeDetailInfo
	73, // 43: docvault.DistributeDetailInfo.distribute_list:type_name -> docvault.DistributeInventoryDetail
	74, // 44: docvault.DistributeInventoryDetail.permissions:type_name -> docvault.PermissionDetail
	75, // 45: docvault.PermissionDetail.received_by:type_name -> docvault.DistributeUserDetail
	48, // 46: docvault.UpdateUserDisposalStatusReq.recycles:type_name -> docvault.RecycleList
	79, // 47: docvault.GetRecycleInfoResp.recycle_info:type_name -> docvault.RecycleInfo
	80, // 48: docvault.RecycleInfo.recycle_records:type_name -> docvault.RecycleRecord
	81, // 49: docvault.RecycleRecord.handover_persons:type_name -> docvault.HandoverPerson
	84, // 50: docvault.GetDisposalDetailResp.disposal_records:type_name -> docvault.DisposalRecord
	3,  // 51: docvault.InternalDocumentLibrary.Create:input_type -> docvault.InternalDocumentCreateReq
	6,  // 52: docvault.InternalDocumentLibrary.Get:input_type -> docvault.InternalDocumentGetReq
	9,  // 53: docvault.InternalDocumentLibrary.Page:input_type -> docvault.InternalDocumentPageReq
	5,  // 54: docvault.InternalDocumentLibrary.Change:input_type -> docvault.InternalDocumentChangeReq
	24, // 55: docvault.InternalDocumentLibrary.BatchCreate:input_type -> docvault.InternalDocumentBatchCreateReq
	12, // 56: docvault.Book.CreateBook:input_type -> docvault.CreateBookReq
	14, // 57: docvault.Book.GetBookList:input_type -> docvault.GetBookListReq
	15, // 58: docvault.Book.UpdateBook:input_type -> docvault.BookInfo
	18, // 59: docvault.Book.DeleteBook:input_type -> docvault.DeleteBookReq
	20, // 60: docvault.Book.ImportBook:input_type -> docvault.ImportBookReq
	26, // 61: docvault.ExternalDocumentLibrary.Create:input_type -> docvault.ExternalDocumentCreateReq
	30, // 62: docvault.ExternalDocumentLibrary.Get:input_type -> docvault.ExternalDocumentGetReq
	32, // 63: docvault.ExternalDocumentLibrary.Page:input_type -> docvault.ExternalDocumentPageReq
	35, // 64: docvault.ExternalDocumentLibrary.Change:input_type -> docvault.ExternalDocumentChangeReq
	39, // 65: docvault.ExternalDocumentLibrary.ImportCompanyPlagiarismCheck:input_type -> docvault.PlagiarismCheckReq
	36, // 66: docvault.ExternalDocumentLibrary.ImportGroupDocsToCompany:input_type -> docvault.ImportGroupDocsToCompanyReq
	40, // 67: docvault.DocumentLibrary.PreSaveDistributeRecord:input_type -> docvault.DocumentDistributeReq
	44, // 68: docvault.DocumentLibrary.GetDocPermissionUsers:input_type -> docvault.GetDocPermissionUsersReq
	46, // 69: docvault.DocumentLibrary.UpdateDistributeStatusByWorkflowId:input_type -> docvault.UpdateDistributeStatusReq
	56, // 70: docvault.DocumentLibrary.SaveDistributeApproval:input_type -> docvault.DistributeApprovalReq
	47, // 71: docvault.DocumentLibrary.SaveRecycleApprovalInfo:input_type -> docvault.RecycleApprovalInfo
	50, // 72: docvault.DocumentLibrary.SaveDisposalApprovalInfo:input_type -> docvault.DisposalApprovalInfo
	52, // 73: docvault.DocumentLibrary.GetDistributeInfoList:input_type -> docvault.GetDistributeListReq
	64, // 74: docvault.DocumentLibrary.GetDistributeApplicationById:input_type -> docvault.GetDistributeApplicationReq
	69, // 75: docvault.DocumentLibrary.DeleteDistributeRecord:input_type -> docvault.DeleteDistributeReq
	70, // 76: docvault.DocumentLibrary.GetDistributeDetail:input_type -> docvault.GetDistributeDetailReq
	77, // 77: docvault.DocumentLibrary.GetRecycleInfoByDistributeRecordFileId:input_type -> docvault.GetRecycleInfoReq
	77, // 78: docvault.DocumentLibrary.GetRecycleInfoByDistributeId:input_type -> docvault.GetRecycleInfoReq
	58, // 79: docvault.DocumentLibrary.CreateBorrowRecord:input_type -> docvault.BorrowRecordCreateReq
	60, // 80: docvault.DocumentLibrary.ModifyBorrowRecord:input_type -> docvault.BorrowRecordModifyReq
	61, // 81: docvault.DocumentLibrary.ModifyBorrowRecordStatus:input_type -> docvault.BorrowRecordStatusModifyReq
	62, // 82: docvault.DocumentLibrary.ModifyBorrowDocumentStatus:input_type -> docvault.BorrowDocumentStatusModifyReq
	63, // 83: docvault.DocumentLibrary.DeleteBorrowRecord:input_type -> docvault.BorrowRecordDeleteReq
	76, // 84: docvault.DocumentLibrary.UpdateUserDisposalStatus:input_type -> docvault.UpdateUserDisposalStatusReq
	82, // 85: docvault.DocumentLibrary.GetDisposalDetail:input_type -> docvault.GetDisposalDetailReq
	85, // 86: docvault.DocumentLibrary.UpdatePermissionUsedStatus:input_type -> docvault.UpdatePermissionUsedStatusReq
	4,  // 87: docvault.InternalDocumentLibrary.Create:output_type -> docvault.InternalDocumentCreateResp
	7,  // 88: docvault.InternalDocumentLibrary.Get:output_type -> docvault.InternalDocumentGetResp
	10, // 89: docvault.InternalDocumentLibrary.Page:output_type -> docvault.InternalDocumentPageResp
	0,  // 90: docvault.InternalDocumentLibrary.Change:output_type -> docvault.EmptyResp
	25, // 91: docvault.InternalDocumentLibrary.BatchCreate:output_type -> docvault.InternalDocumentBatchCreateResp
	13, // 92: docvault.Book.CreateBook:output_type -> docvault.CreateBookResp
	16, // 93: docvault.Book.GetBookList:output_type -> docvault.GetBookListResp
	17, // 94: docvault.Book.UpdateBook:output_type -> docvault.UpdateBookResp
	19, // 95: docvault.Book.DeleteBook:output_type -> docvault.DeleteBookResp
	22, // 96: docvault.Book.ImportBook:output_type -> docvault.ImportBookResp
	28, // 97: docvault.ExternalDocumentLibrary.Create:output_type -> docvault.ExternalDocumentCreateResp
	31, // 98: docvault.ExternalDocumentLibrary.Get:output_type -> docvault.ExternalDocumentGetResp
	33, // 99: docvault.ExternalDocumentLibrary.Page:output_type -> docvault.ExternalDocumentPageResp
	0,  // 100: docvault.ExternalDocumentLibrary.Change:output_type -> docvault.EmptyResp
	0,  // 101: docvault.ExternalDocumentLibrary.ImportCompanyPlagiarismCheck:output_type -> docvault.EmptyResp
	38, // 102: docvault.ExternalDocumentLibrary.ImportGroupDocsToCompany:output_type -> docvault.ImportGroupDocsToCompanyResp
	0,  // 103: docvault.DocumentLibrary.PreSaveDistributeRecord:output_type -> docvault.EmptyResp
	45, // 104: docvault.DocumentLibrary.GetDocPermissionUsers:output_type -> docvault.GetDocPermissionUsersResp
	0,  // 105: docvault.DocumentLibrary.UpdateDistributeStatusByWorkflowId:output_type -> docvault.EmptyResp
	0,  // 106: docvault.DocumentLibrary.SaveDistributeApproval:output_type -> docvault.EmptyResp
	0,  // 107: docvault.DocumentLibrary.SaveRecycleApprovalInfo:output_type -> docvault.EmptyResp
	0,  // 108: docvault.DocumentLibrary.SaveDisposalApprovalInfo:output_type -> docvault.EmptyResp
	53, // 109: docvault.DocumentLibrary.GetDistributeInfoList:output_type -> docvault.GetDistributeListResp
	65, // 110: docvault.DocumentLibrary.GetDistributeApplicationById:output_type -> docvault.GetDistributeApplicationResp
	0,  // 111: docvault.DocumentLibrary.DeleteDistributeRecord:output_type -> docvault.EmptyResp
	71, // 112: docvault.DocumentLibrary.GetDistributeDetail:output_type -> docvault.GetDistributeDetailResp
	78, // 113: docvault.DocumentLibrary.GetRecycleInfoByDistributeRecordFileId:output_type -> docvault.GetRecycleInfoResp
	78, // 114: docvault.DocumentLibrary.GetRecycleInfoByDistributeId:output_type -> docvault.GetRecycleInfoResp
	59, // 115: docvault.DocumentLibrary.CreateBorrowRecord:output_type -> docvault.BorrowRecordCreateResp
	0,  // 116: docvault.DocumentLibrary.ModifyBorrowRecord:output_type -> docvault.EmptyResp
	0,  // 117: docvault.DocumentLibrary.ModifyBorrowRecordStatus:output_type -> docvault.EmptyResp
	0,  // 118: docvault.DocumentLibrary.ModifyBorrowDocumentStatus:output_type -> docvault.EmptyResp
	0,  // 119: docvault.DocumentLibrary.DeleteBorrowRecord:output_type -> docvault.EmptyResp
	0,  // 120: docvault.DocumentLibrary.UpdateUserDisposalStatus:output_type -> docvault.EmptyResp
	83, // 121: docvault.DocumentLibrary.GetDisposalDetail:output_type -> docvault.GetDisposalDetailResp
	0,  // 122: docvault.DocumentLibrary.UpdatePermissionUsedStatus:output_type -> docvault.EmptyResp
	87, // [87:123] is the sub-list for method output_type
	51, // [51:87] is the sub-list for method input_type
	51, // [51:51] is the sub-list for extension type_name
	51, // [51:51] is the sub-list for extension extendee
	0,  // [0:51] is the sub-list for field type_name
}

func init() { file_internal_infrastructure_adapter_grpc_protos_docvault_proto_init() }
func file_internal_infrastructure_adapter_grpc_protos_docvault_proto_init() {
	if File_internal_infrastructure_adapter_grpc_protos_docvault_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDesc), len(file_internal_infrastructure_adapter_grpc_protos_docvault_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   86,
			NumExtensions: 0,
			NumServices:   4,
		},
		GoTypes:           file_internal_infrastructure_adapter_grpc_protos_docvault_proto_goTypes,
		DependencyIndexes: file_internal_infrastructure_adapter_grpc_protos_docvault_proto_depIdxs,
		MessageInfos:      file_internal_infrastructure_adapter_grpc_protos_docvault_proto_msgTypes,
	}.Build()
	File_internal_infrastructure_adapter_grpc_protos_docvault_proto = out.File
	file_internal_infrastructure_adapter_grpc_protos_docvault_proto_goTypes = nil
	file_internal_infrastructure_adapter_grpc_protos_docvault_proto_depIdxs = nil
}
