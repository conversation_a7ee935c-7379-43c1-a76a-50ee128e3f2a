syntax = "proto3";

package docvault;
option go_package = "./docvault";

message EmptyResp {}

message ApprovalInfoItem {
  string user_id = 1;
  int64 passed_date = 2;
  string Nickname = 3;
}

message ApprovalInfo {
  repeated ApprovalInfoItem auditors = 1;
  repeated ApprovalInfoItem approvers = 2;
}

message InternalDocumentCreateReq {
  string no_prefix = 1;
  string name = 2;
  string file_id = 3;
  string doc_category_id = 4;
  string department_id = 5;
  int32 version_no = 8;
  string author_id = 9;
  int64 publish_date = 10;
  int64 effective_date = 11;
  string original_no = 12;
  string original_version_no = 13;
}

message InternalDocumentCreateResp {
  string id = 1;
}

message InternalDocumentChangeReq {
  string id = 1;
  string name = 2; // 文件名称
  string file_id = 3; // 文件id/文件名称
  string doc_category_id = 4; // 文件类别
  string department_id = 5; // 编制部门
  int32 version_no = 7; // 版本号
  int64 publish_date = 8; // 发布日期
  int64 effective_date = 9; // 实施日期
  string original_no = 10; // 原文件编号
  string original_version_no = 11; // 原文件版本
  string author_id = 12; // 编制人
  string no_prefix = 13; // 编号前缀
}



message InternalDocumentGetReq {
  string id = 1;
}

message InternalDocumentGetResp {
  string id = 1;
  string no = 2;
  string version_no = 3;
  string original_no = 4;
  string original_version_no = 5;
  string name = 6;
  string doc_category_id = 7;
  string department_id = 8;
  string author_id = 9;
  ApprovalInfo approval_info = 10;
  int64 publish_date = 11;
  int64 effective_date = 12;
  int32 status = 13;  // 1- 即将作废 2- 即将实施 3- 有效 4- 拟修订
  string no_prefix = 14;
  string file_id = 15;
}

message PageInfo {
  int32 page = 1;
  int32 page_size = 2;
  bool no_page = 3;
}

message InternalDocumentPageReq {
  PageInfo page_info = 1; 
  string search = 2;
  repeated string ids = 3;
  // 类别ids 
  repeated string doc_category_ids = 4;
  // 部门ids
  repeated string department_ids = 5;
  // 状态 1- 即将作废 2- 即将实施 3- 有效 4- 拟修订
  int32 status = 6;
  // 是否有附件
  int32 has_attachment = 7; // 0-全部，1-有附件，2-无附件
  string name = 8; // 文件名称
  string no = 9; // 文件编号
  string original_no = 10; // 原文件编号
}

message InternalDocumentPageResp {
  repeated InternalDocumentPageItem data = 1;
  int64 total = 2;
}

message InternalDocumentPageItem {
  string id = 1;
  string no = 2;
  string version_no = 3;
  string original_no = 4;
  string original_version_no = 5;
  string name = 6;
  string doc_category_id = 7;
  string department_id = 8;
  string author_id = 9;
  ApprovalInfo approval_info = 10;
  int64 publish_date = 11;
  int64 effective_date = 12;
  int32 status = 13;  // 1- 即将作废 2- 即将实施 3- 有效 4- 拟修订
}

message CreateBookReq {
  string dictionary_node_id = 1;
  string name = 2;
  string author = 3;
  string publisher = 4;
  int32 register_count = 5;
  string book_type = 6;
  string user_id = 7;
  string organization_id = 8;
  string organization_code = 9;
  string book_number = 10;
  string file_id = 11;
}

message CreateBookResp {
  string id = 1;
}

message GetBookListReq {
  PageInfo page_info = 1;
  string number = 2;
  string name = 3;
  string author = 4;
  string publisher = 5;
  repeated string dictionary_node_ids = 6;
  string on_borrow = 7;
  string organization_id = 8;
}

message BookInfo {
  string id = 1;                  // 主键id
  int32 status = 2;               // 状态（0未启用 | 1启用）
  int32 is_delete = 3;            // 是否删除（0未删除 | 1已删除）
  string number = 4;              // 书籍编号
  string name = 5;                // 书籍名称
  string author = 6;              // 作者/编者
  string publisher = 7;           // 出版社
  string book_type = 8;           // 书籍类型
  int32 register_count = 9;       // 总在册数
  int32 receive_count = 10;       // 领用数
  int32 borrow_count = 11;        // 借用数
  bool on_borrow = 12;            // 借用状态(false未借用 | true借用中)
  int32 surplus_count = 13;       // 剩余数
  int64 created_time = 14;       // 创建时间（字符串格式，或使用 Timestamp）
  int64 updated_time = 15;       // 更新时间（字符串格式，或使用 Timestamp）
  string created_by = 16;         // 创建人
  string updated_by = 17;         // 修改人
  string dictionary_node_id = 18; // 类型id
  string file_id = 19;            // 书籍文件id
  string organization_id = 20;    // 所属组织
}

message GetBookListResp {
  int32 total = 1;
  repeated BookInfo data = 2;
}

message UpdateBookResp {
  int32 code = 1;
  string msg = 2;
}

message DeleteBookReq {
  string id = 1;
}

message DeleteBookResp {
  int32 code = 1;
  string msg = 2;
}

message ImportBookReq {
  repeated ImportBookInfo import_book_info = 1;
}

message ImportBookInfo {
  string dictionary_node_id = 1;
  string name = 2;
  string author = 3;
  string publisher = 4;
  int32 register_count = 5;
  string book_type = 6;
  string number = 7;
  string file_id = 8;
}

message ImportBookResp {
  repeated ImportRespInfo data = 1;
}

message ImportRespInfo {
  string dictionary_node_id = 1;
  string book_id = 2;
}

message InternalDocumentBatchCreateReq {
  repeated InternalDocumentCreateReq internal_document_create_reqs = 1;
}

message InternalDocumentBatchCreateResp {
  repeated InternalDocumentGetResp internal_document_infos = 1;
}

message ExternalDocumentCreateReq {
  repeated ExternalDocumentCreateInfo data = 1;
  int32 org_type= 2;
  string org_id = 3;
}

message ExternalDocumentCreateInfo {
  string type_dictionary_node_id = 1;
  string domain_dictionary_node_id = 2;
  repeated string authentication_dictionary_node_ids = 3;
  string number_prefix = 4;
  string doc_type = 5;
  string domain = 6;
  repeated string authentications = 7;
  string name = 8;
  string original_doc_number = 9;
  string publish_doc_number = 10;
  string publish_department = 11;
  string file_id = 12;
  int64 publish_date = 13;
  int64 effective_date = 14;
  string Original_number = 15;
  string Original_version = 16;
}

message ExternalDocumentCreateResp {
  repeated ExternalDocumentCreateRespList data = 1;
}

message ExternalDocumentCreateRespList {
  string id = 1;
  string type_dictionary_node_id = 2;
  string domain_dictionary_node_id = 3;
  repeated string authentication_dictionary_node_ids = 4;
}

message ExternalDocumentGetReq {
  string id = 1;
}

message ExternalDocumentGetResp {
  string id = 1;
  string number = 2;
  string version = 3;
  string original_number = 4;
  string original_version = 5;
  string name = 6;
  string doc_type = 7;
  string domain = 8;
  string original_doc_number = 9;
  string publish_doc_number = 10;
  string publish_department = 11;
  int64 publish_date = 12;
  int64 effective_date = 13;
  string Authentication = 14;
  int32 status = 15;
  string type_dictionary_node_id = 16;
  string domain_dictionary_node_id = 17;
  repeated string authentication_dictionary_node_ids = 18;
  string file_id = 19;
}

message ExternalDocumentPageReq {
  PageInfo page_info = 1;
  string number = 2;
  string name = 3;
  string original_number = 4;
  string original_docNumber = 5;
  string publish_doc_number = 6;
  string publish_department = 7;
  repeated string type_dictionary_node_ids = 8;
  string domain_dictionary_node_id = 9;
  string authentication_dictionary_node_id = 10;
  string be_attached_file = 11;
  int32 status = 12;
  int32 org_type = 13;
  string org_id = 14;
}

message ExternalDocumentPageResp {
  repeated ExternalDocumentPageInfo data = 1;
  int64 total = 2;
}

message ExternalDocumentPageInfo {
  string id = 1;
  string number = 2;
  string version = 3;
  string original_number = 4;
  string original_version = 5;
  string name = 6;
  string doc_type = 7;
  string domain = 8;
  string original_doc_number = 9;
  string publish_doc_number = 10;
  string publish_department = 11;
  ApprovalInfo approval_info = 12;
  int64 publish_date = 13;
  int64 effective_date = 14;
  string Authentication = 15;
  int32 status = 16;
  string type_dictionary_node_id= 17;
  string domain_dictionary_node_id = 18;
  repeated string authentication_dictionary_node_ids = 19;
  string file_id = 20;
}

message ExternalDocumentChangeReq {
  string id = 1;
  string name = 2;
  string original_doc_number = 3;
  string publish_doc_number = 4;
  string publish_department = 5;
  string type_dictionary_node_id = 6;
  string domain_dictionary_node_id = 7;
  repeated string authentication_dictionary_node_ids = 8;
  int64 publish_date = 9;
  int64 effective_date = 10;
  string original_number = 11;
  string original_version = 12;
  string file_id = 13;
  string number_prefix = 14;
  string doc_type = 15;
  repeated string authentications = 16;
  string domain = 17;
}

message ImportGroupDocsToCompanyReq {
  repeated ImportGroupDocsToCompanyInfo data = 1;
  string org_code = 2;
}

message ImportGroupDocsToCompanyInfo {
  string id = 1;
  string original_number = 2;
  string original_version = 3;
  ApprovalInfo approval_info = 4; // 审批信息
}

message ImportGroupDocsToCompanyResp {

}

message PlagiarismCheckReq {
  repeated string ids = 1;
}

message DocumentDistributeReq {
  string id = 1;                         // 文档id
  string applicant = 2;                  // 申请人
  int64 apply_date = 3;                  // 申请日期(时间戳)
  int32 distribute_type = 4;             // 分发类型
  int32 file_type = 5;                   // 文件类型
  string type_dict_node_id = 6;          // 类型字典节点ID
  string reason = 7;                     // 原因
  string other_reason = 8;               // 其他原因(当reason_dict_node_id为"其他"时使用)
  int64 wish_distribute_date = 9;        // 希望分发日期(时间戳)
  repeated DistributeList distribute_list = 10; // 发放清单
  string fileCategory = 11;              // 文件类别
  string workflow_id = 12;              // 流程id
  int32 save_method = 13;                  // 保存方式
}

message DistributeList {
  string file_id = 1;                    // 文件id
  string file_name = 2;                  // 文件名
  string number = 3;                     // 文件编号
  string version = 4;                    // 文件版本
  repeated Permission permissions = 5;   // 权限列表
}

message Permission {
  int32 file_form = 1;                   // 文件形式
  int32 file_permission = 2;             // 文件权限
  string recipient = 3;                  // 接收方
  repeated Recipient received_by = 4;    // 接收人
}

message Recipient {
  string user_id = 1;                    // 用户ID
  string user_name = 2;                  // 用户名
}

message GetDocPermissionUsersReq {
  string file_id = 1;
  int32 file_form = 2;
  int32 file_permission = 3;
}

message GetDocPermissionUsersResp {
  repeated string wait_for_approval_users = 1; // 待审批用户
  repeated string not_recycled_users = 2; // 未回收用户
  repeated string recycle_flow_users = 3; // 回收流程中用户
}

message UpdateDistributeStatusReq {
  string workflow_id = 1;
  int32 status = 2;
}

message RecycleApprovalInfo {
  string distribute_id = 1;              // 发放信息id
  int64 recycle_date = 2;                // 回收日期
  string recycle_reason = 3;             // 回收原因
  string other_reason = 4;               // 其他原因
  repeated RecycleList recycle_list = 5; // 回收清单
  string workflow_id = 6; // 流程id
  ApprovalInfo approval_info = 7; // 审批信息
}

message RecycleList {
  string inventory_id = 1;     // 清单列表ID
  repeated FilePermission permissions = 2; // 文件权限
}

message FilePermission {
  int32 file_form = 1;       // 文件形式
  int32 file_permission = 2; // 文件权限
  repeated string received_by = 3; // 接收人
}

message DisposalApprovalInfo {
  string distribute_id = 1;     // 发放信息id
  int64 disposal_date = 2;     // 处置日期
  string disposal_reason = 3;  // 处置方式
  repeated DisposalList disposal_list = 4; // 处置清单
  string workflow_id = 5; // 流程id
  ApprovalInfo approval_info = 6; // 审批信息
}

message DisposalList {
  string inventory_id = 1;     // 清单列表ID
  repeated FilePermission permissions = 2; // 文件权限
}

// 获取分发列表请求
message GetDistributeListReq {
  PageInfo page_info = 1;          // 分页信息
  string file_number = 2;          // 文件编号
  string file_name = 3;            // 文件名称
  int32 file_type = 4;             // 文件类型
  repeated string file_category = 5;   // 文件类别
  int32 distribute_type = 6;       // 发放类型
  int32 status = 7;                // 状态（1待提交 | 2待审批 | 3已审批 | 4已驳回）
  repeated string applicant = 8;   // 申请人
}

message GetDistributeListResp {
  int64 total = 1;                   // 总记录数
  repeated GetDistributeListInfo data = 2; // 数据列表
}

message GetDistributeListInfo {
  string id = 1;                     // 主键id
  string applicant = 2;              // 申请人
  int64 apply_date = 3;              // 申请时间
  int32 distribute_type = 4;         // 发放类型（1内部发放 | 2外部发放）
  int32 file_type = 5;               // 文件类型（1内部文件 | 2外部文件）
  string file_category = 6;          // 文件类别
  string reason = 7;                 // 发放原因
  string other_reason = 8;           // 其他原因
  int64 wish_distribute_date = 9;    // 期望发放时间
  int32 status = 10;                 // 状态（1待提交 | 2待审批 | 3已审批 | 4已驳回）
  ApprovalInfo approval_info = 11;  // 审批信息
  string workflow_id = 12;          // 流程id
  int32 distribute_count = 13; // 发放份数
  repeated DistributeUser received = 14; // 已接收人
  repeated DistributeUser not_received = 15; // 未接收人
  repeated DistributeUser recycle = 16; // 已回收人
  repeated DistributeUser disposal_by = 17; // 已处置人
}

message DistributeUser {
  string user_id = 1;               // 用户id
  int32 file_form = 2;              // 文件形式（1电子文件 | 2纸质文件）
  string nickname = 3;              // 用户昵称
}

message DistributeApprovalReq {
  string workflow_id = 1; // 流程id
  int32 status = 2; // 审批状态
  int32 sign_for_status = 3;
  int32 dispose_status = 4;
  ApprovalInfo approval_info = 5;  // 审批信息
}

// 借阅文档项
message BorrowDocumentItem {
  string document_id = 1;     // 文档ID
  string version_no = 2;      // 文档版本号
  int32 module_type = 3;      // 文档所属模块，1书籍 | 2内部文档 | 3外部文档
  int32 borrow_status = 4;    // 文档借阅状态，1借阅中 | 2回收中 | 3已回收
  string recover_user_id = 5; // 回收人ID
  int64 recover_time = 6;     // 回收时间
}

// 创建借阅记录请求
message BorrowRecordCreateReq {
  string user_id = 1;                          // 借阅用户ID（必传）
  repeated BorrowDocumentItem documents = 2;   // 借阅的文档列表（必传）
  int64 due_time = 3;                          // 应还时间（必传）
  int32 borrow_reason_type = 4;                // 借阅原因类型（必传）1项目参考/研究 | 2问题调查/分析 | 3审计/检查准备 | 4培训/学习需要 | 5其他
  string borrow_other_reason = 5;              // 借阅原因描述（选择"其他"时必传，其他情况可选）
  int64 borrow_time = 6;                       // 借阅时间（可选，系统自动生成）
  int64 borrow_apply_time = 7;                 // 申请时间（可选，系统自动生成）
  int32 approval_status = 8;                   // 审批状态（可选，系统自动设置）
  ApprovalInfo approval_info = 9;              // 审批信息（可选）
  int64 approval_apply_time = 10;              // 审批申请时间（可选，系统自动生成）
}

// 创建借阅记录响应
message BorrowRecordCreateResp {
  string id = 1; // 借阅记录ID
}

// 修改借阅记录请求
message BorrowRecordModifyReq {
  string borrow_record_id = 1;                    // 借阅记录ID（必传）
  string user_id = 2;                             // 借阅用户ID（可选）
  repeated BorrowDocumentItem documents = 3;      // 借阅的文档列表（可选）
  int64 borrow_time = 4;                          // 借阅时间（可选）
  int64 due_time = 5;                             // 应还时间（可选）
  int32 borrow_reason_type = 6;                   // 借阅原因类型（可选）1项目参考/研究 | 2问题调查/分析 | 3审计/检查准备 | 4培训/学习需要 | 5其他
  string borrow_other_reason = 7;                 // 借阅原因描述（选择"其他"时必传，其他情况可选）
  int64 borrow_apply_time = 8;                    // 申请时间（可选）
  int32 approval_status = 9;                      // 审批状态（可选）
  ApprovalInfo approval_info = 10;                      // 审批信息（可选）
  int64 approval_apply_time = 11;                 // 审批申请时间（可选）
}

// 修改借阅记录状态请求
message BorrowRecordStatusModifyReq {
  string borrow_record_id = 1;  // 借阅记录ID（必传）
  int32 approval_status = 2;    // 新的审批状态（必传）1待审批 | 2已审批 | 3已驳回
}

// 修改借阅文档状态请求
message BorrowDocumentStatusModifyReq {
  string borrow_record_id = 1;  // 借阅记录ID（必传）
  repeated string document_ids = 2; // 文档ID列表（必传）
  int32 borrow_status = 3;      // 新的借阅状态（必传）1借阅中 | 2回收中 | 3已回收
}

// 删除借阅记录请求
message BorrowRecordDeleteReq {
  string borrow_record_id = 1;  // 借阅记录ID（必传）
}

message GetDistributeApplicationReq {
  string id = 1;  // 发放列表id
}

// 获取发放清单响应
message GetDistributeApplicationResp {
  repeated DistributeInventory data = 1; // 发放清单
}

// 发放清单项
message DistributeInventory {
  string id = 1;           // 发放清单id
  string file_id = 2;      // 文档id
  string file_name = 3;    // 文档名称
  string number = 4;       // 文档编号
  string version = 5;      // 文档版本
  repeated PermissionResp permissions = 6; // 权限列表
  string recipient = 7; // 接收方
}

// 权限响应
message PermissionResp {
  int32 file_form = 1;             // 文件形式（1电子文件 | 2纸质文件）
  int32 file_permission = 2;       // 文件权限（1查阅 | 2查阅/下载 | 3一次下载）
  repeated ReceivedBy received_by = 3; // 接收人
}

// 接收人信息
message ReceivedBy {
  string user_id = 1;      // 用户id
  string nickname = 2;     // 用户昵称
  int32 status = 3;        // 状态（1未回收 | 2回收审批中 | 3已回收）\
  int64 recycle_date = 4; // 回收日期
}

message DeleteDistributeReq {
  string id = 1; // 发放列表id
}
// 获取发放详情请求
message GetDistributeDetailReq {
  string id = 1;  // 发放记录ID
}

// 获取发放详情响应
message GetDistributeDetailResp {
  DistributeDetailInfo detail = 1;  // 发放详情信息
}

// 发放详情信息
message DistributeDetailInfo {
  // 基本信息
  string id = 1;                              // 发放记录ID
  string workflow_id = 2;                     // 流程ID
  string applicant = 3;                       // 申请人ID
  string applicant_name = 4;                  // 申请人姓名
  int64 apply_date = 5;                       // 申请日期

  // 类型信息
  int32 distribute_type = 6;                  // 发放类型（1内部发放 | 2外部发放）
  int32 file_type = 7;                        // 文件类型（1内部文件 | 2外部文件）
  string file_category = 8;                   // 文件类别
  string type_dict_node_id = 9;               // 类型字典节点ID

  // 原因信息
  string reason = 10;                         // 发放原因
  string other_reason = 11;                   // 其他原因

  // 日期和状态
  int64 wish_distribute_date = 12;            // 期望发放日期
  int32 status = 13;                          // 状态（1待提交 | 2待审批 | 3已审批 | 4已驳回）

  // 发放清单
  repeated DistributeInventoryDetail distribute_list = 14; // 发放清单

  // 时间戳
  int64 created_at = 15;                      // 创建时间
  int64 updated_at = 16;                      // 更新时间
}

// 发放清单详情
message DistributeInventoryDetail {
  string id = 1;                              // 发放清单ID
  string file_id = 2;                         // 文件ID
  string file_name = 3;                       // 文件名称
  string number = 4;                          // 文件编号
  string version = 5;                         // 文件版本
  repeated PermissionDetail permissions = 6;  // 权限详情列表
}

// 权限详情信息
message PermissionDetail {
  int32 file_form = 1;                        // 文件形式（1电子文件 | 2纸质文件）
  int32 file_permission = 2;                  // 文件权限（1查询 | 2查询/下载 | 3一次下载）
  string recipient = 3;                       // 接收方
  repeated DistributeUserDetail received_by = 4; // 接收人详情列表
}

// 用户详情信息
message DistributeUserDetail {
  string user_id = 1;                         // 用户ID
  string user_nickname = 2;                   // 用户姓名
  int32 recycle_status = 3;                   // 回收状态（0未回收 | 1已回收 | 2待审批）
  int64 recycle_time = 4;                     // 回收时间（Unix时间戳）
}

message UpdateUserDisposalStatusReq {
  string distribute_id = 1; // 发放记录ID
  int32 disposal_status = 2; // 处置状态
  repeated RecycleList  recycles = 3; // 回收列表
}

// 内部文档库
service InternalDocumentLibrary {
  // 创建文档
  rpc Create(InternalDocumentCreateReq) returns (InternalDocumentCreateResp);
  // 获取文档
  rpc Get(InternalDocumentGetReq) returns (InternalDocumentGetResp);
  // 分页查询文档
  rpc Page(InternalDocumentPageReq) returns (InternalDocumentPageResp);
  // 修订文档
  rpc Change(InternalDocumentChangeReq) returns (EmptyResp);

  // 批量创建文档
  rpc BatchCreate(InternalDocumentBatchCreateReq) returns (InternalDocumentBatchCreateResp);
}

// 书籍操作
service Book {
  // 新增书籍信息
  rpc CreateBook(CreateBookReq) returns (CreateBookResp);
  // 查询书籍列表
  rpc GetBookList(GetBookListReq) returns (GetBookListResp);
  // 修改书籍信息
  rpc UpdateBook(BookInfo) returns (UpdateBookResp);
  // 删除书籍信息
  rpc DeleteBook(DeleteBookReq) returns (DeleteBookResp);
  // 批量导入书籍信息
  rpc ImportBook(ImportBookReq) returns (ImportBookResp);
}

service ExternalDocumentLibrary {
  // 批量创建文档
  rpc Create(ExternalDocumentCreateReq) returns (ExternalDocumentCreateResp);
  // 获取文档
  rpc Get(ExternalDocumentGetReq) returns (ExternalDocumentGetResp);
  // 分页查询文档
  rpc Page(ExternalDocumentPageReq) returns (ExternalDocumentPageResp);
  // 修订文档
  rpc Change(ExternalDocumentChangeReq) returns (EmptyResp);
  // 导入集团文档到公司验重
  rpc ImportCompanyPlagiarismCheck(PlagiarismCheckReq) returns (EmptyResp);
  // 导入集团文档到公司
  rpc ImportGroupDocsToCompany(ImportGroupDocsToCompanyReq) returns (ImportGroupDocsToCompanyResp);
}

service DocumentLibrary {
  // 保存发放记录
  rpc PreSaveDistributeRecord(DocumentDistributeReq) returns (EmptyResp);
  // 获取文档权限用户
  rpc GetDocPermissionUsers(GetDocPermissionUsersReq) returns (GetDocPermissionUsersResp);
  // 更新发放状态
  rpc UpdateDistributeStatusByWorkflowId(UpdateDistributeStatusReq) returns (EmptyResp);
  // 保存发放审批信息
  rpc SaveDistributeApproval(DistributeApprovalReq) returns (EmptyResp);
  // 保存回收审批信息
  rpc SaveRecycleApprovalInfo(RecycleApprovalInfo) returns (EmptyResp);
  // 保存处置审批信息
  rpc SaveDisposalApprovalInfo(DisposalApprovalInfo) returns (EmptyResp);
  // 获取发放信息列表
  rpc GetDistributeInfoList(GetDistributeListReq) returns (GetDistributeListResp);
  // 根据发放列表id获取发放清单信息
  rpc GetDistributeApplicationById(GetDistributeApplicationReq) returns (GetDistributeApplicationResp);
  // 删除发放记录
  rpc DeleteDistributeRecord(DeleteDistributeReq) returns (EmptyResp);
 // 获取发放回收详情
  rpc GetDistributeDetail(GetDistributeDetailReq) returns (GetDistributeDetailResp);
  // 根据发放清单ID查询回收信息
  rpc GetRecycleInfoByDistributeRecordFileId(GetRecycleInfoReq) returns (GetRecycleInfoResp);
  rpc GetRecycleInfoByDistributeId(GetRecycleInfoReq) returns (GetRecycleInfoResp);

   // 创建借阅记录
  rpc CreateBorrowRecord(BorrowRecordCreateReq) returns (BorrowRecordCreateResp);
  // 修改借阅记录
  rpc ModifyBorrowRecord(BorrowRecordModifyReq) returns (EmptyResp);
  // 修改借阅记录状态
  rpc ModifyBorrowRecordStatus(BorrowRecordStatusModifyReq) returns (EmptyResp);
  // 修改借阅文档状态
  rpc ModifyBorrowDocumentStatus(BorrowDocumentStatusModifyReq) returns (EmptyResp);
  // 删除借阅记录
  rpc DeleteBorrowRecord(BorrowRecordDeleteReq) returns (EmptyResp);
  // 更新用户处置状态
  rpc UpdateUserDisposalStatus(UpdateUserDisposalStatusReq) returns (EmptyResp);
  // 根据发放清单ID查询处置详情
  rpc GetDisposalDetail(GetDisposalDetailReq) returns (GetDisposalDetailResp);
  // 更新权限使用状态
  rpc UpdatePermissionUsedStatus(UpdatePermissionUsedStatusReq) returns (EmptyResp);
}

// 根据发放清单ID查询回收信息请求
message GetRecycleInfoReq {
  string distribute_record_file_id = 1;  // 发放记录文件ID
}

// 根据发放清单ID查询回收信息响应
message GetRecycleInfoResp {
  RecycleInfo recycle_info = 1;  // 回收信息
}

// 回收信息
message RecycleInfo {
  string file_name = 1;                           // 文件名称
  string file_number = 2;                         // 文件编号
  repeated RecycleRecord recycle_records = 3;     // 回收记录列表
}

// 回收记录
message RecycleRecord {
  string recycle_initiator = 1;                   // 回收发起人
  string recycle_reason = 2;                      // 回收原因
  repeated HandoverPerson handover_persons = 3;   // 交还人信息列表
  repeated string auditors = 4;                   // 审批人列表
  repeated string approvers = 5;                  // 批准人列表
  int64 recycle_date = 6;                         // 回收日期（毫秒级时间戳）
}

// 交还人信息
message HandoverPerson {
  string handover_id = 1;                         // 交还人ID
  string handover_name = 2;                       // 交还人名字
  int32 file_form = 3;                            // 文件形式（1电子文件 | 2纸质文件）
  int32 file_permission = 4;                      // 文件权限（1查询 | 2查询/下载 | 3一次下载）
}

// 根据发放清单ID查询处置详情请求
message GetDisposalDetailReq {
  string distribute_record_file_id = 1;  // 发放记录文件ID
}

// 查询处置详情响应
message GetDisposalDetailResp {
  string file_name = 1;                           // 文件名称
  string file_number = 2;                         // 文件编号
  repeated DisposalRecord disposal_records = 3;   // 处置记录列表
}

// 处置记录
message DisposalRecord {
  string handover_person = 1;                     // 交还人
  int64 handover_date = 2;                        // 交还日期
  string recycle_person = 3;                      // 回收人
  int64 recycle_date = 4;                         // 回收日期
  string disposal_person = 5;                     // 处置人
  int64 disposal_date = 6;                        // 处置日期
  string disposal_method = 7;                     // 处置方式
}

// 更新权限使用状态请求
message UpdatePermissionUsedStatusReq {
  string distribute_record_file_id = 1;          // 发放记录文件ID（清单ID）
  int32 file_form = 2;                           // 文件形式（1电子文件 | 2纸质文件）
  int32 file_permission = 3;                     // 文件权限（1查阅 | 2查阅/下载 | 3一次下载）
  string user_id = 4;                            // 用户ID
  bool is_used = 5;                              // 是否已使用（true已使用 | false未使用）
}

