package clientx

import (
	"context"
	"nebula/internal/infrastructure/adapter/clientx/entity"
)

//go:generate mockgen -source=phoenix.go -destination=mock_phoenix.go -package=clientx

// PhoenixClient 定义了与 Phoenix 服务交互的接口
type PhoenixClient interface {
	// 查询用户昵称
	GetUserNicknames(ctx context.Context, uids []string) (map[string]string, error)

	// 查询文件信息
	GetFileInfo(ctx context.Context, fileId string) (entity.FileInfo, error)

	// 获取审批流信息
	GetWorkflow(ctx context.Context, workflowId string) (progress entity.GetWorkflowRespData, err error)

	// 获取组织信息
	GetOrganizationInfo(ctx context.Context, orgId string) (organizationInfo entity.OrganizationInfo, err error)

	// 批量获取部门信息
	GetOrganizationInfoByCodes(ctx context.Context, codes []string) ([]entity.OrganizationInfo, error)

	// 批量获取用户信息
	GetUserInfoByMobiles(ctx context.Context, mobiles []string) ([]entity.UserInfo, error)

	// UploadFile 根据本地文件路径上传文件，可指定文件名，返回 fileId 和错误信息
	UploadFile(ctx context.Context, filePath, fileName string) (fileId string, err error)

	// GetAllOrganizationsAndUsersByOrgId 获取指定组织下的所有组织和用户，树状
	GetAllOrganizationsAndUsersByOrgId(ctx context.Context, orgID string, descendantNodeTypes ...int) (resp entity.OrganizationUserTree, err error)

	// GetUserInfoByNickname 获取用户信息
	GetUserInfoByNickname(ctx context.Context, nickname string) (resp []entity.UserInfo, err error)

	// CheckUserHasRoleCode 检查用户是否包含指定角色代码
	CheckUserHasRoleCode(ctx context.Context, userId, roleCode string) (bool, error)

	// GenerateWatermarkedFile 生成水印文件
	// 功能: 为指定文件添加水印并生成新文件
	// 参数:
	//   - ctx: 上下文
	//   - req: 生成水印文件请求参数
	// 返回值:
	//   - info: 生成的水印文件信息，包含新文件ID和预览URL
	//   - err: 错误信息
	GenerateWatermarkedFile(ctx context.Context, req entity.GenerateWatermarkedFileReq) (entity.GenerateWatermarkedFileInfo, error)
}
