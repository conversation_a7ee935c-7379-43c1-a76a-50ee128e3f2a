package workflow

import (
	"context"
	"encoding/json"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"

	"github.com/zeromicro/go-zero/core/logc"
)

// BorrowApprovalConsumer 借阅审批
type BorrowApprovalConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewBorrowApprovalConsumer(svcCtx *svc.ServiceContext) *BorrowApprovalConsumer {
	return &BorrowApprovalConsumer{
		svcCtx: svcCtx,
	}
}

func (h *BorrowApprovalConsumer) Name() string {
	return "file_borrow"
}

func (h *BorrowApprovalConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleApproved(ctx, msg)
	return nil
}

// handleApproved 处理借阅审批事件
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
//
// 功能: 根据不同的事件类型处理借阅审批状态更新
func (h *BorrowApprovalConsumer) handleApproved(ctx context.Context, msg WorkflowEventMessage) {
	// 实现步骤:
	// 1. 根据事件类型进行不同的处理
	// 2. 审批驳回时更新状态为已驳回
	// 3. 审批撤销时更新状态为待提交
	// 4. 审批通过时处理通过逻辑

	switch msg.EventType {
	case consts.WorkflowEventRejected:
		// 审批驳回，更新状态为已驳回
		h.handleRejected(ctx, msg)

	case consts.WorkflowEventCanceled:
		// 审批撤销，更新状态为待提交
		h.handleCanceled(ctx, msg)

	case consts.WorkflowEventPassed:
		// 审批通过
		h.handlePassed(ctx, msg)

	default:
		// 未知事件类型，记录日志
		logc.Errorf(ctx, "未知的工作流事件类型: %s", msg.EventType)
	}
}

// handleRejected 处理审批驳回事件
// 功能: 将借阅记录状态更新为已驳回
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *BorrowApprovalConsumer) handleRejected(ctx context.Context, msg WorkflowEventMessage) {
	// 1. 调用docvault服务更新借阅记录状态为已驳回(4)
	_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyBorrowRecordStatus(ctx, &docvault.BorrowRecordStatusModifyReq{
		BorrowRecordId: msg.BusinessID,
		ApprovalStatus: 4, // 已驳回
	})
	if err != nil {
		logc.Errorf(ctx, "处理借阅审批驳回失败: %v", err)
		return
	}
	logc.Infof(ctx, "借阅记录 %s 审批驳回处理完成", msg.BusinessID)
}

// handleCanceled 处理审批撤销事件
// 功能: 将借阅记录状态更新为待审批
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *BorrowApprovalConsumer) handleCanceled(ctx context.Context, msg WorkflowEventMessage) {
	// 1. 调用docvault服务更新借阅记录状态为暂存/待提交(1)
	_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyBorrowRecordStatus(ctx, &docvault.BorrowRecordStatusModifyReq{
		BorrowRecordId: msg.BusinessID,
		ApprovalStatus: 1, // 暂存/待提交
	})
	if err != nil {
		logc.Errorf(ctx, "处理借阅审批撤销失败: %v", err)
		return
	}
	logc.Infof(ctx, "借阅记录 %s 审批撤销处理完成", msg.BusinessID)
}

// handlePassed 处理审批通过事件
// 功能: 将借阅记录状态更新为已审批，并保存审批信息
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *BorrowApprovalConsumer) handlePassed(ctx context.Context, msg WorkflowEventMessage) {
	// 1. 获取审批信息
	approvalInfo, err := GetApproval(ctx, h.svcCtx, msg)
	if err != nil {
		logc.Errorf(ctx, "获取借阅审批信息失败: %v", err)
		return
	}

	// 2. 调用docvault服务更新借阅记录状态为已审批(2)并保存审批信息
	approvalStatus := int32(2) // 已审批
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyBorrowRecord(ctx, &docvault.BorrowRecordModifyReq{
		BorrowRecordId: msg.BusinessID,
		ApprovalStatus: approvalStatus,
		ApprovalInfo:   approvalInfo,
	})
	if err != nil {
		logc.Errorf(ctx, "处理借阅审批通过失败: %v", err)
		return
	}
	logc.Infof(ctx, "借阅记录 %s 审批通过处理完成", msg.BusinessID)
}
