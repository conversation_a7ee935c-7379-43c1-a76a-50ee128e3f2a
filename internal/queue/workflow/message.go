package workflow

import (
	"fmt"

	"github.com/bytedance/sonic"
)

type WorkflowEventMessage struct {
	TenantID       string `json:"tenant_id"`       // 租户ID
	OrganizationID string `json:"organization_id"` // 组织架构ID
	WorkflowID     string `json:"workflow_id"`     // 工作流ID
	SponsorID      string `json:"sponsor_id"`      // 发起人ID
	FormContent    string `json:"form_content"`    // 审批表单信息（JSON字符串）
	CompletedAt    int64  `json:"completed_at"`    // 完成时间（毫秒级时间戳）
	CreatedAt      int64  `json:"created_at"`      // 发起时间（毫秒级时间戳）
	BusinessID     string `json:"business_id"`     // 流程序列号
	BusinessCode   string `json:"business_code"`   // 业务代码
	EventType      string `json:"event_type"`      // 事件类型
}

// GetParamOfFormContent 从FormContent JSON字符串中根据jsonPath提取指定字段的值
// 功能：使用sonic库的GetFromString方法解析JSON路径，支持多级嵌套路径查询
// 参数：
//   - jsonPath: JSON路径数组，如 ["borrowRecordId"] 或 ["data", "borrowReason"]
//
// 返回值：
//   - any: 提取到的值，可能是字符串、数字、布尔值、对象或数组
//   - error: 解析错误，当JSON格式无效或路径不存在时返回
//
// 示例：
//   - 获取顶级字段：{"borrowRecordId":"1"},GetParamOfFormContent(["borrowRecordId"])
//   - 获取嵌套字段：{"data":{"borrowReason":"1"}},GetParamOfFormContent(["data", "borrowReason"])
func (w *WorkflowEventMessage) GetParamOfFormContent(jsonPath []string) (any, error) {
	// 1. 检查路径是否为空
	if len(jsonPath) == 0 {
		return nil, fmt.Errorf("jsonPath不能为空")
	}

	// 2. 将字符串切片转换为interface{}切片
	paths := make([]any, len(jsonPath))
	for i, path := range jsonPath {
		paths[i] = path
	}

	// 3. 使用sonic库解析JSON路径，获取对应的AST节点
	node, err := sonic.GetFromString(w.FormContent, paths...)
	if err != nil {
		return nil, err
	}

	// 3. 将AST节点转换为Go原生类型并返回
	return node.Interface()
}
