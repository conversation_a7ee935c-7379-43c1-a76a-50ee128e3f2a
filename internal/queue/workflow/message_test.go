package workflow

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

// TestWorkflowEventMessage_GetParamOfFormContent 测试GetParamOfFormContent方法
func TestWorkflowEventMessage_GetParamOfFormContent(t *testing.T) {
	Convey("测试GetParamOfFormContent方法", t, func() {
		// 准备测试数据 - 复杂嵌套JSON结构
		complexJSON := `{
			"businessId": "FILE_BORROW",
			"borrowRecordId": "29794932326624",
			"data": {
				"borrowReason": "工作需要",
				"otherReason": "",
				"borrowPeriod": ["2024-01-01", "2024-01-31"],
				"nested": {
					"level2": {
						"level3": "深层嵌套值"
					}
				},
				"numberValue": 12345,
				"boolValue": true
			}
		}`

		msg := &WorkflowEventMessage{
			FormContent: complexJSON,
		}

		<PERSON><PERSON>("正常情况", func() {
			<PERSON>vey("获取顶级字符串字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"borrowRecordId"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, "29794932326624")
			})

			Convey("获取顶级业务ID字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"businessId"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, "FILE_BORROW")
			})

			Convey("获取嵌套字符串字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "borrowReason"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, "工作需要")
			})

			Convey("获取深层嵌套字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "nested", "level2", "level3"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, "深层嵌套值")
			})

			Convey("获取空字符串字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "otherReason"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, "")
			})

			Convey("获取数字字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "numberValue"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, 12345)
			})

			Convey("获取布尔字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "boolValue"})
				So(err, ShouldBeNil)
				So(result, ShouldEqual, true)
			})

			Convey("获取数组字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "borrowPeriod"})
				So(err, ShouldBeNil)
				So(result, ShouldNotBeNil)
				// 验证是数组类型
				if arr, ok := result.([]any); ok {
					So(len(arr), ShouldEqual, 2)
					So(arr[0], ShouldEqual, "2024-01-01")
					So(arr[1], ShouldEqual, "2024-01-31")
				}
			})

			Convey("获取对象字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data"})
				So(err, ShouldBeNil)
				So(result, ShouldNotBeNil)
				// 验证是对象类型
				if obj, ok := result.(map[string]any); ok {
					So(obj["borrowReason"], ShouldEqual, "工作需要")
					So(obj["numberValue"], ShouldEqual, 12345)
				}
			})
		})

		Convey("异常情况", func() {
			Convey("空FormContent", func() {
				emptyMsg := &WorkflowEventMessage{FormContent: ""}
				result, err := emptyMsg.GetParamOfFormContent([]string{"borrowRecordId"})
				So(err, ShouldNotBeNil)
				So(result, ShouldBeNil)
			})

			Convey("无效JSON格式", func() {
				invalidMsg := &WorkflowEventMessage{FormContent: "{invalid json}"}
				result, err := invalidMsg.GetParamOfFormContent([]string{"borrowRecordId"})
				So(err, ShouldNotBeNil)
				So(result, ShouldBeNil)
			})

			Convey("不存在的字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"nonExistentField"})
				So(err, ShouldNotBeNil)
				So(result, ShouldBeNil)
			})

			Convey("不存在的嵌套字段", func() {
				result, err := msg.GetParamOfFormContent([]string{"data", "nonExistentField"})
				So(err, ShouldNotBeNil)
				So(result, ShouldBeNil)
			})

			Convey("空路径数组", func() {
				result, err := msg.GetParamOfFormContent([]string{})
				So(err, ShouldNotBeNil)
				So(result, ShouldBeNil)
			})
		})
	})
}

// TestWorkflowEventMessage_GetParamOfFormContent_SimpleJSON 测试简单JSON结构
func TestWorkflowEventMessage_GetParamOfFormContent_SimpleJSON(t *testing.T) {
	Convey("测试简单JSON结构", t, func() {
		// 简单JSON结构
		simpleJSON := `{"borrowRecordId": "12345", "amount": 100}`
		msg := &WorkflowEventMessage{FormContent: simpleJSON}

		Convey("获取字符串值", func() {
			result, err := msg.GetParamOfFormContent([]string{"borrowRecordId"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, "12345")
		})

		Convey("获取数字值", func() {
			result, err := msg.GetParamOfFormContent([]string{"amount"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, 100)
		})
	})
}

// TestWorkflowEventMessage_GetParamOfFormContent_EdgeCases 测试边界情况
func TestWorkflowEventMessage_GetParamOfFormContent_EdgeCases(t *testing.T) {
	Convey("测试边界情况", t, func() {
		Convey("null值", func() {
			nullJSON := `{"borrowRecordId": null}`
			msg := &WorkflowEventMessage{FormContent: nullJSON}
			result, err := msg.GetParamOfFormContent([]string{"borrowRecordId"})
			So(err, ShouldBeNil)
			So(result, ShouldBeNil)
		})

		Convey("零值", func() {
			zeroJSON := `{"count": 0, "flag": false, "text": ""}`
			msg := &WorkflowEventMessage{FormContent: zeroJSON}

			// 测试数字零值
			result, err := msg.GetParamOfFormContent([]string{"count"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, 0)

			// 测试布尔false值
			result, err = msg.GetParamOfFormContent([]string{"flag"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, false)

			// 测试空字符串
			result, err = msg.GetParamOfFormContent([]string{"text"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, "")
		})

		Convey("特殊字符", func() {
			specialJSON := `{"special": "包含中文和特殊字符!@#$%^&*()"}`
			msg := &WorkflowEventMessage{FormContent: specialJSON}
			result, err := msg.GetParamOfFormContent([]string{"special"})
			So(err, ShouldBeNil)
			So(result, ShouldEqual, "包含中文和特殊字符!@#$%^&*()")
		})
	})
}

// TestGetParamOfFormContent_Standard 标准测试（不使用convey）
func TestGetParamOfFormContent_Standard(t *testing.T) {
	// 简单JSON结构
	simpleJSON := `{"borrowRecordId": "12345"}`
	msg := &WorkflowEventMessage{FormContent: simpleJSON}

	// 测试获取字符串值
	result, err := msg.GetParamOfFormContent([]string{"borrowRecordId"})
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if result != "12345" {
		t.Errorf("Expected '12345', got: %v", result)
	}

	// 测试错误情况
	emptyMsg := &WorkflowEventMessage{FormContent: ""}
	_, err = emptyMsg.GetParamOfFormContent([]string{"borrowRecordId"})
	if err == nil {
		t.Error("Expected error for empty FormContent, got nil")
	}
}