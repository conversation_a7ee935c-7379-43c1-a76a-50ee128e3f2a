package workflow

import (
	"context"
	"encoding/json"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"

	"github.com/zeromicro/go-zero/core/logc"
)

// BorrowApprovalRetrieveConsumer 借阅后的回收审批
type BorrowApprovalRetrieveConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewBorrowApprovalRetrieve(svcCtx *svc.ServiceContext) *BorrowApprovalRetrieveConsumer {
	return &BorrowApprovalRetrieveConsumer{
		svcCtx: svcCtx,
	}
}

func (h *BorrowApprovalRetrieveConsumer) Name() string {
	return "file_borrow_reclaim"
}

func (h *BorrowApprovalRetrieveConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleRetrieve(ctx, msg)
	return nil
}

// handleRetrieve 处理文件回收审批事件
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
//
// 功能: 根据不同的事件类型处理文件回收审批状态更新
func (h *BorrowApprovalRetrieveConsumer) handleRetrieve(ctx context.Context, msg WorkflowEventMessage) {
	// 实现步骤:
	// 1. 根据事件类型进行不同的处理
	// 2. 审批驳回时更新状态为已驳回
	// 3. 审批撤销时更新状态为待提交
	// 4. 审批通过时处理通过逻辑

	switch msg.EventType {
	case consts.WorkflowEventRejected:
		// 审批驳回，更新状态为已驳回
		h.handleRejected(ctx, msg)

	case consts.WorkflowEventCanceled:
		// 审批撤销，更新状态为待提交
		h.handleCanceled(ctx, msg)

	case consts.WorkflowEventPassed:
		// 审批通过
		h.handlePassed(ctx, msg)

	default:
		// 未知事件类型，记录日志
		logc.Errorf(ctx, "未知的工作流事件类型: %s", msg.EventType)
	}
}

// handleRejected 处理回收审批驳回事件
// 功能: 将借阅记录回收状态更新为已驳回
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *BorrowApprovalRetrieveConsumer) handleRejected(ctx context.Context, msg WorkflowEventMessage) {
	// 1. 调用docvault服务更新借阅记录回收状态为借阅中
	_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyBorrowDocumentStatus(ctx, &docvault.BorrowDocumentStatusModifyReq{
		BorrowStatus: 1, // 已驳回
	})
	if err != nil {
		logc.Errorf(ctx, "处理文件回收审批驳回失败: %v", err)
		return
	}
	logc.Infof(ctx, "文件回收记录 %s 审批驳回处理完成", msg.BusinessID)
}

// handleCanceled 处理回收审批撤销事件
// 功能: 将借阅记录回收状态更新为待审批
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *BorrowApprovalRetrieveConsumer) handleCanceled(ctx context.Context, msg WorkflowEventMessage) {
	// 1. 调用docvault服务更新借阅记录回收状态为借阅中
	_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyBorrowDocumentStatus(ctx, &docvault.BorrowDocumentStatusModifyReq{
		BorrowStatus: 1, // 借阅中
	})
	if err != nil {
		logc.Errorf(ctx, "处理文件回收审批撤销失败: %v", err)
		return
	}
	logc.Infof(ctx, "文件回收记录 %s 审批撤销处理完成", msg.BusinessID)
}

// handlePassed 处理回收审批通过事件
// 功能: 将借阅记录回收状态更新为已回收，并保存审批信息
// 参数:
//   - ctx: 上下文
//   - msg: 工作流事件消息
func (h *BorrowApprovalRetrieveConsumer) handlePassed(ctx context.Context, msg WorkflowEventMessage) {

	// 调用docvault服务更新借阅记录回收状态为已回收(3)
	_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).ModifyBorrowDocumentStatus(ctx, &docvault.BorrowDocumentStatusModifyReq{
		BorrowStatus: 3,
	})
	if err != nil {
		logc.Errorf(ctx, "处理文件回收审批通过失败: %v", err)
		return
	}
	logc.Infof(ctx, "文件回收记录 %s 审批通过处理完成", msg.BusinessID)
}
