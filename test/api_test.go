package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"testing"
)

// APIResponse 统一的API响应格式
type APIResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

// PageInfo 分页信息
type PageInfo struct {
	Page     uint64 `json:"page"`
	PageSize uint64 `json:"pageSize"`
	Total    uint64 `json:"total"`
}

// DocumentDistributeRecord 文档发放回收记录
type DocumentDistributeRecord struct {
	RecordID                   string `json:"recordId"`
	WorkflowID                 string `json:"workflowId"`
	ApplyTime                  int64  `json:"applyTime"`
	InternalElectronicQuery    string `json:"internalElectronicQuery"`
	InternalElectronicDownload string `json:"internalElectronicDownload"`
	Applicant                  string `json:"applicant"`
	ApplicantName              string `json:"applicantName"`
}

// InternalPaperDownloadRecord 内发纸质文件一次下载变更记录
type InternalPaperDownloadRecord struct {
	RecordID                string `json:"recordId"`
	WorkflowID              string `json:"workflowId"`
	RecipientUserID         string `json:"recipientUserId"`
	RecipientUserName       string `json:"recipientUserName"`
	DistributeApplicant     string `json:"distributeApplicant"`
	DistributeApplicantName string `json:"distributeApplicantName"`
	DistributeApplyTime     int64  `json:"distributeApplyTime"`
	RecycleApplicant        string `json:"recycleApplicant"`
	RecycleApplicantName    string `json:"recycleApplicantName"`
	RecycleApplyTime        int64  `json:"recycleApplyTime"`
	DisposalMethod          string `json:"disposalMethod"`
	InventoryID             string `json:"inventoryId"`
}

// ExternalElectronicDownloadRecord 外发电子文件一次下载变更记录
type ExternalElectronicDownloadRecord struct {
	RecordID                string `json:"recordId"`
	WorkflowID              string `json:"workflowId"`
	RecipientUserID         string `json:"recipientUserId"`
	RecipientUserName       string `json:"recipientUserName"`
	DistributeApplicant     string `json:"distributeApplicant"`
	DistributeApplicantName string `json:"distributeApplicantName"`
	DistributeApplyTime     int64  `json:"distributeApplyTime"`
	IsDownloaded            bool   `json:"isDownloaded"`
	InventoryID             string `json:"inventoryId"`
}

// DocumentDistributeRecordsResponse 文档发放回收记录响应
type DocumentDistributeRecordsResponse struct {
	Data     []DocumentDistributeRecord `json:"data"`
	PageInfo PageInfo                   `json:"pageInfo"`
}

// InternalPaperDownloadRecordsResponse 内发纸质文件下载记录响应
type InternalPaperDownloadRecordsResponse struct {
	Data     []InternalPaperDownloadRecord `json:"data"`
	PageInfo PageInfo                      `json:"pageInfo"`
}

// ExternalElectronicDownloadRecordsResponse 外发电子文件下载记录响应
type ExternalElectronicDownloadRecordsResponse struct {
	Data     []ExternalElectronicDownloadRecord `json:"data"`
	PageInfo PageInfo                           `json:"pageInfo"`
}

const (
	baseURL = "http://localhost:8888/nebula/api/v1"
)

// TestDocumentDistributeRecordsAPI 测试文档发放回收记录接口
func TestDocumentDistributeRecordsAPI(t *testing.T) {
	// 构建请求URL
	apiURL := fmt.Sprintf("%s/document-library/document/distribute-records", baseURL)
	params := url.Values{}
	params.Add("documentId", "test_doc_123")
	params.Add("page", "1")
	params.Add("pageSize", "10")

	fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())

	// 发送GET请求
	resp, err := http.Get(fullURL)
	if err != nil {
		t.Fatalf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		t.Fatalf("解析响应失败: %v", err)
	}

	// 验证响应格式
	if apiResp.Code != 200 {
		t.Errorf("期望状态码 200，实际 %d", apiResp.Code)
	}

	if apiResp.Msg != "success" {
		t.Errorf("期望消息 'success'，实际 '%s'", apiResp.Msg)
	}

	// 验证数据结构
	dataBytes, _ := json.Marshal(apiResp.Data)
	var dataResp DocumentDistributeRecordsResponse
	if err := json.Unmarshal(dataBytes, &dataResp); err != nil {
		t.Fatalf("解析数据失败: %v", err)
	}

	// 验证分页信息
	if dataResp.PageInfo.Page != 1 {
		t.Errorf("期望页码 1，实际 %d", dataResp.PageInfo.Page)
	}

	if dataResp.PageInfo.PageSize != 10 {
		t.Errorf("期望页大小 10，实际 %d", dataResp.PageInfo.PageSize)
	}

	t.Logf("文档发放回收记录接口测试通过，返回 %d 条记录", len(dataResp.Data))
}

// TestInternalPaperDownloadRecordsAPI 测试内发纸质文件下载记录接口
func TestInternalPaperDownloadRecordsAPI(t *testing.T) {
	// 构建请求URL
	apiURL := fmt.Sprintf("%s/document-library/document/internal-paper-download-records", baseURL)
	params := url.Values{}
	params.Add("documentId", "test_doc_123")
	params.Add("page", "1")
	params.Add("pageSize", "10")

	fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())

	// 发送GET请求
	resp, err := http.Get(fullURL)
	if err != nil {
		t.Fatalf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		t.Fatalf("解析响应失败: %v", err)
	}

	// 验证响应格式
	if apiResp.Code != 200 {
		t.Errorf("期望状态码 200，实际 %d", apiResp.Code)
	}

	// 验证数据结构
	dataBytes, _ := json.Marshal(apiResp.Data)
	var dataResp InternalPaperDownloadRecordsResponse
	if err := json.Unmarshal(dataBytes, &dataResp); err != nil {
		t.Fatalf("解析数据失败: %v", err)
	}

	t.Logf("内发纸质文件下载记录接口测试通过，返回 %d 条记录", len(dataResp.Data))
}

// TestExternalElectronicDownloadRecordsAPI 测试外发电子文件下载记录接口
func TestExternalElectronicDownloadRecordsAPI(t *testing.T) {
	// 构建请求URL
	apiURL := fmt.Sprintf("%s/document-library/document/external-electronic-download-records", baseURL)
	params := url.Values{}
	params.Add("documentId", "test_doc_123")
	params.Add("page", "1")
	params.Add("pageSize", "10")

	fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())

	// 发送GET请求
	resp, err := http.Get(fullURL)
	if err != nil {
		t.Fatalf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		t.Fatalf("解析响应失败: %v", err)
	}

	// 验证响应格式
	if apiResp.Code != 200 {
		t.Errorf("期望状态码 200，实际 %d", apiResp.Code)
	}

	// 验证数据结构
	dataBytes, _ := json.Marshal(apiResp.Data)
	var dataResp ExternalElectronicDownloadRecordsResponse
	if err := json.Unmarshal(dataBytes, &dataResp); err != nil {
		t.Fatalf("解析数据失败: %v", err)
	}

	// 验证 IsDownloaded 字段是布尔类型
	for _, record := range dataResp.Data {
		if record.IsDownloaded != true && record.IsDownloaded != false {
			t.Errorf("IsDownloaded 字段应该是布尔类型")
		}
	}

	t.Logf("外发电子文件下载记录接口测试通过，返回 %d 条记录", len(dataResp.Data))
}

// TestAllAPIsResponseFormat 测试所有接口的响应格式一致性
func TestAllAPIsResponseFormat(t *testing.T) {
	apis := []struct {
		name string
		path string
	}{
		{"文档发放回收记录", "/document-library/document/distribute-records"},
		{"内发纸质文件下载记录", "/document-library/document/internal-paper-download-records"},
		{"外发电子文件下载记录", "/document-library/document/external-electronic-download-records"},
	}

	for _, api := range apis {
		t.Run(api.name, func(t *testing.T) {
			// 构建请求URL
			apiURL := fmt.Sprintf("%s%s", baseURL, api.path)
			params := url.Values{}
			params.Add("documentId", "test_doc_123")
			params.Add("page", "1")
			params.Add("pageSize", "5")

			fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())

			// 发送GET请求
			resp, err := http.Get(fullURL)
			if err != nil {
				t.Fatalf("请求失败: %v", err)
			}
			defer resp.Body.Close()

			// 解析响应
			var apiResp APIResponse
			if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
				t.Fatalf("解析响应失败: %v", err)
			}

			// 验证统一响应格式
			if apiResp.Code == 0 {
				t.Errorf("%s: 缺少 code 字段", api.name)
			}

			if apiResp.Msg == "" {
				t.Errorf("%s: 缺少 msg 字段", api.name)
			}

			if apiResp.Data == nil {
				t.Errorf("%s: 缺少 data 字段", api.name)
			}

			t.Logf("%s: 响应格式验证通过", api.name)
		})
	}
}
